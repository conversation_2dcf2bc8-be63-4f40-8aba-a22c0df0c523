import React, { useEffect, useState } from 'react';

interface ScreenReaderAnnouncementsProps {
  message: string;
  priority?: 'polite' | 'assertive';
  clearAfter?: number; // milliseconds
}

export const ScreenReaderAnnouncements: React.FC<ScreenReaderAnnouncementsProps> = ({
  message,
  priority = 'polite',
  clearAfter = 3000
}) => {
  const [currentMessage, setCurrentMessage] = useState('');

  useEffect(() => {
    if (message) {
      setCurrentMessage(message);
      
      if (clearAfter > 0) {
        const timer = setTimeout(() => {
          setCurrentMessage('');
        }, clearAfter);
        
        return () => clearTimeout(timer);
      }
    }
  }, [message, clearAfter]);

  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
      role="status"
    >
      {currentMessage}
    </div>
  );
};

// Hook for managing screen reader announcements
export const useScreenReaderAnnouncements = () => {
  const [announcement, setAnnouncement] = useState('');
  const [priority, setPriority] = useState<'polite' | 'assertive'>('polite');

  const announce = (message: string, announcementPriority: 'polite' | 'assertive' = 'polite') => {
    setPriority(announcementPriority);
    setAnnouncement(message);
  };

  const AnnouncementComponent = () => (
    <ScreenReaderAnnouncements 
      message={announcement} 
      priority={priority}
    />
  );

  return { announce, AnnouncementComponent };
};
