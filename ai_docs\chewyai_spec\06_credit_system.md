# Phase 6: Credit System Backend
**Priority**: HIGH - Core monetization feature
**Dependencies**: Phase 1 (Foundation), Phase 2 (Database Schema), Phase 4 (Authentication System), Phase 5 (Frontend Foundation)
**Estimated Time**: 4-5 hours

## Overview
Implement comprehensive credit management system with atomic operations, transaction tracking, and integration with Stripe for credit purchases and subscription management. The system supports a student-focused business model with generous free tier and unlimited Pro subscriptions.

## Objectives
- Create credit management backend with atomic operations
- Implement pricing models and transaction tracking
- Build credit validation middleware for AI operations
- Establish subscription credit allocation system
- Create credit purchase and refund workflows

## Tasks

### 5.1 Credit Management Core System
**Objective**: Implement atomic credit operations with transaction safety

**Backend Implementation**:
```typescript
// backend/src/routes/credits.ts
interface CreditTransaction {
  id: string;
  user_id: string;
  amount: number;
  type: 'purchase' | 'usage' | 'refund' | 'subscription_allocation';
  description: string;
  reference_id?: string;
  created_at: Date;
}

interface CreditBalance {
  user_id: string;
  total_credits: number;
  subscription_credits: number;
  purchased_credits: number;
  last_updated: Date;
}
```

**API Endpoints**:
- `GET /api/credits/balance` - Get current credit balance
- `POST /api/credits/purchase` - Purchase credit packages
- `POST /api/credits/consume` - Consume credits for AI operations
- `GET /api/credits/transactions` - Get transaction history
- `POST /api/credits/refund` - Process credit refunds

### 5.2 Pricing Models & Credit Costs
**Objective**: Define credit costs for different AI operations

**Credit Cost Structure**:
- **Flashcard Generation**: 1 credit per 5 flashcards
- **Quiz Generation**: 1 credit per 5 quiz questions
- **Additional Content**: 1 credit per 5 content additions
- **Consistent 1:5 Ratio**: All AI generation operations follow the same pricing model

**Subscription Credit Allocation**:
- **Study Starter (Free)**: 500 AI generations/month
- **Scholar Pro**: Unlimited AI generations ($9.99/month)
- **Academic Year Pass**: Unlimited AI generations ($99.99/year)

**À la Carte Packages**:
- Study Buddy: 100 credits for $9.99 (Perfect for high school & early college students)
- Dean's List: 500 credits + 50 bonus for $39.99 (Most popular for serious undergrads & grad students)
- Academic Legend: 2,500 credits + 750 bonus for $149.99 (Ultimate package for study groups & academic overachievers)

### 5.3 Credit Validation Middleware
**Objective**: Ensure sufficient credits before AI operations

**Middleware Implementation**:
```typescript
// backend/src/middleware/creditValidation.ts
export const validateCredits = (requiredCredits: number) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user.id;
    const balance = await getCreditBalance(userId);
    
    if (balance.total_credits < requiredCredits) {
      return res.status(402).json({
        error: 'Insufficient credits',
        required: requiredCredits,
        available: balance.total_credits
      });
    }
    
    req.creditCost = requiredCredits;
    next();
  };
};
```

### 5.4 Transaction Management
**Objective**: Atomic credit operations with full audit trail

**Database Operations**:
```sql
-- Atomic credit consumption
BEGIN;
  UPDATE user_credits 
  SET total_credits = total_credits - $1,
      last_updated = NOW()
  WHERE user_id = $2 AND total_credits >= $1;
  
  INSERT INTO credit_transactions (user_id, amount, type, description)
  VALUES ($2, -$1, 'usage', $3);
COMMIT;
```

**Transaction Types**:
- **Purchase**: Credit package purchases
- **Usage**: AI generation consumption
- **Refund**: Failed generation refunds
- **Subscription**: Monthly credit allocation
- **Bonus**: Promotional credit awards

### 5.5 Subscription Credit Management
**Objective**: Monthly credit allocation and rollover policies

**Subscription Logic**:
- Credits allocated on subscription anniversary
- Unused subscription credits expire monthly
- Purchased credits never expire
- Credit consumption prioritizes expiring credits first

**Implementation**:
```typescript
// Credit allocation on subscription renewal
export const allocateSubscriptionCredits = async (userId: string, tier: SubscriptionTier) => {
  const creditAmount = TIER_CREDITS[tier];
  
  await db.transaction(async (trx) => {
    // Reset subscription credits
    await trx('user_credits')
      .where('user_id', userId)
      .update({
        subscription_credits: creditAmount,
        last_subscription_renewal: new Date()
      });
    
    // Record transaction
    await trx('credit_transactions').insert({
      user_id: userId,
      amount: creditAmount,
      type: 'subscription_allocation',
      description: `${tier} tier monthly allocation`
    });
  });
};
```

## Implementation Files

### Backend Routes
- `backend/src/routes/credits.ts` - Credit management endpoints
- `backend/src/routes/transactions.ts` - Transaction history and reporting

### Services
- `backend/src/services/creditService.ts` - Core credit operations
- `backend/src/services/pricingService.ts` - Pricing calculations and validation

### Middleware
- `backend/src/middleware/creditValidation.ts` - Credit validation for AI operations
- `backend/src/middleware/subscriptionCheck.ts` - Subscription status validation

### Database
- `backend/src/database/migrations/005_credit_system.sql` - Credit tables and indexes
- `backend/src/database/triggers/credit_audit.sql` - Audit trail triggers

### Types
- `shared/types/credits.ts` - Credit-related TypeScript interfaces
- `shared/types/transactions.ts` - Transaction type definitions

## Success Criteria
- ✅ Credit balance accurately tracked with atomic operations
- ✅ All AI operations properly validate and consume credits
- ✅ Transaction history provides complete audit trail
- ✅ Subscription credits allocated correctly on renewal
- ✅ Credit purchase workflow integrated with Stripe
- ✅ Refund system handles failed AI generations
- ✅ Pricing models correctly implemented and enforced
- ✅ Credit validation prevents unauthorized usage

## Integration Points
- **Phase 6**: Stripe integration for credit purchases
- **Phase 7**: AI generation endpoints consume credits
- **Phase 8**: Study set creation tracks credit usage
- **Phase 10**: Frontend displays credit costs and balances

## Notes
This credit system forms the foundation of the ChewyAI student-focused monetization model. The system balances generous free access with sustainable monetization through unlimited Pro subscriptions and academic-themed credit packages. All AI-powered features integrate with this system to ensure proper usage tracking and billing.
