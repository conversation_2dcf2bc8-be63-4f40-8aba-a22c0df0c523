import React, { useState } from 'react';
import { DocumentMetadata } from '../../../../shared/types';
import { useDocumentStore } from '../../stores/documentStore';
import { Button } from '../common/Button';
import { useDialog } from '../../contexts/DialogContext';

interface DocumentCardProps {
  document: DocumentMetadata;
}

export const DocumentCard: React.FC<DocumentCardProps> = ({ document }) => {
  const { selectedDocuments, toggleDocumentSelection, deleteDocument } = useDocumentStore();
  const { confirm, alert } = useDialog();
  const [isDeleting, setIsDeleting] = useState(false);

  const isSelected = selectedDocuments.has(document.id);

  const handleDelete = async () => {
    const confirmDelete = await confirm({
      title: 'Delete Document',
      message: `Are you sure you want to delete "${document.filename}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      variant: 'danger'
    });

    if (!confirmDelete) return;

    setIsDeleting(true);
    try {
      await deleteDocument(document.id);
    } catch (error) {
      console.error('Delete error:', error);
      await alert({
        title: 'Delete Error',
        message: 'Failed to delete document. Please try again.',
        variant: 'error'
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFileIcon = (fileType: string) => {
    const icons: Record<string, string> = {
      pdf: '📄',
      docx: '📝',
      txt: '📃',
      pptx: '📊'
    };
    return icons[fileType] || '📄';
  };

  return (
    <div
      className={`
        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer
        ${isSelected 
          ? 'border-primary-500 bg-primary-500/10' 
          : 'border-gray-700 hover:border-gray-600'
        }
      `}
      onClick={() => toggleDocumentSelection(document.id)}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <span className="text-2xl">{getFileIcon(document.file_type)}</span>
          <div className="min-w-0 flex-1">
            <h3 className="text-white font-medium truncate" title={document.filename}>
              {document.filename}
            </h3>
            <p className="text-sm text-gray-400">
              {document.file_type.toUpperCase()} • {formatFileSize(document.file_size)}
            </p>
          </div>
        </div>

        {/* Selection Indicator */}
        <div
          className={`
            w-5 h-5 rounded border-2 flex items-center justify-center
            ${isSelected 
              ? 'bg-primary-500 border-primary-500' 
              : 'border-gray-500'
            }
          `}
        >
          {isSelected && (
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </div>
      </div>

      {/* Processing Status */}
      {!document.is_processed && (
        <div className="mb-3">
          <div className="flex items-center space-x-2 text-yellow-400">
            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
            <span className="text-sm">Processing...</span>
          </div>
        </div>
      )}

      {document.processing_error && (
        <div className="mb-3">
          <div className="text-red-400 text-sm">
            ⚠️ Processing failed: {document.processing_error}
          </div>
        </div>
      )}

      {/* Upload Date */}
      <div className="text-xs text-gray-500 mb-3">
        Uploaded {formatDate(document.uploaded_at)}
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-2" onClick={(e) => e.stopPropagation()}>
        <Button
          onClick={handleDelete}
          variant="danger"
          size="sm"
          isLoading={isDeleting}
        >
          Delete
        </Button>
      </div>
    </div>
  );
};
