import React, { useState } from 'react';
import {
  HiDownload,
  HiExclamation<PERSON>ircle,
  HiCheckCircle,
  HiTrash,
  HiRefresh
} from 'react-icons/hi';
import { Button } from '../common/Button';

interface ExportData {
  studySets: boolean;
  flashcards: boolean;
  quizzes: boolean;
  analytics: boolean;
  preferences: boolean;
}

interface DataStats {
  studySets: number;
  flashcards: number;
  quizzes: number;
  documents: number;
  totalSize: string;
}

export const DataManagement: React.FC = () => {
  const [isExporting, setIsExporting] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [dataStats, setDataStats] = useState<DataStats>({
    studySets: 12,
    flashcards: 245,
    quizzes: 18,
    documents: 8,
    totalSize: '2.4 MB'
  });
  
  const [exportData, setExportData] = useState<ExportData>({
    studySets: true,
    flashcards: true,
    quizzes: true,
    analytics: true,
    preferences: true
  });

  const handleExportData = async () => {
    setIsExporting(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/data/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ exportData }),
      });

      if (!response.ok) {
        throw new Error('Failed to export data');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chewyai-data-export-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      window.URL.revokeObjectURL(url);

      setSuccess('Data exported successfully!');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export data');
    } finally {
      setIsExporting(false);
    }
  };



  const handleClearData = async (dataType: string) => {
    if (!confirm(`Are you sure you want to clear all ${dataType}? This action cannot be undone.`)) {
      return;
    }

    setIsClearing(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/data/clear/${dataType}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to clear ${dataType}`);
      }

      const result = await response.json();
      if (result.success) {
        setSuccess(`${dataType} cleared successfully!`);
        await fetchDataStats();
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to clear ${dataType}`);
    } finally {
      setIsClearing(false);
    }
  };

  const fetchDataStats = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/data/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setDataStats(result.data);
        }
      }
    } catch (err) {
      // Silently fail for stats refresh
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Data Management</h3>
        
        {error && (
          <div className="mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiExclamationCircle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">Error</span>
            </div>
            <p className="text-red-300 mt-1">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiCheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-medium">Success</span>
            </div>
            <p className="text-green-300 mt-1">{success}</p>
          </div>
        )}

        {/* Data Overview */}
        <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-white">Your Data Overview</h4>
            <Button
              onClick={fetchDataStats}
              variant="secondary"
              size="sm"
            >
              <HiRefresh className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-400">{dataStats.studySets}</div>
              <div className="text-sm text-gray-400">Study Sets</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-400">{dataStats.flashcards}</div>
              <div className="text-sm text-gray-400">Flashcards</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-400">{dataStats.quizzes}</div>
              <div className="text-sm text-gray-400">Quizzes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-400">{dataStats.documents}</div>
              <div className="text-sm text-gray-400">Documents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-400">{dataStats.totalSize}</div>
              <div className="text-sm text-gray-400">Total Size</div>
            </div>
          </div>
        </div>

        {/* Export Data */}
        <div className="bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6">
          <div className="flex items-center space-x-3 mb-4">
            <HiDownload className="w-6 h-6 text-blue-400" />
            <h4 className="text-lg font-medium text-white">Export Your Data</h4>
          </div>
          
          <p className="text-gray-400 text-sm mb-4">
            Download a copy of your data in JSON format. You can use this to backup your data or import it into another account.
          </p>

          <div className="space-y-3 mb-6">
            <h5 className="font-medium text-white">Select data to export:</h5>
            {Object.entries(exportData).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between">
                <label className="text-sm text-gray-300 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </label>
                <button
                  onClick={() => setExportData({ ...exportData, [key]: !value })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    value ? 'bg-primary-500' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      value ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>

          <Button
            onClick={handleExportData}
            isLoading={isExporting}
            variant="primary"
          >
            <HiDownload className="w-4 h-4 mr-2" />
            Export Data
          </Button>
        </div>



        {/* Clear Data */}
        <div className="bg-red-500/10 rounded-lg p-6 border border-red-500/30">
          <div className="flex items-center space-x-3 mb-4">
            <HiTrash className="w-6 h-6 text-red-400" />
            <h4 className="text-lg font-medium text-white">Clear Data</h4>
          </div>
          
          <p className="text-gray-400 text-sm mb-6">
            Permanently delete specific types of data from your account. This action cannot be undone.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <Button
                onClick={() => handleClearData('study-sets')}
                isLoading={isClearing}
                variant="danger"
                size="sm"
                className="w-full"
              >
                Clear All Study Sets
              </Button>
              <Button
                onClick={() => handleClearData('flashcards')}
                isLoading={isClearing}
                variant="danger"
                size="sm"
                className="w-full"
              >
                Clear All Flashcards
              </Button>
            </div>
            <div className="space-y-3">
              <Button
                onClick={() => handleClearData('quizzes')}
                isLoading={isClearing}
                variant="danger"
                size="sm"
                className="w-full"
              >
                Clear All Quizzes
              </Button>
              <Button
                onClick={() => handleClearData('analytics')}
                isLoading={isClearing}
                variant="danger"
                size="sm"
                className="w-full"
              >
                Clear Analytics Data
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
