import React, { memo, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { DocumentMetadata } from '../../../../shared/types';
import { DocumentCard } from './DocumentCard';

interface VirtualizedDocumentListProps {
  documents: DocumentMetadata[];
  height?: number;
  itemHeight?: number;
}

interface DocumentItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    documents: DocumentMetadata[];
  };
}

const DocumentItem: React.FC<DocumentItemProps> = memo(({ index, style, data }) => {
  const { documents } = data;
  const document = documents[index];

  return (
    <div style={style}>
      <div className="px-2 py-1">
        <DocumentCard
          document={document}
        />
      </div>
    </div>
  );
});

DocumentItem.displayName = 'DocumentItem';

export const VirtualizedDocumentList: React.FC<VirtualizedDocumentListProps> = memo(({
  documents,
  height = 400,
  itemHeight = 120
}) => {
  const itemData = useMemo(() => ({
    documents
  }), [documents]);

  if (documents.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">No documents uploaded yet</div>
        <p className="text-sm text-gray-500">
          Upload your first document to get started with AI-powered study materials.
        </p>
      </div>
    );
  }

  // For small lists, use regular rendering to avoid virtualization overhead
  if (documents.length <= 10) {
    return (
      <div className="space-y-4">
        {documents.map((document) => (
          <DocumentCard
            key={document.id}
            document={document}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="border border-gray-600 rounded-lg overflow-hidden">
      <List
        height={height}
        width="100%"
        itemCount={documents.length}
        itemSize={itemHeight}
        itemData={itemData}
        overscanCount={5}
      >
        {DocumentItem}
      </List>
    </div>
  );
});

VirtualizedDocumentList.displayName = 'VirtualizedDocumentList';
