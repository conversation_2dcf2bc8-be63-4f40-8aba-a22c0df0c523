{"version": 3, "file": "static/js/30.50d8cfa6.chunk.js", "mappings": "qKAWA,MAAMA,EAAoB,CACxB,CACEC,MAAOC,EAAAA,GAAgBC,KACvBC,MAAO,OACPC,YAAa,+BAEf,CACEJ,MAAOC,EAAAA,GAAgBI,OACvBF,MAAO,SACPC,YAAa,mCAEf,CACEJ,MAAOC,EAAAA,GAAgBK,KACvBH,MAAO,OACPC,YAAa,uCAEf,CACEJ,MAAOC,EAAAA,GAAgBM,QACvBJ,MAAO,UACPC,YAAa,kCAEf,CACEJ,MAAOC,EAAAA,GAAgBO,SACvBL,MAAO,WACPC,YAAa,2BAEf,CACEJ,MAAOC,EAAAA,GAAgBQ,IACvBN,MAAO,MACPC,YAAa,6BA0CJM,EAAwDC,IAM9D,IAN+D,MACpEX,EAAK,SACLY,EAAQ,UACRC,EAAY,GAAE,SACdC,GAAW,EAAK,MAChBX,EAAQ,oBACTQ,EACC,OACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAS,aAAAG,OAAeH,GAAYI,SAAA,EACvCC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,8CAA6CI,SAC3Dd,KAEHe,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wCAAuCI,SACnDlB,EAAkBoB,IAAKC,IACtB,MAAMC,EAAarB,IAAUoB,EAAOpB,MAC9BsB,EAAeD,EAlCKE,KAClC,OAAQA,GACN,KAAKtB,EAAAA,GAAgBC,KACnB,MAAO,gFACT,KAAKD,EAAAA,GAAgBI,OACnB,MAAO,wFACT,KAAKJ,EAAAA,GAAgBK,KACnB,MAAO,oFACT,KAAKL,EAAAA,GAAgBM,QACnB,MAAO,oFACT,KAAKN,EAAAA,GAAgBO,SACnB,MAAO,wEACT,KAAKP,EAAAA,GAAgBQ,IACnB,MAAO,4EACT,QACE,MAAO,0FAoBCe,CAA2BJ,EAAOpB,OAtDpBuB,KAC1B,OAAQA,GACN,KAAKtB,EAAAA,GAAgBC,KACnB,MAAO,gHACT,KAAKD,EAAAA,GAAgBI,OACnB,MAAO,sHACT,KAAKJ,EAAAA,GAAgBK,KACnB,MAAO,mHACT,KAAKL,EAAAA,GAAgBM,QACnB,MAAO,mHACT,KAAKN,EAAAA,GAAgBO,SACnB,MAAO,0GACT,KAAKP,EAAAA,GAAgBQ,IACnB,MAAO,6GACT,QACE,MAAO,wHAwCCgB,CAAmBL,EAAOpB,OAE9B,OACEe,EAAAA,EAAAA,MAAA,UAEEW,KAAK,SACLC,QAASA,KAAOb,GAAYF,EAASQ,EAAOpB,OAC5Cc,SAAUA,EACVD,UAAS,qIAAAG,OAELM,EAAY,sBAAAN,OAEZF,EACI,gCACA,iCAAgC,sBAAAE,OAGpCK,EACI,uEACA,GAAE,qJAIVO,MAAOR,EAAOhB,YACd,eAAciB,EAAWJ,SAAA,EAEzBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,SAAEG,EAAOjB,SACvCe,EAAAA,EAAAA,KAAA,OACEL,UAAS,gBAAAG,OACPK,EAAa,gBAAkB,uBAC9BJ,SAEFG,EAAOhB,iBAGXiB,IACCH,EAAAA,EAAAA,KAAA,OAAKL,UAAU,yBAAwBI,UACrCC,EAAAA,EAAAA,KAAA,OACEL,UAAU,UACVgB,KAAK,eACLC,QAAQ,YAAWb,UAEnBC,EAAAA,EAAAA,KAAA,QACEa,SAAS,UACTC,EAAE,qHACFC,SAAS,kBA1CZb,EAAOpB,YAmDpBkB,EAAAA,EAAAA,KAAA,KAAGL,UAAU,0BAAyBI,SAAC,oI,sIC9H7C,MAAMiB,EAAwC,CAC5C,CACEC,GAAI,OACJC,KAAM,gBACNC,MAAO,EACPC,SAAU,QACVC,SAAU,CACR,qCACA,+BACA,2BACA,wBACA,mCAGJ,CACEJ,GAAI,cACJC,KAAM,cACNC,MAAO,KACPC,SAAU,QACVE,SAAS,EACTD,SAAU,CACR,iCACA,2CACA,6BACA,8BACA,iCACA,yBACA,iCAGJ,CACEJ,GAAI,aACJC,KAAM,qBACNC,MAAO,MACPC,SAAU,OACVC,SAAU,CACR,iCACA,2CACA,6BACA,8BACA,iCACA,yBACA,uCACA,wCAKOE,EAAmCA,KAAO,IAADC,EAAAC,EACpD,MAAOC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAkC,OAC3EC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAYC,IAAiBJ,EAAAA,EAAAA,WAAS,IACtCK,EAAOC,IAAYN,EAAAA,EAAAA,UAAwB,OAElDO,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAAMA,EAAwBC,UAC5BP,GAAa,GACbI,EAAS,MAET,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,oBAAqB,CAChDC,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,MAI/B,IAAKG,EAASG,GACZ,MAAM,IAAIC,MAAM,qCAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,OAFvBN,EAAoBmB,EAAOG,KAI/B,CAAE,MAAOC,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,oCAE9CxB,EAAoB,CAClByB,YAAapC,EAAkB,GAC/BqC,OAAQ,SACRC,gBAAiB,IAAIC,KAAKA,KAAKC,MAAQ,QAA0BC,eAErE,CAAC,QACC3B,GAAa,EACf,GAmGF,OAAID,GAEA7B,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeI,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,uCACfK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wCAAuCI,SACnD,CAAC,EAAG,EAAG,GAAGE,IAAKyD,IACd1D,EAAAA,EAAAA,KAAA,OAAaL,UAAU,+BAAb+D,YASpB1D,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,4BAErDkC,IACCpC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DI,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,2BAA0BI,SAAC,cAE7CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oBAAmBI,SAAEkC,OAKrCP,IACC7B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,kBAC/CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,gBAAeI,UACG,QAA5ByB,EAAAE,EAAiB0B,mBAAW,IAAA5B,OAAA,EAA5BA,EAA8BN,OAAQ,uBAG3ClB,EAAAA,EAAAA,KAAA,OAAKL,UAAU,aAAYI,UACzBC,EAAAA,EAAAA,KAAA,OAAKL,UAAS,uEAAAG,OACgB,WAA5B4B,EAAiB2B,OAAsB,iCACX,aAA5B3B,EAAiB2B,OAAwB,6BACb,aAA5B3B,EAAiB2B,OAAwB,mCACzC,gCACCtD,SACA2B,EAAiB2B,OAAOO,OAAO,GAAGC,cAAgBnC,EAAiB2B,OAAOS,MAAM,UAKtFpC,EAAiB4B,kBAChBzD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDI,SAAA,EAChEC,EAAAA,EAAAA,KAAC+D,EAAAA,IAAmB,CAACpE,UAAU,aAC/BK,EAAAA,EAAAA,KAAA,QAAAD,SACG2B,EAAiBsC,kBAAiB,kBAAAlE,OACb,IAAIyD,KAAK7B,EAAiB4B,iBAAiBW,sBAAoB,sBAAAnE,OAC3D,IAAIyD,KAAK7B,EAAiB4B,iBAAiBW,2BAMrC,UAAT,QAA5BxC,EAAAC,EAAiB0B,mBAAW,IAAA3B,OAAA,EAA5BA,EAA8BR,MAC7BpB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,CACjC2B,EAAiBsC,mBAChBhE,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAlGmB4B,UACnCL,GAAc,GACdE,EAAS,MAET,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,+BAAgC,CAC3DyB,OAAQ,OACRxB,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,MAI/B,IAAKG,EAASG,GACZ,MAAM,IAAIC,MAAM,qCAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,aAFjBG,GAIV,CAAE,MAAOc,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,oCAChD,CAAC,QACCnB,GAAc,EAChB,GAwEgBH,UAAWE,EACXqC,QAAQ,UACRC,KAAK,KAAItE,SACV,6BAIDC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QA7Ie4B,UAC/B,GAAKiC,QAAQ,8HAAb,CAIAtC,GAAc,GACdE,EAAS,MAET,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,2BAA4B,CACvDyB,OAAQ,OACRxB,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,MAI/B,IAAKG,EAASG,GACZ,MAAM,IAAIC,MAAM,iCAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,aAFjBG,GAIV,CAAE,MAAOc,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,gCAChD,CAAC,QACCnB,GAAc,EAChB,CA5BA,GA2IgBH,UAAWE,EACXqC,QAAQ,SACRC,KAAK,KAAItE,SACV,yBAIHF,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CACLzD,QAAS2B,EACTgC,QAAQ,YACRC,KAAK,KAAItE,SAAA,EAETC,EAAAA,EAAAA,KAACuE,EAAAA,IAAS,CAAC5E,UAAU,iBAAiB,oBAShDE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,qBACpDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wCAAuCI,SACnDiB,EAAkBf,IAAKuE,IAAU,IAADC,EAC/B,MAAMC,GAA4B,OAAhBhD,QAAgB,IAAhBA,GAA6B,QAAb+C,EAAhB/C,EAAkB0B,mBAAW,IAAAqB,OAAb,EAAhBA,EAA+BxD,MAAOuD,EAAKvD,GAE7D,OACEpB,EAAAA,EAAAA,MAAC8E,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBvF,UAAS,sFAAAG,OACP0E,EAAKlD,QACD,gDACAoD,EACE,4CACA,+CACL3E,SAAA,CAEFyE,EAAKlD,UACJtB,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sDAAqDI,UAClEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mGAAkGI,SAAA,EAC/GC,EAAAA,EAAAA,KAACmF,EAAAA,IAAM,CAACxF,UAAU,aAClBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,sBAKX2E,IACC1E,EAAAA,EAAAA,KAAA,OAAKL,UAAU,0BAAyBI,UACtCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGI,SAAA,EAC7GC,EAAAA,EAAAA,KAACoF,EAAAA,IAAO,CAACzF,UAAU,aACnBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBAKZF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBI,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAEyE,EAAKtD,QAC5DrB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BI,SAAA,CAAC,IAC3CyE,EAAKrD,OACPtB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,wBAAuBI,SAAA,CAAC,IAAEyE,EAAKpD,mBAInDpB,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iBAAgBI,SAC3ByE,EAAKnD,SAASpB,IAAI,CAACoF,EAASC,KAC3BzF,EAAAA,EAAAA,MAAA,MAAgBF,UAAU,8BAA6BI,SAAA,EACrDC,EAAAA,EAAAA,KAACoF,EAAAA,IAAO,CAACzF,UAAU,0CACnBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,wBAAuBI,SAAEsF,MAFlCC,OAObtF,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IA3PF4B,WACvBL,GAAc,GACdE,EAAS,MAET,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,2BAA4B,CACvDyB,OAAQ,OACRxB,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,GAC3B,eAAgB,oBAElBiD,KAAMC,KAAKC,UAAU,CAAEC,aAGzB,IAAKjD,EAASG,GACZ,MAAM,IAAIC,MAAM,sCAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,aAFjBG,GAIV,CAAE,MAAOc,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,qCAChD,CAAC,QACCnB,GAAc,EAChB,GA8N+B2D,CAAiBnB,EAAKvD,IACrCrB,SAAU8E,GAAa3C,EACvBF,UAAWE,EACXqC,QAASI,EAAKlD,QAAU,UAAY,YACpC3B,UAAU,SAAQI,SAEjB2E,EAAY,eAAc,aAAA5E,OAAgB0E,EAAKtD,UAtD7CsD,EAAKvD,iBC1Sf2E,EAA2BA,KACtC,MAAOC,EAAaC,IAAkBlE,EAAAA,EAAAA,WAAS,IACxCmE,EAAYC,IAAiBpE,EAAAA,EAAAA,WAAS,IACtCK,EAAOC,IAAYN,EAAAA,EAAAA,UAAwB,OAC3CoB,EAASiD,IAAcrE,EAAAA,EAAAA,UAAwB,OAC/CsE,EAAWC,IAAgBvE,EAAAA,EAAAA,UAAoB,CACpDwE,UAAW,GACXC,WAAY,IACZC,QAAS,GACTC,UAAW,EACXC,UAAW,YAGNC,EAAYC,IAAiB9E,EAAAA,EAAAA,UAAqB,CACvDwE,WAAW,EACXC,YAAY,EACZC,SAAS,EACTK,WAAW,EACXC,aAAa,IAyCTC,EAAkBxE,UACtB,GAAKiC,QAAQ,sCAADxE,OAAuCgH,EAAQ,oCAA3D,CAIAd,GAAc,GACd9D,EAAS,MACT+D,EAAW,MAEX,IACE,MAAM3D,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,mBAAD5C,OAAoBgH,GAAY,CAC1D3C,OAAQ,SACRxB,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,MAI/B,IAAKG,EAASG,GACZ,MAAM,IAAIC,MAAM,mBAAD/C,OAAoBgH,IAGrC,MAAMhE,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAIT,MAAM,IAAIH,MAAMC,EAAOb,OAHvBgE,EAAW,GAADnG,OAAIgH,EAAQ,iCAChBC,GAIV,CAAE,MAAO7D,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAO,mBAAArD,OAAsBgH,GACnE,CAAC,QACCd,GAAc,EAChB,CA9BA,GAiCIe,EAAiB1E,UACrB,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAAmB,CAC9CC,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,MAI/B,GAAIG,EAASG,GAAI,CACf,MAAME,QAAeL,EAASM,OAC1BD,EAAOE,SACTmD,EAAarD,EAAOG,KAExB,CACF,CAAE,MAAOC,GACP,GAIJ,OACElD,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,oBAErDkC,IACCpC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DI,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,2BAA0BI,SAAC,cAE7CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oBAAmBI,SAAEkC,OAIrCe,IACCnD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iEAAgEI,SAAA,EAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACgH,EAAAA,IAAa,CAACrH,UAAU,4BACzBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BI,SAAC,gBAE/CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,sBAAqBI,SAAEiD,QAKxCnD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,wBAC/CF,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CACLzD,QAASsG,EACT3C,QAAQ,YACRC,KAAK,KAAItE,SAAA,EAETC,EAAAA,EAAAA,KAACuE,EAAAA,IAAS,CAAC5E,UAAU,iBAAiB,iBAK1CE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAEmG,EAAUE,aAChEpG,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,mBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAEmG,EAAUG,cAChErG,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,mBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAEmG,EAAUI,WAChEtG,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,gBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAEmG,EAAUK,aAChEvG,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,kBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sCAAqCI,SAAEmG,EAAUM,aAChExG,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wBAAuBI,SAAC,yBAM7CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAACiH,EAAAA,IAAU,CAACtH,UAAU,2BACtBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,yBAGjDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,0HAI1CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,2BACtCmH,OAAOC,QAAQV,GAAYxG,IAAIR,IAAA,IAAE2H,EAAKtI,GAAMW,EAAA,OAC3CI,EAAAA,EAAAA,MAAA,OAAeF,UAAU,oCAAmCI,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,mCAAkCI,SAChDqH,EAAIC,QAAQ,WAAY,OAAOC,UAElCtH,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAMiG,GAAaa,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAId,GAAU,IAAE,CAACW,IAAOtI,KACtDa,UAAS,6EAAAG,OACPhB,EAAQ,iBAAmB,eAC1BiB,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACPhB,EAAQ,gBAAkB,uBAZxBsI,SAoBdvH,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CACLzD,QAhMe4B,UACvByD,GAAe,GACf5D,EAAS,MACT+D,EAAW,MAEX,IACE,MAAM3D,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,mBAAoB,CAC/CyB,OAAQ,OACRxB,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,GAC3B,eAAgB,oBAElBiD,KAAMC,KAAKC,UAAU,CAAEgB,iBAGzB,IAAKhE,EAASG,GACZ,MAAM,IAAIC,MAAM,yBAGlB,MAAM2E,QAAa/E,EAAS+E,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,uBAAAnI,QAA0B,IAAIyD,MAAOE,cAAcyE,MAAM,KAAK,GAAE,SAC1EL,EAAEM,QACFT,OAAOC,IAAIS,gBAAgBX,GAE3BxB,EAAW,8BACb,CAAE,MAAO/C,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,wBAChD,CAAC,QACC2C,GAAe,EACjB,GAgKQjE,UAAWgE,EACXzB,QAAQ,UAASrE,SAAA,EAEjBC,EAAAA,EAAAA,KAACiH,EAAAA,IAAU,CAACtH,UAAU,iBAAiB,qBAQ3CE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wDAAuDI,SAAA,EACpEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAACqI,EAAAA,IAAO,CAAC1I,UAAU,0BACnBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,mBAGjDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,gGAI1CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAMoG,EAAgB,cAC/BhF,UAAWkE,EACX3B,QAAQ,SACRC,KAAK,KACL1E,UAAU,SAAQI,SACnB,0BAGDC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAMoG,EAAgB,cAC/BhF,UAAWkE,EACX3B,QAAQ,SACRC,KAAK,KACL1E,UAAU,SAAQI,SACnB,6BAIHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAMoG,EAAgB,WAC/BhF,UAAWkE,EACX3B,QAAQ,SACRC,KAAK,KACL1E,UAAU,SAAQI,SACnB,uBAGDC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAMoG,EAAgB,aAC/BhF,UAAWkE,EACX3B,QAAQ,SACRC,KAAK,KACL1E,UAAU,SAAQI,SACnB,wCC7PFuI,EAA4BA,KACvC,MAAOC,EAAaC,IAAkB5G,EAAAA,EAAAA,UAA6B,OAC5DC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpC6G,EAAYC,IAAiB9G,EAAAA,EAAAA,WAAS,IACtCK,EAAOC,IAAYN,EAAAA,EAAAA,UAAwB,OAElDO,EAAAA,EAAAA,WAAU,KACRwG,KACC,IAEH,MAAMA,EAAmBtG,UACvBP,GAAa,GACbI,EAAS,MAET,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,eAAgB,CAC3CC,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,MAI/B,IAAKG,EAASG,GACZ,MAAM,IAAIC,MAAM,gCAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,OAFvBuG,EAAe1F,EAAOG,KAI1B,CAAE,MAAOC,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,+BAE9CqF,EAAe,CACbI,eAAgB,CACd,CACE3H,GAAI,IACJT,KAAM,OACNqI,MAAO,OACPC,MAAO,OACPC,YAAa,GACbC,WAAY,KACZC,WAAW,IAGfC,SAAU,CACR,CACEjI,GAAI,IACJkI,OAAQ,UACRC,OAAQ,KACRC,SAAU,MACVhG,OAAQ,OACRiG,KAAM,IAAI/F,KAAKA,KAAKC,MAAQ,QAA0BC,cACtDvE,YAAa,sBAEf,CACE+B,GAAI,IACJkI,OAAQ,UACRC,OAAQ,KACRC,SAAU,MACVhG,OAAQ,UACRiG,MAAM,IAAI/F,MAAOE,cACjB8F,QAAS,IAAIhG,KAAKA,KAAKC,MAAQ,QAAyBC,cACxDvE,YAAa,uBAGjBsK,YAAa,CACXJ,OAAQ,KACRC,SAAU,MACVC,KAAM,IAAI/F,KAAKA,KAAKC,MAAQ,QAA0BC,gBAG5D,CAAC,QACC3B,GAAa,EACf,GAiEI2H,EAAiBpG,IACrB,OAAQA,GACN,IAAK,OACH,OAAOrD,EAAAA,EAAAA,KAACgH,EAAAA,IAAa,CAACrH,UAAU,2BAClC,IAAK,UACH,OAAOK,EAAAA,EAAAA,KAAC0J,EAAAA,IAAO,CAAC/J,UAAU,4BAC5B,IAAK,SACH,OAAOK,EAAAA,EAAAA,KAAC2J,EAAAA,IAAG,CAAChK,UAAU,yBACxB,QACE,OAAOK,EAAAA,EAAAA,KAAC0J,EAAAA,IAAO,CAAC/J,UAAU,4BAI1BiK,EAAkBvG,IACtB,OAAQA,GACN,IAAK,OACH,MAAO,iBACT,IAAK,UACH,MAAO,kBACT,IAAK,SACH,MAAO,eACT,QACE,MAAO,kBAIb,OAAIxB,GAEA7B,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeI,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,uCACfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iCACfK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,yCAQvBK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAC,wBACjDF,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CACLzD,QAASkI,EACTvE,QAAQ,YACRC,KAAK,KAAItE,SAAA,EAETC,EAAAA,EAAAA,KAACuE,EAAAA,IAAS,CAAC5E,UAAU,iBAAiB,gBAKzCsC,IACCpC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DI,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,2BAA0BI,SAAC,cAE7CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oBAAmBI,SAAEkC,QAKtCpC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEI,SAAA,EACtFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,qBAC/CC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QA3GmB4B,UAE7BH,EAAS,0CA0GCkC,QAAQ,YACRC,KAAK,KAAItE,SACV,0BAKqC,KAA5B,OAAXwI,QAAW,IAAXA,OAAW,EAAXA,EAAaK,eAAeiB,SAC3BhK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBI,SAAA,EAC/BC,EAAAA,EAAAA,KAAC8J,EAAAA,IAAY,CAACnK,UAAU,0CACxBK,EAAAA,EAAAA,KAAA,KAAGL,UAAU,gBAAeI,SAAC,iCAG/BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACZ,OAAXwI,QAAW,IAAXA,OAAW,EAAXA,EAAaK,eAAe3I,IAAKkE,IAAM,IAAA4F,EAAA,OACtClK,EAAAA,EAAAA,MAAA,OAEEF,UAAU,wGAAuGI,SAAA,EAEjHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC8J,EAAAA,IAAY,CAACnK,UAAU,2BACxBE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,QAAMF,UAAU,yBAAwBI,SAAA,CACzB,QADyBgK,EACrC5F,EAAO2E,aAAK,IAAAiB,OAAA,EAAZA,EAAclG,cAAc,6BAAOM,EAAO0E,SAE5C1E,EAAO8E,YACNjJ,EAAAA,EAAAA,KAAA,QAAML,UAAU,2EAA0EI,SAAC,eAK9FoE,EAAO4E,aAAe5E,EAAO6E,aAC5BnJ,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CAAC,WAC1BoE,EAAO4E,YAAYiB,WAAWC,SAAS,EAAG,KAAK,IAAE9F,EAAO6E,qBAMvE7E,EAAO8E,YACPjJ,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAjJS4B,WACpCqG,GAAc,GACdxG,EAAS,MAET,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,uCAAwC,CACnEyB,OAAQ,OACRxB,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,GAC3B,eAAgB,oBAElBiD,KAAMC,KAAKC,UAAU,CAAEyE,sBAGzB,IAAKzH,EAASG,GACZ,MAAM,IAAIC,MAAM,2CAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,aAFjB0G,GAIV,CAAE,MAAOzF,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,kCAChD,CAAC,QACCuF,GAAc,EAChB,GAoHiCyB,CAA8BhG,EAAOlD,IACpDY,UAAW4G,EACXrE,QAAQ,YACRC,KAAK,KAAItE,SACV,qBA9BEoE,EAAOlD,YAyCV,OAAXsH,QAAW,IAAXA,OAAW,EAAXA,EAAaiB,eACZ3J,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+DAA8DI,SAAA,EAC3EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,sBACpDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,oCAAmCI,UAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGF,UAAU,gBAAeI,SAAA,CAAC,IACzBwI,EAAYiB,YAAYJ,OAAO,IAAEb,EAAYiB,YAAYH,SAASxF,kBAEtEhE,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CAAC,UAC3B,IAAIwD,KAAKgF,EAAYiB,YAAYF,MAAMrF,iCAQzDpE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,oBAElB,KAAtB,OAAXwI,QAAW,IAAXA,OAAW,EAAXA,EAAaW,SAASW,SACrB7J,EAAAA,EAAAA,KAAA,OAAKL,UAAU,mBAAkBI,UAC/BC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,gBAAeI,SAAC,yBAG/BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACZ,OAAXwI,QAAW,IAAXA,OAAW,EAAXA,EAAaW,SAASjJ,IAAKmK,IAC1BvK,EAAAA,EAAAA,MAAC8E,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BpF,UAAU,wGAAuGI,SAAA,EAEjHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,CACzC0J,EAAcW,EAAQ/G,SACvBxD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAML,UAAU,yBAAwBI,SAAEqK,EAAQjB,UAClDnJ,EAAAA,EAAAA,KAAA,QAAML,UAAS,uBAAAG,OAAyB8J,EAAeQ,EAAQ/G,SAAUtD,SACtEqK,EAAQ/G,OAAOO,OAAO,GAAGC,cAAgBuG,EAAQ/G,OAAOS,MAAM,SAGnE9D,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAEqK,EAAQlL,eAC9CW,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CACjC,IAAIwD,KAAK6G,EAAQd,MAAMrF,qBACvBmG,EAAQb,SAA8B,YAAnBa,EAAQ/G,SAC1BxD,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,eAAQ,IAAIwD,KAAK6G,EAAQb,SAAStF,kCAMhDpE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,QAAMF,UAAU,yBAAwBI,SAAA,CAAC,IACrCqK,EAAQhB,OAAO,IAAEgB,EAAQf,SAASxF,iBAElB,SAAnBuG,EAAQ/G,SACPxD,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CACLzD,QAASA,IAxPD4B,WAC5B,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,yBAAD5C,OAA0BuK,EAAS,aAAa,CAC1E1H,QAAS,CACP,cAAgB,UAAD7C,OAAYwC,MAI/B,IAAKG,EAASG,GACZ,MAAM,IAAIC,MAAM,8BAGlB,MAAM2E,QAAa/E,EAAS+E,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,WAAAnI,OAAcuK,EAAS,QACjCxC,EAAEM,QACFT,OAAOC,IAAIS,gBAAgBX,EAC7B,CAAE,MAAOvE,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,6BAChD,GAkOmCmH,CAAsBF,EAAQnJ,IAC7CmD,QAAQ,YACRC,KAAK,KAAItE,SAAA,EAETC,EAAAA,EAAAA,KAACiH,EAAAA,IAAU,CAACtH,UAAU,iBAAiB,mBAlCxCyK,EAAQnJ,gBC7TlBsJ,EAA8C9K,IAGpD,IAHqD,QAC1D+K,EAAO,SACPC,GACDhL,EACC,MAAOiL,EAAaC,IAAkB/I,EAAAA,EAAAA,WAAS,IACxCC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCK,EAAOC,IAAYN,EAAAA,EAAAA,UAAwB,OAC3CoB,EAASiD,IAAcrE,EAAAA,EAAAA,UAAwB,OAC/CgJ,EAAWC,IAAgBjJ,EAAAA,EAAAA,UAA2B,OACtDkJ,EAAkBC,IAAuBnJ,EAAAA,EAAAA,UAAS,KAClDoJ,EAAYC,IAAiBrJ,EAAAA,EAAAA,WAAS,IACtCsJ,EAAiBC,IAAsBvJ,EAAAA,EAAAA,WAAS,GA0DjDwJ,EAAkB/I,UACtB,GAAKyI,GAAgD,IAA5BA,EAAiBjB,OAA1C,CAKA/H,GAAa,GACbI,EAAS,MAET,IAEE,MAAM,aAAEmJ,SAAuB,6BACzBC,EAAWD,EACfE,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYC,uBACZD,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYE,6BAId,IAAKb,EACH,MAAM,IAAI/H,MAAM,2BAIlB,MAAQI,KAAMyI,EAAezJ,MAAO0J,SAC5BL,EAASM,KAAKC,IAAIC,UAAU,CAChCC,SAAUnB,EAAUmB,WAGxB,GAAIJ,EACF,MAAM,IAAI9I,MAAM8I,EAAexI,SAGjC,MAAQlB,MAAO+J,SAAsBV,EAASM,KAAKC,IAAII,OAAO,CAC5DF,SAAUnB,EAAUmB,SACpBG,YAAaR,EAAczK,GAC3BkL,KAAMrB,IAGR,GAAIkB,EACF,MAAM,IAAInJ,MAAMmJ,EAAY7I,SAG9B8C,EAAW,mDACX0E,GAAe,GACfQ,GAAmB,GACnBV,GAAS,EACX,CAAE,MAAOvH,GACPhB,EACEgB,aAAeL,MAAQK,EAAIC,QAAU,4BAEzC,CAAC,QACCrB,GAAa,EACf,CAhDA,MAFEI,EAAS,sCAqGPkK,EAAyBC,IAC7BC,UAAUC,UAAUC,UAAUH,GAC9BpG,EAAW,wBACXwG,WAAW,IAAMxG,EAAW,MAAO,MAG/ByG,EAAoBA,KACxB/B,GAAe,GACfE,EAAa,MACbE,EAAoB,IACpB7I,EAAS,MACT+D,EAAW,OAGb,OAAIyE,GAAeE,GAEf/K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAAC2M,EAAAA,IAAa,CAAChN,UAAU,8BACzBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,uCAKhDkC,IACCjC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,6DAA4DI,UACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,uBAAsBI,SAAEkC,SAK7Ce,IACChD,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iEAAgEI,UAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACgH,EAAAA,IAAa,CAACrH,UAAU,4BACzBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,yBAAwBI,SAAEiD,SAK9CkI,GAwFArL,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAACgH,EAAAA,IAAa,CAACrH,UAAU,2CACzBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCI,SAAC,+BAGpDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,sEAKvCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DI,SAAA,EAC1EC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAC,uCAGjDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,+BAA8BI,SAAC,8HAI5CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,8BAA6BI,SACzC6K,EAAUgC,YAAY3M,IAAI,CAACkM,EAAM7G,KAChCtF,EAAAA,EAAAA,KAAA,OAEEL,UAAU,+EAA8EI,UAExFC,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BI,SAAEoM,KAHzC7G,OAOXzF,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CACLzD,QAASA,IACP2L,EAAsBxB,EAAUgC,YAAYC,KAAK,OAEnDzI,QAAQ,YACRC,KAAK,KAAItE,SAAA,EAETC,EAAAA,EAAAA,KAAC8M,EAAAA,IAAW,CAACnN,UAAU,iBAAiB,wBAK5CK,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAM0K,GAAmB,GAClC/G,QAAQ,UACRzE,UAAU,SAAQI,SACnB,mCApIHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,8BAA6BI,SAAC,0BAG5CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,uFAI1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,uCAAsCI,UACnDC,EAAAA,EAAAA,KAAA,OACE+M,IAAKnC,EAAUoC,OACfC,IAAI,cACJtN,UAAU,oBAKhBE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,8BAA6BI,SAAC,wCAG5CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,oEAG1CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,6EAA4EI,UACzFC,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BI,SACzCiL,EAAaJ,EAAUsC,OAAS,wGAGrClN,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAMwK,GAAeD,GAC9B5G,QAAQ,YACRC,KAAK,KAAItE,SAERiL,GACChL,EAAAA,EAAAA,KAACmN,EAAAA,IAAQ,CAACxN,UAAU,aAEpBK,EAAAA,EAAAA,KAACoN,EAAAA,IAAK,CAACzN,UAAU,eAGrBK,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAM2L,EAAsBxB,EAAUsC,QAC/C9I,QAAQ,YACRC,KAAK,KAAItE,UAETC,EAAAA,EAAAA,KAAC8M,EAAAA,IAAW,CAACnN,UAAU,qBAK7BE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,8BAA6BI,SAAC,0BAG5CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,yDAG1CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAACqN,EAAAA,EAAK,CACJvO,MAAOgM,EACPpL,SAAUqL,EACVuC,YAAY,SACZ3N,UAAU,YAEZK,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAAS2K,EACTvJ,UAAWA,EACXjC,SAAsC,IAA5BkL,EAAiBjB,OAAa9J,SACzC,kBAMLC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iBAAgBI,UAC7BC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASiM,EACTtI,QAAQ,YACRzE,UAAU,SAAQI,SACnB,oBA6DXF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCI,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC2M,EAAAA,IAAa,CACZhN,UAAS,WAAAG,OACP0K,EAAU,iBAAmB,oBAGjC3K,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,iCAAgCI,SAAC,+BAG/CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,0DAKzCC,EAAAA,EAAAA,KAAA,OACEL,UAAS,uEAAAG,OACP0K,EACI,iCACA,gCACHzK,SAEFyK,EAAU,UAAY,gBAI1BvI,IACCjC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,6DAA4DI,UACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,uBAAsBI,SAAEkC,SAK7Ce,IACChD,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iEAAgEI,UAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACgH,EAAAA,IAAa,CAACrH,UAAU,4BACzBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,yBAAwBI,SAAEiD,UAKhDnD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SACjCyK,EACG,uGACA,8JAGNxK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iBAAgBI,SAC5ByK,GACCxK,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAjSa4B,UACvB,GACGiC,QACC,wGAFJ,CAQAxC,GAAa,GACbI,EAAS,MAET,IAEE,MAAM,aAAEmJ,SAAuB,6BACzBC,EAAWD,EACfE,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYC,uBACZD,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYE,8BAINxI,KAAMsK,EAAatL,MAAOuL,SAC1BlC,EAASM,KAAKC,IAAI4B,cAE1B,GAAID,EACF,MAAM,IAAI3K,MAAM2K,EAAarK,SAI/B,IAAK,MAAMuK,KAAUH,EAAYI,KAAM,CACrC,MAAQ1L,MAAO2L,SAAwBtC,EAASM,KAAKC,IAAIgC,SAAS,CAChE9B,SAAU2B,EAAOzM,KAGf2M,GACFE,QAAQ7L,MAAM,6BAA8B2L,EAEhD,CAEA3H,EAAW,mDACXwE,GAAS,EACX,CAAE,MAAOvH,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,wBAChD,CAAC,QACCrB,GAAa,EACf,CAtCA,GA2RUD,UAAWA,EACXuC,QAAQ,SAAQrE,SACjB,iBAIDF,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CACLzD,QAxZY4B,UACtBP,GAAa,GACbI,EAAS,MACT+D,EAAW,MAEX,IAEE,MAAM,aAAEoF,SAAuB,6BACzBC,EAAWD,EACfE,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYC,uBACZD,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYE,8BAIR,KAAExI,EAAI,MAAEhB,SAAgBqJ,EAASM,KAAKC,IAAIkC,OAAO,CACrDC,WAAY,OACZC,aAAc,0BAGhB,GAAIhM,EACF,MAAM,IAAIY,MAAMZ,EAAMkB,SAIxB0H,EAAa,CACXmC,OAAQ/J,EAAK0K,KAAKO,QAClBhB,OAAQjK,EAAK0K,KAAKT,OAClBN,YAAa,GACbb,SAAU9I,EAAKhC,KAEjB0J,GAAe,EACjB,CAAE,MAAOzH,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,uBAE9C0H,EAAa,CACXmC,OACE,yHACFE,OAAQ,mBACRnB,SAAU,iBACVa,YAAa,CACX,WACA,WACA,WACA,WACA,WACA,WACA,WACA,cAGJjC,GAAe,EACjB,CAAC,QACC7I,GAAa,EACf,GAoWUD,UAAWA,EACXuC,QAAQ,UAASrE,SAAA,EAEjBC,EAAAA,EAAAA,KAACmO,EAAAA,IAAK,CAACxO,UAAU,iBAAiB,yBCvbnCyO,EAA0D3O,IAIhE,IAJiE,OACtE4O,EAAM,QACNC,EAAO,UACPC,GACD9O,EACC,MAAO+O,EAAUC,IAAe7M,EAAAA,EAAAA,UAAS,CACvC8M,gBAAiB,GACjBC,YAAa,GACbC,gBAAiB,MAEZC,EAAeC,IAAoBlN,EAAAA,EAAAA,UAAS,CACjDmN,SAAS,EACTC,KAAK,EACL1K,SAAS,KAEJzC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCK,EAAOC,IAAYN,EAAAA,EAAAA,UAAwB,OAC3CoB,EAASiD,IAAcrE,EAAAA,EAAAA,UAAwB,MAOhDqN,EAAgBC,GAAmBpQ,IALfqQ,EAACD,EAAepQ,KACxC2P,EAAYW,IAAI7H,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAU6H,GAAI,IAAE,CAACF,GAAQpQ,KACzCoD,EAAS,OAITiN,CAAkBD,EAAOpQ,IAGrBuQ,EAA4BH,IAChCJ,EAAiBM,IAAI7H,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAU6H,GAAI,IAAE,CAACF,IAASE,EAAKF,OAsEhDI,EAAYA,KAChBb,EAAY,CACVC,gBAAiB,GACjBC,YAAa,GACbC,gBAAiB,KAEnBE,EAAiB,CACfC,SAAS,EACTC,KAAK,EACL1K,SAAS,IAEXpC,EAAS,MACT+D,EAAW,OAGPsJ,EAAcA,KAClBD,IACAhB,KAGF,OAAKD,GAGHrO,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iFAAgFI,UAC7FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kFAAiFI,SAAA,EAE9FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uEAAsEI,SAAA,EACnFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACmO,EAAAA,IAAK,CAACxO,UAAU,8BACjBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAC,wBAEnDC,EAAAA,EAAAA,KAAA,UACES,QAAS8O,EACT5P,UAAU,mDAAkDI,UAE5DC,EAAAA,EAAAA,KAAC2J,EAAAA,IAAG,CAAChK,UAAU,kBAKnBE,EAAAA,EAAAA,MAAA,QAAM2P,SAnFSnN,UAGnB,GAFAoN,EAAEC,iBAxBGlB,EAASE,gBAITF,EAASG,YAIVH,EAASG,YAAY9E,OAAS,GAChC3H,EAAS,mDACF,GAELsM,EAASG,cAAgBH,EAASI,iBACpC1M,EAAS,8BACF,GAELsM,EAASE,kBAAoBF,EAASG,cACxCzM,EAAS,wDACF,IAbPA,EAAS,4BACF,IALPA,EAAS,gCACF,GAwBT,CAEAJ,GAAa,GACbI,EAAS,MACT+D,EAAW,MAEX,IACE,MAAM3D,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,4BAA6B,CACxDyB,OAAQ,OACRxB,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAAD7C,OAAYwC,IAE7BiD,KAAMC,KAAKC,UAAU,CACnBiJ,gBAAiBF,EAASE,gBAC1BC,YAAaH,EAASG,gBAIpB1L,QAAaR,EAASM,OAE5B,IAAKN,EAASG,GACZ,MAAM,IAAIC,MAAMI,EAAKhB,OAAS,6BAGhCgE,EAAW,kCACXwG,WAAW,KACT8B,IACAD,IACAgB,KACC,KAEL,CAAE,MAAOpM,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,4BAChD,CAAC,QACCrB,GAAa,EACf,CArC2B,GAgFOnC,UAAU,gBAAeI,SAAA,EAErDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,sBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUI,SAAA,EACvBC,EAAAA,EAAAA,KAACqN,EAAAA,EAAK,CACJ7M,KAAMqO,EAAcE,QAAU,OAAS,WACvCjQ,MAAO0P,EAASE,gBAChBhP,SAAUuP,EAAa,mBACvB3B,YAAY,8BACZ3N,UAAU,QACVC,SAAUiC,KAEZ7B,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IAAM4O,EAAyB,WACxC1P,UAAU,qFAAoFI,SAE7F8O,EAAcE,SAAU/O,EAAAA,EAAAA,KAACmN,EAAAA,IAAQ,CAACxN,UAAU,aAAeK,EAAAA,EAAAA,KAACoN,EAAAA,IAAK,CAACzN,UAAU,qBAMnFE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,kBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUI,SAAA,EACvBC,EAAAA,EAAAA,KAACqN,EAAAA,EAAK,CACJ7M,KAAMqO,EAAcG,IAAM,OAAS,WACnClQ,MAAO0P,EAASG,YAChBjP,SAAUuP,EAAa,eACvB3B,YAAY,0BACZ3N,UAAU,QACVC,SAAUiC,KAEZ7B,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IAAM4O,EAAyB,OACxC1P,UAAU,qFAAoFI,SAE7F8O,EAAcG,KAAMhP,EAAAA,EAAAA,KAACmN,EAAAA,IAAQ,CAACxN,UAAU,aAAeK,EAAAA,EAAAA,KAACoN,EAAAA,IAAK,CAACzN,UAAU,kBAG7EK,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,oDAM5CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,0BAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUI,SAAA,EACvBC,EAAAA,EAAAA,KAACqN,EAAAA,EAAK,CACJ7M,KAAMqO,EAAcvK,QAAU,OAAS,WACvCxF,MAAO0P,EAASI,gBAChBlP,SAAUuP,EAAa,mBACvB3B,YAAY,4BACZ3N,UAAU,QACVC,SAAUiC,KAEZ7B,EAAAA,EAAAA,KAAA,UACEQ,KAAK,SACLC,QAASA,IAAM4O,EAAyB,WACxC1P,UAAU,qFAAoFI,SAE7F8O,EAAcvK,SAAUtE,EAAAA,EAAAA,KAACmN,EAAAA,IAAQ,CAACxN,UAAU,aAAeK,EAAAA,EAAAA,KAACoN,EAAAA,IAAK,CAACzN,UAAU,oBAMlFsC,IACCjC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wDAAuDI,UACpEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,uBAAsBI,SAAEkC,MAIxCe,IACChD,EAAAA,EAAAA,KAAA,OAAKL,UAAU,4DAA2DI,UACxEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,yBAAwBI,SAAEiD,OAK3CnD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,EAClCC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACL1D,KAAK,SACL4D,QAAQ,YACR3D,QAAS8O,EACT3P,SAAUiC,EACVlC,UAAU,SAAQI,SACnB,YAGDC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACL1D,KAAK,SACL4D,QAAQ,UACRvC,UAAWA,EACXjC,SAAUiC,EACVlC,UAAU,SAAQI,SAEjB8B,EAAY,cAAgB,+BA/HrB,MCvHT8N,EAAkDlQ,IAIxD,IAJyD,OAC9D4O,EAAM,QACNC,EAAO,OACPsB,GACDnQ,EACC,MAAOoQ,EAAkBC,IAAuBlO,EAAAA,EAAAA,UAAS,KAClDC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCK,EAAOC,IAAYN,EAAAA,EAAAA,UAAwB,OAC3CmO,EAAMC,IAAWpO,EAAAA,EAAAA,UAA8B,WAqBhDqO,EAnBe,CACnBC,WAAY,CACVxP,MAAO,qBACPxB,YAAa,wFACbiR,YAAa,aACbC,WAAY,qBACZC,YAAa,8DACbC,SAAU,gCAEZC,OAAQ,CACN7P,MAAO,iBACPxB,YAAa,mGACbiR,YAAa,iBACbC,WAAY,yBACZC,YAAa,uHACbC,SAAU,6BAIcV,GACtBY,EAAsBX,IAAqBI,EAAOE,YAElDM,EAAepO,UACnB,GAAa,YAAT0N,EAKJ,GAAKS,EAAL,CAKA1O,GAAa,GACbI,EAAS,MAET,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAMuN,EAAOK,SAAU,CAC5CnM,OAAQ,OACRxB,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAAD7C,OAAYwC,IAE7BiD,KAAMC,KAAKC,UAAU,CACnBiL,aAAcb,MAIZ5M,QAAaR,EAASM,OAE5B,IAAKN,EAASG,GACZ,MAAM,IAAIC,MAAMI,EAAKhB,OAAK,aAAAnC,OAAiB8P,EAAM,aAInDrN,aAAaoO,WAAW,cACxBpO,aAAaoO,WAAW,aACxBjJ,OAAOkJ,SAAS5I,KAAO,QAEzB,CAAE,MAAO9E,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAO,aAAArD,OAAgB8P,EAAM,YACnE,CAAC,QACC9N,GAAa,EACf,CAjCA,MAFEI,EAAS,gBAADpC,OAAiBmQ,EAAOE,YAAW,sBAL3CH,EAAQ,UA2CNT,EAAcA,KAClBO,EAAoB,IACpB5N,EAAS,MACT8N,EAAQ,WACR1B,KAQF,OAAKD,GAGHrO,EAAAA,EAAAA,KAAA,OAAKL,UAAU,iFAAgFI,UAC7FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8EAA6EI,SAAA,EAE1FF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEI,SAAA,EAC/EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCI,SAAEkQ,EAAOvP,YAE3DV,EAAAA,EAAAA,KAAA,UACES,QAAS8O,EACT5P,UAAU,mDAAkDI,UAE5DC,EAAAA,EAAAA,KAAC2J,EAAAA,IAAG,CAAChK,UAAU,kBAKnBK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,MAAKI,SACR,YAATgQ,GACClQ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wDAAuDI,UACpEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BI,SAAA,EACzCC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,+CAC/BE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,gCAA+BI,SAAC,aAC9CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAEkQ,EAAOI,uBAKnDxQ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAEkQ,EAAO/Q,cAEtC,WAAX0Q,IACC/P,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kCAAiCI,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,uBACHF,EAAAA,EAAAA,MAAA,MAAIF,UAAU,uCAAsCI,SAAA,EAClDC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,mCACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,+BACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,sCACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0CACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,qCAMZF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,EAClCC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLE,QAAQ,YACR3D,QAAS8O,EACT5P,UAAU,SAAQI,SACnB,YAGDC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLE,QAAQ,SACR3D,QAASgQ,EACT9Q,UAAU,SAAQI,SACnB,oBAMLF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wDAAuDI,UACpEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACqI,EAAAA,IAAO,CAAC1I,UAAU,0BACnBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,2BAA0BI,SAAC,6BAI7CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGF,UAAU,6BAA4BI,SAAA,CAAC,wCACJC,EAAAA,EAAAA,KAAA,QAAML,UAAU,mCAAkCI,SAAEkQ,EAAOE,cAAmB,yBAGpHnQ,EAAAA,EAAAA,KAACqN,EAAAA,EAAK,CACJ7M,KAAK,OACL1B,MAAO+Q,EACPnQ,SAAWZ,IACTgR,EAAoBhR,GACpBoD,EAAS,OAEXoL,YAAa2C,EAAOE,YACpBxQ,UAAU,YACVC,SAAUiC,OAIbI,IACCjC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,wDAAuDI,UACpEC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,uBAAsBI,SAAEkC,OAIzCpC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBI,SAAA,EAClCC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLE,QAAQ,YACR3D,QA5GGoQ,KACjBb,EAAQ,WACR9N,EAAS,OA2GKtC,SAAUiC,EACVlC,UAAU,SAAQI,SACnB,UAGDC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLE,QAAQ,SACR3D,QAASgQ,EACT5O,UAAWA,EACXjC,SAAUiC,IAAc2O,EACxB7Q,UAAU,SAAQI,SAEjB8B,EAAY,gBAAkBoO,EAAOG,0BApHlC,M,kCC/DtB,MAAMU,EAAsC,CAC1C,CACE7P,GAAI,UACJhC,MAAO,UACP8R,KAAMC,EAAAA,IACN9R,YAAa,mCAEf,CACE+B,GAAI,cACJhC,MAAO,cACP8R,KAAME,EAAAA,IACN/R,YAAa,6BAEf,CACE+B,GAAI,gBACJhC,MAAO,gBACP8R,KAAMG,EAAAA,IACNhS,YAAa,iCAEf,CACE+B,GAAI,WACJhC,MAAO,WACP8R,KAAMpE,EAAAA,IACNzN,YAAa,kCAEf,CACE+B,GAAI,eACJhC,MAAO,eACP8R,KAAMjH,EAAAA,IACN5K,YAAa,iCAEf,CACE+B,GAAI,UACJhC,MAAO,UACP8R,KAAMjH,EAAAA,IACN5K,YAAa,gCAEf,CACE+B,GAAI,OACJhC,MAAO,kBACP8R,KAAMI,EAAAA,IACNjS,YAAa,yCAIJkS,EAAyBA,KACpC,MAAOC,EAAeC,IAAoB1P,EAAAA,EAAAA,UAAS,YAC5CC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCK,EAAOC,IAAYN,EAAAA,EAAAA,UAAwB,OAC3CoB,EAASiD,IAAcrE,EAAAA,EAAAA,UAAwB,OAChD,KAAE2P,EAAI,OAAEC,IAAWC,EAAAA,EAAAA,KACnBC,GAAWC,EAAAA,EAAAA,OAGVC,EAAaC,IAAkBjQ,EAAAA,EAAAA,UAAS,CAC7CV,MAAU,OAAJqQ,QAAI,IAAJA,OAAI,EAAJA,EAAMrQ,OAAQ,GACpB4Q,OAAW,OAAJP,QAAI,IAAJA,OAAI,EAAJA,EAAMO,QAAS,GACtBC,IAAK,GACLC,OAAQ,QAGHpL,EAAaqL,IAAkBrQ,EAAAA,EAAAA,UAAS,CAC7CsQ,MAAO,OACPC,SAAU,KACVC,gBAAgB,EAChBC,UAAU,EACVC,iBAAkB,aAClBC,gBAAiB,GACjBC,gBAAiBzT,EAAAA,GAAgBI,UAG5BsT,EAAeC,IAAoB9Q,EAAAA,EAAAA,UAAS,CACjD+Q,oBAAoB,EACpBP,gBAAgB,EAChBQ,gBAAgB,EAChBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,iBAAiB,KAGZC,EAAkBC,IAAuBrR,EAAAA,EAAAA,UAAS,CACvDsR,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAgB,MAIXC,EAA2BC,IAChC1R,EAAAA,EAAAA,WAAS,IACJ2R,EAAuBC,IAA4B5R,EAAAA,EAAAA,WAAS,IAC5D6R,EAAkBC,IAAuB9R,EAAAA,EAAAA,UAE9C,eAGFO,EAAAA,EAAAA,WAAU,KACiBE,WACvB,IACEP,GAAa,GAEb,MAAMQ,EAAQC,aAAaC,QAAQ,cACnC,IAAKF,EAEH,YADAJ,EAAS,2BAKX,MAAMO,QAAiBC,MAAM,wBAAyB,CACpDC,QAAS,CACPgR,cAAc,UAAD7T,OAAYwC,MAI7B,IAAKG,EAASG,GACZ,MAAM,IAAIC,MAAM,mCAGlB,MAAMC,QAAeL,EAASM,OAC9B,GAAID,EAAOE,SAAWF,EAAOG,KAAM,CAEjC,MAAM2Q,EAAe9Q,EAAOG,KAC5BgP,EAAe,CACbC,MAAO0B,EAAa1B,MACpBC,SAAUyB,EAAazB,SACvBC,eAAgBwB,EAAaC,gBAC7BxB,SAAUuB,EAAaE,UACvBxB,iBAAkBsB,EAAaG,mBAC/BxB,gBAAiBqB,EAAaI,iBAC9BxB,gBAAiBoB,EAAaK,kBAElC,CACF,CAAE,MAAO/Q,GACPhB,EAAS,gCACT4L,QAAQ7L,MAAM,4BAA6BiB,EAC7C,CAAC,QACCpB,GAAa,EACf,GAGFoS,IACC,IAEH,MAAMC,EAAoB9R,UACxBP,GAAa,GACbI,EAAS,MACT+D,EAAW,MAEX,IACE,MAAMuI,EAAW,IAAI4F,SACrB5F,EAAS6F,OAAO,OAAQzC,EAAY1Q,MACpCsN,EAAS6F,OAAO,MAAOzC,EAAYG,KAC/BH,EAAYI,QACdxD,EAAS6F,OAAO,SAAUzC,EAAYI,QAGxC,MAAM1P,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,oBAAqB,CAChDyB,OAAQ,MACRxB,QAAS,CACPgR,cAAc,UAAD7T,OAAYwC,IAE3BiD,KAAMiJ,IAGR,IAAK/L,EAASG,GACZ,MAAM,IAAIC,MAAM,4BAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,OAFvBgE,EAAW,gCAIf,CAAE,MAAO/C,GACPhB,EAASgB,aAAeL,MAAQK,EAAIC,QAAU,2BAChD,CAAC,QACCrB,GAAa,EACf,GAGIwS,EAAwBjS,UAE5B,IAAIR,EAAJ,CACAC,GAAa,GACbI,EAAS,MACT+D,EAAW,MAEX,IACE,MAAM3D,EAAQC,aAAaC,QAAQ,cAEnC,IAAKF,EAGH,OAFAJ,EAAS,gCACTJ,GAAa,GAKf,MAAMyS,EAAqB,CACzBrC,MAAOtL,EAAYsL,MACnBC,SAAUvL,EAAYuL,SACtB0B,gBAAiBjN,EAAYwL,eAC7B0B,UAAWlN,EAAYyL,SACvB0B,mBAAoBnN,EAAY0L,iBAChC0B,iBAAkBpN,EAAY2L,gBAC9B0B,iBAAkBrN,EAAY4L,iBAG1B/P,QAAiBC,MAAM,wBAAyB,CACpDyB,OAAQ,MACRxB,QAAS,CACPgR,cAAc,UAAD7T,OAAYwC,GACzB,eAAgB,oBAElBiD,KAAMC,KAAKC,UAAU8O,KAGvB,IAAK9R,EAASG,GACZ,MAAM,IAAIC,MAAM,gCAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,OAFvBgE,EAAW,oCAIf,CAAE,MAAO/C,GACPhB,EACEgB,aAAeL,MAAQK,EAAIC,QAAU,+BAEzC,CAAC,QACCrB,GAAa,EACf,CAlDqB,GAqDjB0S,EAA0BnS,UAC9BP,GAAa,GACbI,EAAS,MACT+D,EAAW,MAEX,IACE,MAAM3D,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,0BAA2B,CACtDyB,OAAQ,MACRxB,QAAS,CACPgR,cAAc,UAAD7T,OAAYwC,GACzB,eAAgB,oBAElBiD,KAAMC,KAAKC,UAAUgN,KAGvB,IAAKhQ,EAASG,GACZ,MAAM,IAAIC,MAAM,0CAGlB,MAAMC,QAAeL,EAASM,OAC9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOb,OAFvBgE,EAAW,8CAIf,CAAE,MAAO/C,GACPhB,EACEgB,aAAeL,MACXK,EAAIC,QACJ,yCAER,CAAC,QACCrB,GAAa,EACf,GAGI2S,EAAepS,UACnB,IACEP,GAAa,SACP0P,IACNE,EAAS,SACX,CAAE,MAAOzP,GACPC,EAAS,uCACTJ,GAAa,EACf,GAGI4S,EAAuBA,KAAA,IAAAC,EAAAC,EAAA,OAC3B5U,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,yBAKtDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,qBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,0IAAyII,UACjJ,OAAJwR,QAAI,IAAJA,GAAU,QAANoD,EAAJpD,EAAMrQ,YAAI,IAAAyT,GAAW,QAAXC,EAAVD,EAAY/Q,OAAO,UAAE,IAAAgR,OAAjB,EAAJA,EAAuB/Q,gBAAiB,OAE3ChE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CACLE,QAAQ,YACRC,KAAK,KACL5D,QAASA,KACP,MAAMoU,EAAQ/M,SAASC,cAAc,SACrC8M,EAAMrU,KAAO,OACbqU,EAAMC,OAAS,UACfD,EAAME,SAAYtF,IAAO,IAADuF,EACtB,MAAMC,EAA2C,QAAvCD,EAAIvF,EAAEyF,OAA4BC,aAAK,IAAAH,OAAA,EAApCA,EAAuC,GAChDC,GACFpD,GAActK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIqK,GAAW,IAAEI,OAAQiD,MAG7CJ,EAAM1M,SACNpI,SAAA,EAEFC,EAAAA,EAAAA,KAACoV,EAAAA,IAAQ,CAACzV,UAAU,iBAAiB,mBAGvCK,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,iCAK3CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBC,EAAAA,EAAAA,KAACqN,EAAAA,EAAK,CACJpO,MAAM,YACNH,MAAO8S,EAAY1Q,KACnBxB,SAAWZ,GACT+S,GAActK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIqK,GAAW,IAAE1Q,KAAMpC,KAEzCwO,YAAY,0BAEdtN,EAAAA,EAAAA,KAACqN,EAAAA,EAAK,CACJpO,MAAM,gBACNuB,KAAK,QACL1B,MAAO8S,EAAYE,MACnBpS,SAAWZ,GACT+S,GAActK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIqK,GAAW,IAAEE,MAAOhT,KAE1CwO,YAAY,mBACZ1N,UAAQ,KAEVC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,oBAGhEC,EAAAA,EAAAA,KAAA,YACElB,MAAO8S,EAAYG,IACnBrS,SAAW+P,GACToC,GAActK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIqK,GAAW,IAAEG,IAAKtC,EAAEyF,OAAOpW,SAEjDwO,YAAY,4BACZ+H,KAAM,EACN1V,UAAU,mLAIhBK,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CAACzD,QAAS0T,EAAmBtS,UAAWA,EAAU9B,SAAC,yBAodlE,OACEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CI,SAAA,EAC5DF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CI,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMI,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,qCAAoCI,SAAC,cACnDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,gBAAeI,SAAC,2CAI9BkC,IACCpC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DI,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,QAAML,UAAU,2BAA0BI,SAAC,cAE7CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,oBAAmBI,SAAEkC,KAClCjC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAMyB,EAAS,MACxBkC,QAAQ,YACRC,KAAK,KACL1E,UAAU,OAAMI,SACjB,eAMJiD,IACCnD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iEAAgEI,SAAA,EAC7EF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BI,SAAA,EAC1CC,EAAAA,EAAAA,KAACgH,EAAAA,IAAa,CAACrH,UAAU,4BACzBK,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BI,SAAC,gBAE/CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,sBAAqBI,SAAEiD,KACpChD,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLzD,QAASA,IAAMwF,EAAW,MAC1B7B,QAAQ,YACRC,KAAK,KACL1E,UAAU,OAAMI,SACjB,gBAMLF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCI,SAAA,EAEpDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,UAC5BC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,sEAAqEI,UAClFC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACvB+Q,EAAiB7Q,IAAKqV,IACrB,MAAMC,EAAOD,EAAQvE,KACfyE,EAAWnE,IAAkBiE,EAAQrU,GAE3C,OACEpB,EAAAA,EAAAA,MAAA,UAEEY,QAASA,IAAM6Q,EAAiBgE,EAAQrU,IACxCtB,UAAS,6KAAAG,OAIL0V,EACI,kEACA,8DAA6D,4BAEnEzV,SAAA,EAEFC,EAAAA,EAAAA,KAACuV,EAAI,CAAC5V,UAAU,aAChBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,QAAML,UAAU,oBAAmBI,SAChCuV,EAAQrW,SAEXe,EAAAA,EAAAA,KAAA,QAAML,UAAU,uCAAsCI,SACnDuV,EAAQpW,mBAlBRoW,EAAQrU,aA6BzBjB,EAAAA,EAAAA,KAAA,OAAKL,UAAU,gBAAeI,UAC5BC,EAAAA,EAAAA,KAAC2E,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAG2Q,EAAG,IAC1BzQ,QAAS,CAAEF,QAAS,EAAG2Q,EAAG,GAC1BxQ,WAAY,CAAEC,SAAU,IACxBvF,UAAU,sEAAqEI,SAjHrE2V,MACpB,OAAQrE,GACN,IAAK,UAcL,QACE,OAAOqD,IAbT,IAAK,cACH,OA3bJ1U,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,qBAGtDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,WAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEY,QAASA,IACPwR,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIX,GAAW,IAAEsL,MAAO,UAE1CvS,UAAS,6EAAAG,OACe,SAAtB8G,EAAYsL,MACR,wDACA,uDACHnS,SAAA,EAEHC,EAAAA,EAAAA,KAAC2V,EAAAA,IAAM,CAAChW,UAAU,aAClBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,aAERF,EAAAA,EAAAA,MAAA,UACEY,QAASA,IACPwR,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIX,GAAW,IAAEsL,MAAO,WAE1CvS,UAAS,6EAAAG,OACe,UAAtB8G,EAAYsL,MACR,wDACA,uDAENtS,UAAQ,EAAAG,SAAA,EAERC,EAAAA,EAAAA,KAAC4V,EAAAA,IAAK,CAACjW,UAAU,aACjBK,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kCAMZF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,cAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAO8H,EAAYuL,SACnBzS,SAAW+P,GACTwC,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIX,GAAW,IAAEuL,SAAU1C,EAAEyF,OAAOpW,SAEtDa,UAAU,0KAAyKI,SAAA,EAEnLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,KAAIiB,SAAC,aACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,KAAKc,UAAQ,EAAAG,SAAC,2BAG5BC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,KAAKc,UAAQ,EAAAG,SAAC,gCAOhCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,wBAGhEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBI,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPwR,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXX,GAAW,IACd0L,iBAAkB,gBAGtB3S,UAAS,6EAAAG,OAC0B,eAAjC8G,EAAY0L,iBACR,wDACA,uDACHvS,UAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBAERC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPwR,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIX,GAAW,IAAE0L,iBAAkB,UAErD3S,UAAS,6EAAAG,OAC0B,SAAjC8G,EAAY0L,iBACR,wDACA,uDACHvS,UAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBAMZF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,wCAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAO8H,EAAY2L,gBACnB7S,SAAW+P,GACTwC,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXX,GAAW,IACd2L,gBAAiBsD,SAASpG,EAAEyF,OAAOpW,UAGvCa,UAAU,0KAAyKI,SAAA,EAEnLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,YACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,qBAKvBC,EAAAA,EAAAA,KAACR,EAAAA,EAAkB,CACjBV,MAAO8H,EAAY4L,gBACnB9S,SAAWoW,GACT7D,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIX,GAAW,IAAE4L,gBAAiBsD,KAEpD7W,MAAM,2BACNU,UAAU,4CAIZE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,oCAAmCI,SAAC,qBAGrDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,wCAIvCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPwR,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXX,GAAW,IACdwL,gBAAiBxL,EAAYwL,kBAGjCzS,UAAS,6EAAAG,OACP8G,EAAYwL,eAAiB,iBAAmB,eAC/CrS,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACP8G,EAAYwL,eACR,gBACA,yBAMZvS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,oCAAmCI,SAAC,eAGrDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,yCAIvCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPwR,GAAc1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXX,GAAW,IACdyL,UAAWzL,EAAYyL,YAG3B1S,UAAS,6EAAAG,OACP8G,EAAYyL,SAAW,iBAAmB,eACzCtS,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACP8G,EAAYyL,SAAW,gBAAkB,+BAOrDrS,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CAACzD,QAAS6T,EAAuBzS,UAAWA,EAAU9B,SAAC,4BAyPlE,IAAK,gBACH,OAjPJC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,2BAGtDC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,SACvBmH,OAAOC,QAAQsL,GAAexS,IAAIR,IAAA,IAAE2H,EAAKtI,GAAMW,EAAA,OAC9CI,EAAAA,EAAAA,MAAA,OAAeF,UAAU,oCAAmCI,SAAA,EAC1DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAC5DqH,EAAIC,QAAQ,WAAY,OAAOC,UAElCzH,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBI,SAAA,CACzB,uBAARqH,GACC,sCACO,mBAARA,GACC,uCACO,mBAARA,GACC,wCACO,oBAARA,GAA6B,2BACrB,6BAARA,GACC,4CACO,oBAARA,GACC,iDAGNpH,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPiS,GAAgBnL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIkL,GAAa,IAAE,CAACrL,IAAOtI,KAE/Ca,UAAS,6EAAAG,OACPhB,EAAQ,iBAAmB,eAC1BiB,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACPhB,EAAQ,gBAAkB,uBA7BxBsI,QAoCdpH,EAAAA,EAAAA,KAAA,OAAKL,UAAU,OAAMI,UACnBC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CAACzD,QAAS+T,EAAyB3S,UAAWA,EAAU9B,SAAC,sCAsMpE,IAAK,WACH,OA9LJC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAWI,UACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,wCAAuCI,SAAC,uBAGtDF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EAExBC,EAAAA,EAAAA,KAACuK,EAAa,CACZC,QAASwI,EAAiBE,iBAC1BzI,SAAWD,GACTyI,GAAmB1L,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAChByL,GAAgB,IACnBE,iBAAkB1I,QAMxB3K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,8BAA6BI,SAAC,wBAC5CF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,oCAAmCI,SAAC,yBAGrDC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,wBAAuBI,SAAC,yDAIvCC,EAAAA,EAAAA,KAAA,UACES,QAASA,IACPwS,GAAmB1L,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAChByL,GAAgB,IACnBG,oBAAqBH,EAAiBG,sBAG1CxT,UAAS,6EAAAG,OACPkT,EAAiBG,mBACb,iBACA,eACHpT,UAEHC,EAAAA,EAAAA,KAAA,QACEL,UAAS,6EAAAG,OACPkT,EAAiBG,mBACb,gBACA,yBAMZtT,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOL,UAAU,+CAA8CI,SAAC,+BAGhEF,EAAAA,EAAAA,MAAA,UACEf,MAAOkU,EAAiBI,eACxB1T,SAAW+P,GACTwD,GAAmB1L,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAChByL,GAAgB,IACnBI,eAAgByC,SAASpG,EAAEyF,OAAOpW,UAGtCa,UAAU,0KAAyKI,SAAA,EAEnLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,gBACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,GAAGiB,SAAC,YACnBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,IAAIiB,SAAC,aACpBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,IAAIiB,SAAC,aACpBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,KAAKiB,SAAC,WACrBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,YACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,MAAMiB,SAAC,aACtBC,EAAAA,EAAAA,KAAA,UAAQlB,MAAO,EAAEiB,SAAC,8BAO1BF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAACmO,EAAAA,IAAK,CAACxO,UAAU,8BACjBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,wBAEzCC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,sDAG1CC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLE,QAAQ,YACR3D,QAASA,IAAM6S,GAA6B,GAAMvT,SACnD,wBAMHF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qEAAoEI,SAAA,EACjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAAC+V,EAAAA,IAAQ,CAACpW,UAAU,6BACpBK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,iBAEzCC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,6CAG1CC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLE,QAAQ,YACR3D,QAASgU,EACT7U,SAAUiC,EAAU9B,SAEnB8B,EAAY,iBAAmB,iBAKpChC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wDAAuDI,SAAA,EACpEF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCI,SAAA,EAC/CC,EAAAA,EAAAA,KAAC2D,EAAAA,IAAmB,CAAChE,UAAU,0BAC/BK,EAAAA,EAAAA,KAAA,MAAIL,UAAU,yBAAwBI,SAAC,oBAEzCF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,gCAA+BI,SAAC,0BAG9CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,uEAI1CC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLE,QAAQ,YACRC,KAAK,KACL5D,QAASA,KACPiT,EAAoB,cACpBF,GAAyB,IACzBzT,SACH,2BAKHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIL,UAAU,gCAA+BI,SAAC,sBAG9CC,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BI,SAAC,4FAI1CC,EAAAA,EAAAA,KAACkE,EAAAA,EAAM,CACLE,QAAQ,SACRC,KAAK,KACL5D,QAASA,KACPiT,EAAoB,UACpBF,GAAyB,IACzBzT,SACH,oCA2BX,IAAK,eACH,OAjBkCC,EAAAA,EAAAA,KAACuB,EAAsB,IAkB3D,IAAK,UACH,OAjB6BvB,EAAAA,EAAAA,KAACsI,EAAe,IAkB/C,IAAK,OACH,OAjB0BtI,EAAAA,EAAAA,KAAC4F,EAAc,MAqHlC8P,IANIrE,YAabrR,EAAAA,EAAAA,KAACoO,EAAmB,CAClBC,OAAQgF,EACR/E,QAASA,IAAMgF,GAA6B,GAC5C/E,UAAWA,KAETT,QAAQkI,IAAI,qCAIhBhW,EAAAA,EAAAA,KAAC2P,EAAe,CACdtB,OAAQkF,EACRjF,QAASA,IAAMkF,GAAyB,GACxC5D,OAAQ6D,O,uEC32BT,IAAK1U,EAAe,SAAfA,GAAe,OAAfA,EAAe,YAAfA,EAAe,gBAAfA,EAAe,YAAfA,EAAe,kBAAfA,EAAe,oBAAfA,EAAe,UAAfA,CAAe,MASfkX,EAAa,SAAbA,GAAa,OAAbA,EAAa,cAAbA,EAAa,gBAAbA,EAAa,YAAbA,CAAa,MAiMlB,MAAMC,EAA2BJ,IACU,CAC9C,CAAC/W,EAAgBC,MAAO,OACxB,CAACD,EAAgBI,QAAS,SAC1B,CAACJ,EAAgBK,MAAO,OACxB,CAACL,EAAgBM,SAAU,UAC3B,CAACN,EAAgBO,UAAW,WAC5B,CAACP,EAAgBQ,KAAM,OAEXuW,IA+CHK,EAA2BL,IACW,CAC/C,CAAC/W,EAAgBC,MAAO,EACxB,CAACD,EAAgBI,QAAS,EAC1B,CAACJ,EAAgBK,MAAO,EACxB,CAACL,EAAgBM,SAAU,EAC3B,CAACN,EAAgBO,UAAW,EAC5B,CAACP,EAAgBQ,KAAM,GAEVuW,IAGJM,EAA2BC,IACtC,OAAQA,GACN,KAAK,EAEL,KAAK,EACH,OAAOtX,EAAgBC,KACzB,KAAK,EAUL,QACE,OAAOD,EAAgBI,OATzB,KAAK,EACH,OAAOJ,EAAgBK,KACzB,KAAK,EACH,OAAOL,EAAgBM,QACzB,KAAK,EACH,OAAON,EAAgBO,SACzB,KAAK,EACH,OAAOP,EAAgBQ,K", "sources": ["components/common/DifficultySelector.tsx", "components/settings/SubscriptionManagement.tsx", "components/settings/DataManagement.tsx", "components/settings/EnhancedBilling.tsx", "components/settings/TwoFactorAuth.tsx", "components/settings/ChangePasswordModal.tsx", "components/settings/DangerZoneModal.tsx", "pages/SettingsPage.tsx", "shared/types.ts"], "sourcesContent": ["import React from \"react\";\r\nimport { DifficultyLevel } from \"../../shared/types\";\r\n\r\ninterface DifficultySelectorProps {\r\n  value: DifficultyLevel;\r\n  onChange: (difficulty: DifficultyLevel) => void;\r\n  className?: string;\r\n  disabled?: boolean;\r\n  label?: string;\r\n}\r\n\r\nconst difficultyOptions = [\r\n  {\r\n    value: DifficultyLevel.EASY,\r\n    label: \"Easy\",\r\n    description: \"Basic facts and definitions\",\r\n  },\r\n  {\r\n    value: DifficultyLevel.MEDIUM,\r\n    label: \"Medium\",\r\n    description: \"Moderate understanding required\",\r\n  },\r\n  {\r\n    value: DifficultyLevel.HARD,\r\n    label: \"Hard\",\r\n    description: \"Deep analysis and critical thinking\",\r\n  },\r\n  {\r\n    value: DifficultyLevel.COLLEGE,\r\n    label: \"College\",\r\n    description: \"Undergraduate level complexity\",\r\n  },\r\n  {\r\n    value: DifficultyLevel.GRADUATE,\r\n    label: \"Graduate\",\r\n    description: \"Advanced graduate study\",\r\n  },\r\n  {\r\n    value: DifficultyLevel.PHD,\r\n    label: \"PhD\",\r\n    description: \"Research-level expertise\",\r\n  },\r\n];\r\n\r\nconst getDifficultyColor = (difficulty: DifficultyLevel): string => {\r\n  switch (difficulty) {\r\n    case DifficultyLevel.EASY:\r\n      return \"bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50\";\r\n    case DifficultyLevel.MEDIUM:\r\n      return \"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50\";\r\n    case DifficultyLevel.HARD:\r\n      return \"bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50\";\r\n    case DifficultyLevel.COLLEGE:\r\n      return \"bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50\";\r\n    case DifficultyLevel.GRADUATE:\r\n      return \"bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50\";\r\n    case DifficultyLevel.PHD:\r\n      return \"bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50\";\r\n    default:\r\n      return \"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50\";\r\n  }\r\n};\r\n\r\nconst getSelectedDifficultyColor = (difficulty: DifficultyLevel): string => {\r\n  switch (difficulty) {\r\n    case DifficultyLevel.EASY:\r\n      return \"bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20\";\r\n    case DifficultyLevel.MEDIUM:\r\n      return \"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20\";\r\n    case DifficultyLevel.HARD:\r\n      return \"bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20\";\r\n    case DifficultyLevel.COLLEGE:\r\n      return \"bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20\";\r\n    case DifficultyLevel.GRADUATE:\r\n      return \"bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20\";\r\n    case DifficultyLevel.PHD:\r\n      return \"bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20\";\r\n    default:\r\n      return \"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20\";\r\n  }\r\n};\r\n\r\nexport const DifficultySelector: React.FC<DifficultySelectorProps> = ({\r\n  value,\r\n  onChange,\r\n  className = \"\",\r\n  disabled = false,\r\n  label = \"Difficulty Level\",\r\n}) => {\r\n  return (\r\n    <div className={`space-y-3 ${className}`}>\r\n      <label className=\"block text-sm font-medium text-text-primary\">\r\n        {label}\r\n      </label>\r\n      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\r\n        {difficultyOptions.map((option) => {\r\n          const isSelected = value === option.value;\r\n          const colorClasses = isSelected\r\n            ? getSelectedDifficultyColor(option.value)\r\n            : getDifficultyColor(option.value);\r\n\r\n          return (\r\n            <button\r\n              key={option.value}\r\n              type=\"button\"\r\n              onClick={() => !disabled && onChange(option.value)}\r\n              disabled={disabled}\r\n              className={`\r\n                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu\r\n                ${colorClasses}\r\n                ${\r\n                  disabled\r\n                    ? \"opacity-50 cursor-not-allowed\"\r\n                    : \"cursor-pointer hover:scale-105\"\r\n                }\r\n                ${\r\n                  isSelected\r\n                    ? \"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary\"\r\n                    : \"\"\r\n                }\r\n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary\r\n              `}\r\n              title={option.description}\r\n              aria-pressed={isSelected}\r\n            >\r\n              <div className=\"text-center\">\r\n                <div className=\"font-semibold\">{option.label}</div>\r\n                <div\r\n                  className={`text-xs mt-1 ${\r\n                    isSelected ? \"text-white/90\" : \"text-text-secondary\"\r\n                  }`}\r\n                >\r\n                  {option.description}\r\n                </div>\r\n              </div>\r\n              {isSelected && (\r\n                <div className=\"absolute top-2 right-2\">\r\n                  <svg\r\n                    className=\"w-4 h-4\"\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n              )}\r\n            </button>\r\n          );\r\n        })}\r\n      </div>\r\n      <p className=\"text-xs text-text-muted\">\r\n        Select the appropriate difficulty level for your flashcards. This\r\n        affects the complexity of questions and answers generated.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DifficultySelector;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  HiRefresh,\r\n  HiExclamationCircle,\r\n  HiInformationCircle\r\n} from 'react-icons/hi';\r\nimport { Button } from '../common/Button';\r\n\r\ninterface SubscriptionPlan {\r\n  id: string;\r\n  name: string;\r\n  price: number;\r\n  interval: 'month' | 'year';\r\n  features: string[];\r\n  popular?: boolean;\r\n  current?: boolean;\r\n}\r\n\r\ninterface SubscriptionData {\r\n  currentPlan: SubscriptionPlan | null;\r\n  status: 'active' | 'canceled' | 'past_due' | 'trialing';\r\n  nextBillingDate?: string;\r\n  cancelAtPeriodEnd?: boolean;\r\n}\r\n\r\nconst subscriptionPlans: SubscriptionPlan[] = [\r\n  {\r\n    id: 'free',\r\n    name: 'Study Starter',\r\n    price: 0,\r\n    interval: 'month',\r\n    features: [\r\n      '500 AI study generations per month',\r\n      'Basic flashcards and quizzes',\r\n      'Up to 5 document uploads',\r\n      'Basic study analytics',\r\n      'Perfect for trying out ChewyAI'\r\n    ]\r\n  },\r\n  {\r\n    id: 'pro_monthly',\r\n    name: 'Scholar Pro',\r\n    price: 9.99,\r\n    interval: 'month',\r\n    popular: true,\r\n    features: [\r\n      'Unlimited AI study generations',\r\n      'Advanced study modes & spaced repetition',\r\n      'Unlimited document uploads',\r\n      'Detailed progress analytics',\r\n      'Priority support during finals',\r\n      'Export study materials',\r\n      'Perfect for serious students'\r\n    ]\r\n  },\r\n  {\r\n    id: 'pro_yearly',\r\n    name: 'Academic Year Pass',\r\n    price: 99.99,\r\n    interval: 'year',\r\n    features: [\r\n      'Unlimited AI study generations',\r\n      'Advanced study modes & spaced repetition',\r\n      'Unlimited document uploads',\r\n      'Detailed progress analytics',\r\n      'Priority support during finals',\r\n      'Export study materials',\r\n      'Save $20 vs monthly (2 months free!)',\r\n      'Perfect for the full academic year'\r\n    ]\r\n  }\r\n];\r\n\r\nexport const SubscriptionManagement: React.FC = () => {\r\n  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isChanging, setIsChanging] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    fetchSubscriptionData();\r\n  }, []);\r\n\r\n  const fetchSubscriptionData = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/subscription', {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch subscription data');\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        setSubscriptionData(result.data);\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load subscription data');\r\n      // Set mock data for development\r\n      setSubscriptionData({\r\n        currentPlan: subscriptionPlans[0], // Free plan\r\n        status: 'active',\r\n        nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePlanChange = async (planId: string) => {\r\n    setIsChanging(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/subscription/change', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ planId }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to change subscription plan');\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        await fetchSubscriptionData();\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to change subscription plan');\r\n    } finally {\r\n      setIsChanging(false);\r\n    }\r\n  };\r\n\r\n  const handleCancelSubscription = async () => {\r\n    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.')) {\r\n      return;\r\n    }\r\n\r\n    setIsChanging(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/subscription/cancel', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to cancel subscription');\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        await fetchSubscriptionData();\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to cancel subscription');\r\n    } finally {\r\n      setIsChanging(false);\r\n    }\r\n  };\r\n\r\n  const handleReactivateSubscription = async () => {\r\n    setIsChanging(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/subscription/reactivate', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to reactivate subscription');\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        await fetchSubscriptionData();\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to reactivate subscription');\r\n    } finally {\r\n      setIsChanging(false);\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"animate-pulse\">\r\n          <div className=\"h-8 w-48 bg-gray-600 rounded mb-4\"></div>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n            {[1, 2, 3].map((i) => (\r\n              <div key={i} className=\"h-64 bg-gray-600 rounded-lg\"></div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Subscription Management</h3>\r\n        \r\n        {error && (\r\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n              <span className=\"text-red-400 font-medium\">Error</span>\r\n            </div>\r\n            <p className=\"text-red-300 mt-1\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Current Subscription Status */}\r\n        {subscriptionData && (\r\n          <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <div>\r\n                <h4 className=\"text-lg font-medium text-white\">Current Plan</h4>\r\n                <p className=\"text-gray-400\">\r\n                  {subscriptionData.currentPlan?.name || 'No active plan'}\r\n                </p>\r\n              </div>\r\n              <div className=\"text-right\">\r\n                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${\r\n                  subscriptionData.status === 'active' ? 'bg-green-500/20 text-green-400' :\r\n                  subscriptionData.status === 'canceled' ? 'bg-red-500/20 text-red-400' :\r\n                  subscriptionData.status === 'past_due' ? 'bg-yellow-500/20 text-yellow-400' :\r\n                  'bg-blue-500/20 text-blue-400'\r\n                }`}>\r\n                  {subscriptionData.status.charAt(0).toUpperCase() + subscriptionData.status.slice(1)}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {subscriptionData.nextBillingDate && (\r\n              <div className=\"flex items-center space-x-2 text-gray-400 text-sm\">\r\n                <HiInformationCircle className=\"w-4 h-4\" />\r\n                <span>\r\n                  {subscriptionData.cancelAtPeriodEnd \r\n                    ? `Access ends on ${new Date(subscriptionData.nextBillingDate).toLocaleDateString()}`\r\n                    : `Next billing date: ${new Date(subscriptionData.nextBillingDate).toLocaleDateString()}`\r\n                  }\r\n                </span>\r\n              </div>\r\n            )}\r\n\r\n            {subscriptionData.currentPlan?.id !== 'free' && (\r\n              <div className=\"mt-4 flex space-x-3\">\r\n                {subscriptionData.cancelAtPeriodEnd ? (\r\n                  <Button\r\n                    onClick={handleReactivateSubscription}\r\n                    isLoading={isChanging}\r\n                    variant=\"primary\"\r\n                    size=\"sm\"\r\n                  >\r\n                    Reactivate Subscription\r\n                  </Button>\r\n                ) : (\r\n                  <Button\r\n                    onClick={handleCancelSubscription}\r\n                    isLoading={isChanging}\r\n                    variant=\"danger\"\r\n                    size=\"sm\"\r\n                  >\r\n                    Cancel Subscription\r\n                  </Button>\r\n                )}\r\n                <Button\r\n                  onClick={fetchSubscriptionData}\r\n                  variant=\"secondary\"\r\n                  size=\"sm\"\r\n                >\r\n                  <HiRefresh className=\"w-4 h-4 mr-2\" />\r\n                  Refresh\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Available Plans */}\r\n        <div>\r\n          <h4 className=\"text-lg font-medium text-white mb-4\">Available Plans</h4>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n            {subscriptionPlans.map((plan) => {\r\n              const isCurrent = subscriptionData?.currentPlan?.id === plan.id;\r\n              \r\n              return (\r\n                <motion.div\r\n                  key={plan.id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.3 }}\r\n                  className={`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${\r\n                    plan.popular \r\n                      ? 'border-primary-500 ring-2 ring-primary-500/20' \r\n                      : isCurrent\r\n                        ? 'border-green-500 ring-2 ring-green-500/20'\r\n                        : 'border-border-primary hover:border-gray-500'\r\n                  }`}\r\n                >\r\n                  {plan.popular && (\r\n                    <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\r\n                      <div className=\"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1\">\r\n                        <HiStar className=\"w-3 h-3\" />\r\n                        <span>Most Popular</span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {isCurrent && (\r\n                    <div className=\"absolute -top-3 right-4\">\r\n                      <div className=\"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1\">\r\n                        <HiCheck className=\"w-3 h-3\" />\r\n                        <span>Current</span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"text-center mb-6\">\r\n                    <h5 className=\"text-xl font-semibold text-white mb-2\">{plan.name}</h5>\r\n                    <div className=\"text-3xl font-bold text-white\">\r\n                      ${plan.price}\r\n                      <span className=\"text-lg text-gray-400\">/{plan.interval}</span>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <ul className=\"space-y-3 mb-6\">\r\n                    {plan.features.map((feature, index) => (\r\n                      <li key={index} className=\"flex items-center space-x-2\">\r\n                        <HiCheck className=\"w-4 h-4 text-green-400 flex-shrink-0\" />\r\n                        <span className=\"text-gray-300 text-sm\">{feature}</span>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n\r\n                  <Button\r\n                    onClick={() => handlePlanChange(plan.id)}\r\n                    disabled={isCurrent || isChanging}\r\n                    isLoading={isChanging}\r\n                    variant={plan.popular ? 'primary' : 'secondary'}\r\n                    className=\"w-full\"\r\n                  >\r\n                    {isCurrent ? 'Current Plan' : `Switch to ${plan.name}`}\r\n                  </Button>\r\n                </motion.div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from 'react';\r\nimport {\r\n  HiDownload,\r\n  HiExclamation<PERSON>ircle,\r\n  HiCheckCircle,\r\n  HiTrash,\r\n  HiRefresh\r\n} from 'react-icons/hi';\r\nimport { Button } from '../common/Button';\r\n\r\ninterface ExportData {\r\n  studySets: boolean;\r\n  flashcards: boolean;\r\n  quizzes: boolean;\r\n  analytics: boolean;\r\n  preferences: boolean;\r\n}\r\n\r\ninterface DataStats {\r\n  studySets: number;\r\n  flashcards: number;\r\n  quizzes: number;\r\n  documents: number;\r\n  totalSize: string;\r\n}\r\n\r\nexport const DataManagement: React.FC = () => {\r\n  const [isExporting, setIsExporting] = useState(false);\r\n  const [isClearing, setIsClearing] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n  const [dataStats, setDataStats] = useState<DataStats>({\r\n    studySets: 12,\r\n    flashcards: 245,\r\n    quizzes: 18,\r\n    documents: 8,\r\n    totalSize: '2.4 MB'\r\n  });\r\n  \r\n  const [exportData, setExportData] = useState<ExportData>({\r\n    studySets: true,\r\n    flashcards: true,\r\n    quizzes: true,\r\n    analytics: true,\r\n    preferences: true\r\n  });\r\n\r\n  const handleExportData = async () => {\r\n    setIsExporting(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/data/export', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ exportData }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to export data');\r\n      }\r\n\r\n      const blob = await response.blob();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `chewyai-data-export-${new Date().toISOString().split('T')[0]}.json`;\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n\r\n      setSuccess('Data exported successfully!');\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to export data');\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleClearData = async (dataType: string) => {\r\n    if (!confirm(`Are you sure you want to clear all ${dataType}? This action cannot be undone.`)) {\r\n      return;\r\n    }\r\n\r\n    setIsClearing(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch(`/api/data/clear/${dataType}`, {\r\n        method: 'DELETE',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to clear ${dataType}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        setSuccess(`${dataType} cleared successfully!`);\r\n        await fetchDataStats();\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : `Failed to clear ${dataType}`);\r\n    } finally {\r\n      setIsClearing(false);\r\n    }\r\n  };\r\n\r\n  const fetchDataStats = async () => {\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/data/stats', {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        if (result.success) {\r\n          setDataStats(result.data);\r\n        }\r\n      }\r\n    } catch (err) {\r\n      // Silently fail for stats refresh\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Data Management</h3>\r\n        \r\n        {error && (\r\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n              <span className=\"text-red-400 font-medium\">Error</span>\r\n            </div>\r\n            <p className=\"text-red-300 mt-1\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {success && (\r\n          <div className=\"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiCheckCircle className=\"w-5 h-5 text-green-400\" />\r\n              <span className=\"text-green-400 font-medium\">Success</span>\r\n            </div>\r\n            <p className=\"text-green-300 mt-1\">{success}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Data Overview */}\r\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h4 className=\"text-lg font-medium text-white\">Your Data Overview</h4>\r\n            <Button\r\n              onClick={fetchDataStats}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n            >\r\n              <HiRefresh className=\"w-4 h-4 mr-2\" />\r\n              Refresh\r\n            </Button>\r\n          </div>\r\n          \r\n          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.studySets}</div>\r\n              <div className=\"text-sm text-gray-400\">Study Sets</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.flashcards}</div>\r\n              <div className=\"text-sm text-gray-400\">Flashcards</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.quizzes}</div>\r\n              <div className=\"text-sm text-gray-400\">Quizzes</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.documents}</div>\r\n              <div className=\"text-sm text-gray-400\">Documents</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary-400\">{dataStats.totalSize}</div>\r\n              <div className=\"text-sm text-gray-400\">Total Size</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Export Data */}\r\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\r\n          <div className=\"flex items-center space-x-3 mb-4\">\r\n            <HiDownload className=\"w-6 h-6 text-blue-400\" />\r\n            <h4 className=\"text-lg font-medium text-white\">Export Your Data</h4>\r\n          </div>\r\n          \r\n          <p className=\"text-gray-400 text-sm mb-4\">\r\n            Download a copy of your data in JSON format. You can use this to backup your data or import it into another account.\r\n          </p>\r\n\r\n          <div className=\"space-y-3 mb-6\">\r\n            <h5 className=\"font-medium text-white\">Select data to export:</h5>\r\n            {Object.entries(exportData).map(([key, value]) => (\r\n              <div key={key} className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm text-gray-300 capitalize\">\r\n                  {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                </label>\r\n                <button\r\n                  onClick={() => setExportData({ ...exportData, [key]: !value })}\r\n                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\r\n                    value ? 'bg-primary-500' : 'bg-gray-600'\r\n                  }`}\r\n                >\r\n                  <span\r\n                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                      value ? 'translate-x-6' : 'translate-x-1'\r\n                    }`}\r\n                  />\r\n                </button>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <Button\r\n            onClick={handleExportData}\r\n            isLoading={isExporting}\r\n            variant=\"primary\"\r\n          >\r\n            <HiDownload className=\"w-4 h-4 mr-2\" />\r\n            Export Data\r\n          </Button>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Clear Data */}\r\n        <div className=\"bg-red-500/10 rounded-lg p-6 border border-red-500/30\">\r\n          <div className=\"flex items-center space-x-3 mb-4\">\r\n            <HiTrash className=\"w-6 h-6 text-red-400\" />\r\n            <h4 className=\"text-lg font-medium text-white\">Clear Data</h4>\r\n          </div>\r\n          \r\n          <p className=\"text-gray-400 text-sm mb-6\">\r\n            Permanently delete specific types of data from your account. This action cannot be undone.\r\n          </p>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-3\">\r\n              <Button\r\n                onClick={() => handleClearData('study-sets')}\r\n                isLoading={isClearing}\r\n                variant=\"danger\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n              >\r\n                Clear All Study Sets\r\n              </Button>\r\n              <Button\r\n                onClick={() => handleClearData('flashcards')}\r\n                isLoading={isClearing}\r\n                variant=\"danger\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n              >\r\n                Clear All Flashcards\r\n              </Button>\r\n            </div>\r\n            <div className=\"space-y-3\">\r\n              <Button\r\n                onClick={() => handleClearData('quizzes')}\r\n                isLoading={isClearing}\r\n                variant=\"danger\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n              >\r\n                Clear All Quizzes\r\n              </Button>\r\n              <Button\r\n                onClick={() => handleClearData('analytics')}\r\n                isLoading={isClearing}\r\n                variant=\"danger\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n              >\r\n                Clear Analytics Data\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { \r\n  HiCreditCard, \r\n  HiDownload, \r\n  HiRefresh,\r\n  HiExclamationCircle,\r\n  HiCheckCircle,\r\n  HiClock,\r\n  HiX\r\n} from 'react-icons/hi';\r\nimport { Button } from '../common/Button';\r\n\r\ninterface PaymentMethod {\r\n  id: string;\r\n  type: 'card' | 'paypal';\r\n  last4?: string;\r\n  brand?: string;\r\n  expiryMonth?: number;\r\n  expiryYear?: number;\r\n  isDefault: boolean;\r\n}\r\n\r\ninterface Invoice {\r\n  id: string;\r\n  number: string;\r\n  amount: number;\r\n  currency: string;\r\n  status: 'paid' | 'pending' | 'failed' | 'draft';\r\n  date: string;\r\n  dueDate?: string;\r\n  downloadUrl?: string;\r\n  description: string;\r\n}\r\n\r\ninterface BillingData {\r\n  paymentMethods: PaymentMethod[];\r\n  invoices: Invoice[];\r\n  nextInvoice?: {\r\n    amount: number;\r\n    currency: string;\r\n    date: string;\r\n  };\r\n}\r\n\r\nexport const EnhancedBilling: React.FC = () => {\r\n  const [billingData, setBillingData] = useState<BillingData | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    fetchBillingData();\r\n  }, []);\r\n\r\n  const fetchBillingData = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/billing', {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch billing data');\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        setBillingData(result.data);\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load billing data');\r\n      // Set mock data for development\r\n      setBillingData({\r\n        paymentMethods: [\r\n          {\r\n            id: '1',\r\n            type: 'card',\r\n            last4: '4242',\r\n            brand: 'visa',\r\n            expiryMonth: 12,\r\n            expiryYear: 2025,\r\n            isDefault: true\r\n          }\r\n        ],\r\n        invoices: [\r\n          {\r\n            id: '1',\r\n            number: 'INV-001',\r\n            amount: 9.99,\r\n            currency: 'USD',\r\n            status: 'paid',\r\n            date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),\r\n            description: 'Pro Plan - Monthly'\r\n          },\r\n          {\r\n            id: '2',\r\n            number: 'INV-002',\r\n            amount: 9.99,\r\n            currency: 'USD',\r\n            status: 'pending',\r\n            date: new Date().toISOString(),\r\n            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n            description: 'Pro Plan - Monthly'\r\n          }\r\n        ],\r\n        nextInvoice: {\r\n          amount: 9.99,\r\n          currency: 'USD',\r\n          date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()\r\n        }\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDownloadInvoice = async (invoiceId: string) => {\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch(`/api/billing/invoices/${invoiceId}/download`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to download invoice');\r\n      }\r\n\r\n      const blob = await response.blob();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `invoice-${invoiceId}.pdf`;\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to download invoice');\r\n    }\r\n  };\r\n\r\n  const handleAddPaymentMethod = async () => {\r\n    // This would typically open a Stripe payment method setup flow\r\n    setError('Payment method management coming soon');\r\n  };\r\n\r\n  const handleSetDefaultPaymentMethod = async (paymentMethodId: string) => {\r\n    setIsUpdating(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/billing/payment-methods/default', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ paymentMethodId }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to update default payment method');\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        await fetchBillingData();\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to update payment method');\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status) {\r\n      case 'paid':\r\n        return <HiCheckCircle className=\"w-5 h-5 text-green-400\" />;\r\n      case 'pending':\r\n        return <HiClock className=\"w-5 h-5 text-yellow-400\" />;\r\n      case 'failed':\r\n        return <HiX className=\"w-5 h-5 text-red-400\" />;\r\n      default:\r\n        return <HiClock className=\"w-5 h-5 text-gray-400\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'paid':\r\n        return 'text-green-400';\r\n      case 'pending':\r\n        return 'text-yellow-400';\r\n      case 'failed':\r\n        return 'text-red-400';\r\n      default:\r\n        return 'text-gray-400';\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"animate-pulse\">\r\n          <div className=\"h-8 w-48 bg-gray-600 rounded mb-4\"></div>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"h-32 bg-gray-600 rounded-lg\"></div>\r\n            <div className=\"h-64 bg-gray-600 rounded-lg\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <h3 className=\"text-lg font-semibold text-white\">Billing & Invoices</h3>\r\n          <Button\r\n            onClick={fetchBillingData}\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n          >\r\n            <HiRefresh className=\"w-4 h-4 mr-2\" />\r\n            Refresh\r\n          </Button>\r\n        </div>\r\n        \r\n        {error && (\r\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n              <span className=\"text-red-400 font-medium\">Error</span>\r\n            </div>\r\n            <p className=\"text-red-300 mt-1\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Payment Methods */}\r\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h4 className=\"text-lg font-medium text-white\">Payment Methods</h4>\r\n            <Button\r\n              onClick={handleAddPaymentMethod}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n            >\r\n              Add Payment Method\r\n            </Button>\r\n          </div>\r\n\r\n          {billingData?.paymentMethods.length === 0 ? (\r\n            <div className=\"text-center py-8\">\r\n              <HiCreditCard className=\"w-16 h-16 text-gray-500 mx-auto mb-4\" />\r\n              <p className=\"text-gray-400\">No payment methods added</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-3\">\r\n              {billingData?.paymentMethods.map((method) => (\r\n                <div\r\n                  key={method.id}\r\n                  className=\"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary\"\r\n                >\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <HiCreditCard className=\"w-6 h-6 text-gray-400\" />\r\n                    <div>\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <span className=\"text-white font-medium\">\r\n                          {method.brand?.toUpperCase()} •••• {method.last4}\r\n                        </span>\r\n                        {method.isDefault && (\r\n                          <span className=\"bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium\">\r\n                            Default\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                      {method.expiryMonth && method.expiryYear && (\r\n                        <p className=\"text-gray-400 text-sm\">\r\n                          Expires {method.expiryMonth.toString().padStart(2, '0')}/{method.expiryYear}\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {!method.isDefault && (\r\n                    <Button\r\n                      onClick={() => handleSetDefaultPaymentMethod(method.id)}\r\n                      isLoading={isUpdating}\r\n                      variant=\"secondary\"\r\n                      size=\"sm\"\r\n                    >\r\n                      Set as Default\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Next Invoice */}\r\n        {billingData?.nextInvoice && (\r\n          <div className=\"bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6\">\r\n            <h4 className=\"text-lg font-medium text-white mb-2\">Upcoming Invoice</h4>\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <p className=\"text-blue-300\">\r\n                  ${billingData.nextInvoice.amount} {billingData.nextInvoice.currency.toUpperCase()}\r\n                </p>\r\n                <p className=\"text-blue-400 text-sm\">\r\n                  Due on {new Date(billingData.nextInvoice.date).toLocaleDateString()}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Invoice History */}\r\n        <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary\">\r\n          <h4 className=\"text-lg font-medium text-white mb-4\">Invoice History</h4>\r\n          \r\n          {billingData?.invoices.length === 0 ? (\r\n            <div className=\"text-center py-8\">\r\n              <p className=\"text-gray-400\">No invoices found</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-3\">\r\n              {billingData?.invoices.map((invoice) => (\r\n                <motion.div\r\n                  key={invoice.id}\r\n                  initial={{ opacity: 0, y: 10 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  className=\"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary\"\r\n                >\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    {getStatusIcon(invoice.status)}\r\n                    <div>\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <span className=\"text-white font-medium\">{invoice.number}</span>\r\n                        <span className={`text-sm font-medium ${getStatusColor(invoice.status)}`}>\r\n                          {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}\r\n                        </span>\r\n                      </div>\r\n                      <p className=\"text-gray-400 text-sm\">{invoice.description}</p>\r\n                      <p className=\"text-gray-500 text-xs\">\r\n                        {new Date(invoice.date).toLocaleDateString()}\r\n                        {invoice.dueDate && invoice.status === 'pending' && (\r\n                          <span> • Due {new Date(invoice.dueDate).toLocaleDateString()}</span>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <span className=\"text-white font-medium\">\r\n                      ${invoice.amount} {invoice.currency.toUpperCase()}\r\n                    </span>\r\n                    {invoice.status === 'paid' && (\r\n                      <Button\r\n                        onClick={() => handleDownloadInvoice(invoice.id)}\r\n                        variant=\"secondary\"\r\n                        size=\"sm\"\r\n                      >\r\n                        <HiDownload className=\"w-4 h-4 mr-2\" />\r\n                        Download\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON>xclamation<PERSON><PERSON>cle,\r\n  <PERSON><PERSON>heckCircle,\r\n  HiClipboard,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON>yeO<PERSON>,\r\n} from \"react-icons/hi\";\r\nimport { Button } from \"../common/Button\";\r\nimport { Input } from \"../common/Input\";\r\n\r\ninterface TwoFactorAuthProps {\r\n  enabled: boolean;\r\n  onToggle: (enabled: boolean) => void;\r\n}\r\n\r\ninterface SetupData {\r\n  qrCode: string;\r\n  secret: string;\r\n  backupCodes: string[];\r\n  factorId: string;\r\n}\r\n\r\nexport const TwoFactorAuth: React.FC<TwoFactorAuthProps> = ({\r\n  enabled,\r\n  onToggle,\r\n}) => {\r\n  const [isSetupMode, setIsSetupMode] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n  const [setupData, setSetupData] = useState<SetupData | null>(null);\r\n  const [verificationCode, setVerificationCode] = useState(\"\");\r\n  const [showSecret, setShowSecret] = useState(false);\r\n  const [showBackupCodes, setShowBackupCodes] = useState(false);\r\n\r\n  const handleEnable2FA = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      // Import supabase client\r\n      const { createClient } = await import(\"@supabase/supabase-js\");\r\n      const supabase = createClient(\r\n        process.env.REACT_APP_SUPABASE_URL!,\r\n        process.env.REACT_APP_SUPABASE_ANON_KEY!\r\n      );\r\n\r\n      // Enroll a new TOTP factor\r\n      const { data, error } = await supabase.auth.mfa.enroll({\r\n        factorType: \"totp\",\r\n        friendlyName: \"ChewyAI Authenticator\",\r\n      });\r\n\r\n      if (error) {\r\n        throw new Error(error.message);\r\n      }\r\n\r\n      // Extract the QR code and secret from the response\r\n      setSetupData({\r\n        qrCode: data.totp.qr_code,\r\n        secret: data.totp.secret,\r\n        backupCodes: [], // Supabase doesn't provide backup codes in this response\r\n        factorId: data.id,\r\n      });\r\n      setIsSetupMode(true);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : \"Failed to setup 2FA\");\r\n      // Mock data for development\r\n      setSetupData({\r\n        qrCode:\r\n          \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==\",\r\n        secret: \"JBSWY3DPEHPK3PXP\",\r\n        factorId: \"mock-factor-id\",\r\n        backupCodes: [\r\n          \"12345678\",\r\n          \"87654321\",\r\n          \"11111111\",\r\n          \"22222222\",\r\n          \"33333333\",\r\n          \"44444444\",\r\n          \"55555555\",\r\n          \"66666666\",\r\n        ],\r\n      });\r\n      setIsSetupMode(true);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleVerify2FA = async () => {\r\n    if (!verificationCode || verificationCode.length !== 6) {\r\n      setError(\"Please enter a valid 6-digit code\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Import supabase client\r\n      const { createClient } = await import(\"@supabase/supabase-js\");\r\n      const supabase = createClient(\r\n        process.env.REACT_APP_SUPABASE_URL!,\r\n        process.env.REACT_APP_SUPABASE_ANON_KEY!\r\n      );\r\n\r\n      // Get the factor ID from the setup data\r\n      if (!setupData) {\r\n        throw new Error(\"No setup data available\");\r\n      }\r\n\r\n      // Challenge and verify the TOTP factor\r\n      const { data: challengeData, error: challengeError } =\r\n        await supabase.auth.mfa.challenge({\r\n          factorId: setupData.factorId,\r\n        });\r\n\r\n      if (challengeError) {\r\n        throw new Error(challengeError.message);\r\n      }\r\n\r\n      const { error: verifyError } = await supabase.auth.mfa.verify({\r\n        factorId: setupData.factorId,\r\n        challengeId: challengeData.id,\r\n        code: verificationCode,\r\n      });\r\n\r\n      if (verifyError) {\r\n        throw new Error(verifyError.message);\r\n      }\r\n\r\n      setSuccess(\"Two-factor authentication enabled successfully!\");\r\n      setIsSetupMode(false);\r\n      setShowBackupCodes(true);\r\n      onToggle(true);\r\n    } catch (err) {\r\n      setError(\r\n        err instanceof Error ? err.message : \"Failed to verify 2FA code\"\r\n      );\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDisable2FA = async () => {\r\n    if (\r\n      !confirm(\r\n        \"Are you sure you want to disable two-factor authentication? This will make your account less secure.\"\r\n      )\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Import supabase client\r\n      const { createClient } = await import(\"@supabase/supabase-js\");\r\n      const supabase = createClient(\r\n        process.env.REACT_APP_SUPABASE_URL!,\r\n        process.env.REACT_APP_SUPABASE_ANON_KEY!\r\n      );\r\n\r\n      // List all factors and unenroll them\r\n      const { data: factorsData, error: factorsError } =\r\n        await supabase.auth.mfa.listFactors();\r\n\r\n      if (factorsError) {\r\n        throw new Error(factorsError.message);\r\n      }\r\n\r\n      // Unenroll all TOTP factors\r\n      for (const factor of factorsData.totp) {\r\n        const { error: unenrollError } = await supabase.auth.mfa.unenroll({\r\n          factorId: factor.id,\r\n        });\r\n\r\n        if (unenrollError) {\r\n          console.error(\"Failed to unenroll factor:\", unenrollError);\r\n        }\r\n      }\r\n\r\n      setSuccess(\"Two-factor authentication disabled successfully\");\r\n      onToggle(false);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : \"Failed to disable 2FA\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCopyToClipboard = (text: string) => {\r\n    navigator.clipboard.writeText(text);\r\n    setSuccess(\"Copied to clipboard!\");\r\n    setTimeout(() => setSuccess(null), 2000);\r\n  };\r\n\r\n  const handleCancelSetup = () => {\r\n    setIsSetupMode(false);\r\n    setSetupData(null);\r\n    setVerificationCode(\"\");\r\n    setError(null);\r\n    setSuccess(null);\r\n  };\r\n\r\n  if (isSetupMode && setupData) {\r\n    return (\r\n      <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary\">\r\n        <div className=\"flex items-center space-x-3 mb-6\">\r\n          <HiShieldCheck className=\"w-6 h-6 text-primary-400\" />\r\n          <h4 className=\"text-lg font-medium text-white\">\r\n            Setup Two-Factor Authentication\r\n          </h4>\r\n        </div>\r\n\r\n        {error && (\r\n          <div className=\"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiExclamationCircle className=\"w-4 h-4 text-red-400\" />\r\n              <span className=\"text-red-400 text-sm\">{error}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {success && (\r\n          <div className=\"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiCheckCircle className=\"w-4 h-4 text-green-400\" />\r\n              <span className=\"text-green-400 text-sm\">{success}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {!showBackupCodes ? (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <h5 className=\"font-medium text-white mb-2\">\r\n                Step 1: Scan QR Code\r\n              </h5>\r\n              <p className=\"text-gray-400 text-sm mb-4\">\r\n                Scan this QR code with your authenticator app (Google\r\n                Authenticator, Authy, etc.)\r\n              </p>\r\n              <div className=\"bg-white p-4 rounded-lg inline-block\">\r\n                <img\r\n                  src={setupData.qrCode}\r\n                  alt=\"2FA QR Code\"\r\n                  className=\"w-48 h-48\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h5 className=\"font-medium text-white mb-2\">\r\n                Step 2: Manual Entry (Alternative)\r\n              </h5>\r\n              <p className=\"text-gray-400 text-sm mb-3\">\r\n                If you can't scan the QR code, enter this secret key manually:\r\n              </p>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"flex-1 bg-background-secondary border border-border-primary rounded-lg p-3\">\r\n                  <code className=\"text-primary-400 font-mono\">\r\n                    {showSecret ? setupData.secret : \"••••••••••••••••\"}\r\n                  </code>\r\n                </div>\r\n                <Button\r\n                  onClick={() => setShowSecret(!showSecret)}\r\n                  variant=\"secondary\"\r\n                  size=\"sm\"\r\n                >\r\n                  {showSecret ? (\r\n                    <HiEyeOff className=\"w-4 h-4\" />\r\n                  ) : (\r\n                    <HiEye className=\"w-4 h-4\" />\r\n                  )}\r\n                </Button>\r\n                <Button\r\n                  onClick={() => handleCopyToClipboard(setupData.secret)}\r\n                  variant=\"secondary\"\r\n                  size=\"sm\"\r\n                >\r\n                  <HiClipboard className=\"w-4 h-4\" />\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h5 className=\"font-medium text-white mb-2\">\r\n                Step 3: Verify Setup\r\n              </h5>\r\n              <p className=\"text-gray-400 text-sm mb-3\">\r\n                Enter the 6-digit code from your authenticator app:\r\n              </p>\r\n              <div className=\"flex space-x-3\">\r\n                <Input\r\n                  value={verificationCode}\r\n                  onChange={setVerificationCode}\r\n                  placeholder=\"123456\"\r\n                  className=\"flex-1\"\r\n                />\r\n                <Button\r\n                  onClick={handleVerify2FA}\r\n                  isLoading={isLoading}\r\n                  disabled={verificationCode.length !== 6}\r\n                >\r\n                  Verify\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex space-x-3\">\r\n              <Button\r\n                onClick={handleCancelSetup}\r\n                variant=\"secondary\"\r\n                className=\"flex-1\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <HiCheckCircle className=\"w-16 h-16 text-green-400 mx-auto mb-4\" />\r\n              <h5 className=\"text-lg font-medium text-white mb-2\">\r\n                2FA Enabled Successfully!\r\n              </h5>\r\n              <p className=\"text-gray-400 text-sm\">\r\n                Your account is now protected with two-factor authentication.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4\">\r\n              <h5 className=\"font-medium text-yellow-400 mb-2\">\r\n                Important: Save Your Backup Codes\r\n              </h5>\r\n              <p className=\"text-yellow-300 text-sm mb-4\">\r\n                Store these backup codes in a safe place. You can use them to\r\n                access your account if you lose your authenticator device.\r\n              </p>\r\n              <div className=\"grid grid-cols-2 gap-2 mb-4\">\r\n                {setupData.backupCodes.map((code, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"bg-background-secondary border border-border-primary rounded p-2 text-center\"\r\n                  >\r\n                    <code className=\"text-primary-400 font-mono\">{code}</code>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <Button\r\n                onClick={() =>\r\n                  handleCopyToClipboard(setupData.backupCodes.join(\"\\n\"))\r\n                }\r\n                variant=\"secondary\"\r\n                size=\"sm\"\r\n              >\r\n                <HiClipboard className=\"w-4 h-4 mr-2\" />\r\n                Copy All Codes\r\n              </Button>\r\n            </div>\r\n\r\n            <Button\r\n              onClick={() => setShowBackupCodes(false)}\r\n              variant=\"primary\"\r\n              className=\"w-full\"\r\n            >\r\n              I've Saved My Backup Codes\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-background-tertiary rounded-lg p-6 border border-border-primary\">\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <HiShieldCheck\r\n            className={`w-6 h-6 ${\r\n              enabled ? \"text-green-400\" : \"text-gray-400\"\r\n            }`}\r\n          />\r\n          <div>\r\n            <h4 className=\"text-lg font-medium text-white\">\r\n              Two-Factor Authentication\r\n            </h4>\r\n            <p className=\"text-gray-400 text-sm\">\r\n              Add an extra layer of security to your account\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div\r\n          className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${\r\n            enabled\r\n              ? \"bg-green-500/20 text-green-400\"\r\n              : \"bg-gray-500/20 text-gray-400\"\r\n          }`}\r\n        >\r\n          {enabled ? \"Enabled\" : \"Disabled\"}\r\n        </div>\r\n      </div>\r\n\r\n      {error && (\r\n        <div className=\"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <HiExclamationCircle className=\"w-4 h-4 text-red-400\" />\r\n            <span className=\"text-red-400 text-sm\">{error}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {success && (\r\n        <div className=\"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <HiCheckCircle className=\"w-4 h-4 text-green-400\" />\r\n            <span className=\"text-green-400 text-sm\">{success}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-4\">\r\n        <p className=\"text-gray-300 text-sm\">\r\n          {enabled\r\n            ? \"Two-factor authentication is currently enabled for your account. You can disable it below if needed.\"\r\n            : \"Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy.\"}\r\n        </p>\r\n\r\n        <div className=\"flex space-x-3\">\r\n          {enabled ? (\r\n            <Button\r\n              onClick={handleDisable2FA}\r\n              isLoading={isLoading}\r\n              variant=\"danger\"\r\n            >\r\n              Disable 2FA\r\n            </Button>\r\n          ) : (\r\n            <Button\r\n              onClick={handleEnable2FA}\r\n              isLoading={isLoading}\r\n              variant=\"primary\"\r\n            >\r\n              <HiKey className=\"w-4 h-4 mr-2\" />\r\n              Enable 2FA\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from 'react';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-icons/hi';\r\nimport { Button } from '../common/Button';\r\nimport { Input } from '../common/Input';\r\n\r\ninterface ChangePasswordModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSuccess: () => void;\r\n}\r\n\r\nexport const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onSuccess\r\n}) => {\r\n  const [formData, setFormData] = useState({\r\n    currentPassword: '',\r\n    newPassword: '',\r\n    confirmPassword: ''\r\n  });\r\n  const [showPasswords, setShowPasswords] = useState({\r\n    current: false,\r\n    new: false,\r\n    confirm: false\r\n  });\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n\r\n  const handleInputChange = (field: string, value: string) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n    setError(null);\r\n  };\r\n\r\n  const handleChange = (field: string) => (value: string) => {\r\n    handleInputChange(field, value);\r\n  };\r\n\r\n  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {\r\n    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));\r\n  };\r\n\r\n  const validateForm = () => {\r\n    if (!formData.currentPassword) {\r\n      setError('Current password is required');\r\n      return false;\r\n    }\r\n    if (!formData.newPassword) {\r\n      setError('New password is required');\r\n      return false;\r\n    }\r\n    if (formData.newPassword.length < 8) {\r\n      setError('New password must be at least 8 characters long');\r\n      return false;\r\n    }\r\n    if (formData.newPassword !== formData.confirmPassword) {\r\n      setError('New passwords do not match');\r\n      return false;\r\n    }\r\n    if (formData.currentPassword === formData.newPassword) {\r\n      setError('New password must be different from current password');\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!validateForm()) return;\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch('/api/auth/change-password', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${token}`\r\n        },\r\n        body: JSON.stringify({\r\n          currentPassword: formData.currentPassword,\r\n          newPassword: formData.newPassword\r\n        })\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || 'Failed to change password');\r\n      }\r\n\r\n      setSuccess('Password changed successfully!');\r\n      setTimeout(() => {\r\n        onSuccess();\r\n        onClose();\r\n        resetForm();\r\n      }, 1500);\r\n\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to change password');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setFormData({\r\n      currentPassword: '',\r\n      newPassword: '',\r\n      confirmPassword: ''\r\n    });\r\n    setShowPasswords({\r\n      current: false,\r\n      new: false,\r\n      confirm: false\r\n    });\r\n    setError(null);\r\n    setSuccess(null);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    resetForm();\r\n    onClose();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-background-secondary rounded-lg border border-border-primary w-full max-w-md\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-border-primary\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <HiKey className=\"w-5 h-5 text-primary-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">Change Password</h3>\r\n          </div>\r\n          <button\r\n            onClick={handleClose}\r\n            className=\"text-gray-400 hover:text-white transition-colors\"\r\n          >\r\n            <HiX className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\r\n          {/* Current Password */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n              Current Password\r\n            </label>\r\n            <div className=\"relative\">\r\n              <Input\r\n                type={showPasswords.current ? 'text' : 'password'}\r\n                value={formData.currentPassword}\r\n                onChange={handleChange('currentPassword')}\r\n                placeholder=\"Enter your current password\"\r\n                className=\"pr-10\"\r\n                disabled={isLoading}\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => togglePasswordVisibility('current')}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white\"\r\n              >\r\n                {showPasswords.current ? <HiEyeOff className=\"w-4 h-4\" /> : <HiEye className=\"w-4 h-4\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* New Password */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n              New Password\r\n            </label>\r\n            <div className=\"relative\">\r\n              <Input\r\n                type={showPasswords.new ? 'text' : 'password'}\r\n                value={formData.newPassword}\r\n                onChange={handleChange('newPassword')}\r\n                placeholder=\"Enter your new password\"\r\n                className=\"pr-10\"\r\n                disabled={isLoading}\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => togglePasswordVisibility('new')}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white\"\r\n              >\r\n                {showPasswords.new ? <HiEyeOff className=\"w-4 h-4\" /> : <HiEye className=\"w-4 h-4\" />}\r\n              </button>\r\n            </div>\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Password must be at least 8 characters long\r\n            </p>\r\n          </div>\r\n\r\n          {/* Confirm Password */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n              Confirm New Password\r\n            </label>\r\n            <div className=\"relative\">\r\n              <Input\r\n                type={showPasswords.confirm ? 'text' : 'password'}\r\n                value={formData.confirmPassword}\r\n                onChange={handleChange('confirmPassword')}\r\n                placeholder=\"Confirm your new password\"\r\n                className=\"pr-10\"\r\n                disabled={isLoading}\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => togglePasswordVisibility('confirm')}\r\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white\"\r\n              >\r\n                {showPasswords.confirm ? <HiEyeOff className=\"w-4 h-4\" /> : <HiEye className=\"w-4 h-4\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Error/Success Messages */}\r\n          {error && (\r\n            <div className=\"bg-red-500/10 border border-red-500/30 rounded-lg p-3\">\r\n              <p className=\"text-red-400 text-sm\">{error}</p>\r\n            </div>\r\n          )}\r\n\r\n          {success && (\r\n            <div className=\"bg-green-500/10 border border-green-500/30 rounded-lg p-3\">\r\n              <p className=\"text-green-400 text-sm\">{success}</p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex space-x-3 pt-4\">\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"secondary\"\r\n              onClick={handleClose}\r\n              disabled={isLoading}\r\n              className=\"flex-1\"\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              variant=\"primary\"\r\n              isLoading={isLoading}\r\n              disabled={isLoading}\r\n              className=\"flex-1\"\r\n            >\r\n              {isLoading ? 'Changing...' : 'Change Password'}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from 'react';\r\nimport { HiX, HiExclamationCircle, HiTrash } from 'react-icons/hi';\r\nimport { Button } from '../common/Button';\r\nimport { Input } from '../common/Input';\r\n\r\ninterface DangerZoneModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  action: 'deactivate' | 'delete';\r\n}\r\n\r\nexport const DangerZoneModal: React.FC<DangerZoneModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  action\r\n}) => {\r\n  const [confirmationText, setConfirmationText] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [step, setStep] = useState<'confirm' | 'final'>('confirm');\r\n\r\n  const actionConfig = {\r\n    deactivate: {\r\n      title: 'Deactivate Account',\r\n      description: 'Your account will be temporarily disabled. You can reactivate it by logging in again.',\r\n      confirmText: 'DEACTIVATE',\r\n      buttonText: 'Deactivate Account',\r\n      warningText: 'This will temporarily disable your account and log you out.',\r\n      endpoint: '/api/auth/deactivate-account'\r\n    },\r\n    delete: {\r\n      title: 'Delete Account',\r\n      description: 'This will permanently delete your account and all associated data. This action cannot be undone.',\r\n      confirmText: 'DELETE FOREVER',\r\n      buttonText: 'Delete Account Forever',\r\n      warningText: 'This will permanently delete all your data including study sets, flashcards, progress, and subscription information.',\r\n      endpoint: '/api/auth/delete-account'\r\n    }\r\n  };\r\n\r\n  const config = actionConfig[action];\r\n  const isConfirmationValid = confirmationText === config.confirmText;\r\n\r\n  const handleSubmit = async () => {\r\n    if (step === 'confirm') {\r\n      setStep('final');\r\n      return;\r\n    }\r\n\r\n    if (!isConfirmationValid) {\r\n      setError(`Please type \"${config.confirmText}\" to confirm`);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem('auth_token');\r\n      const response = await fetch(config.endpoint, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${token}`\r\n        },\r\n        body: JSON.stringify({\r\n          confirmation: confirmationText\r\n        })\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || `Failed to ${action} account`);\r\n      }\r\n\r\n      // For both actions, redirect to login page\r\n      localStorage.removeItem('auth_token');\r\n      localStorage.removeItem('user_data');\r\n      window.location.href = '/login';\r\n\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : `Failed to ${action} account`);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setConfirmationText('');\r\n    setError(null);\r\n    setStep('confirm');\r\n    onClose();\r\n  };\r\n\r\n  const handleBack = () => {\r\n    setStep('confirm');\r\n    setError(null);\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-background-secondary rounded-lg border border-red-500/30 w-full max-w-md\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-red-500/30\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n            <h3 className=\"text-lg font-semibold text-white\">{config.title}</h3>\r\n          </div>\r\n          <button\r\n            onClick={handleClose}\r\n            className=\"text-gray-400 hover:text-white transition-colors\"\r\n          >\r\n            <HiX className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"p-6\">\r\n          {step === 'confirm' ? (\r\n            <div className=\"space-y-4\">\r\n              <div className=\"bg-red-500/10 border border-red-500/30 rounded-lg p-4\">\r\n                <div className=\"flex items-start space-x-3\">\r\n                  <HiExclamationCircle className=\"w-5 h-5 text-red-400 mt-0.5 flex-shrink-0\" />\r\n                  <div>\r\n                    <h4 className=\"font-medium text-red-400 mb-2\">Warning</h4>\r\n                    <p className=\"text-gray-300 text-sm\">{config.warningText}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <p className=\"text-gray-300 text-sm mb-4\">{config.description}</p>\r\n                \r\n                {action === 'delete' && (\r\n                  <div className=\"space-y-2 text-sm text-gray-400\">\r\n                    <p>This will delete:</p>\r\n                    <ul className=\"list-disc list-inside space-y-1 ml-4\">\r\n                      <li>All study sets and flashcards</li>\r\n                      <li>Quiz history and progress</li>\r\n                      <li>Account settings and preferences</li>\r\n                      <li>Subscription and billing information</li>\r\n                      <li>All uploaded documents</li>\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"flex space-x-3 pt-4\">\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  onClick={handleClose}\r\n                  className=\"flex-1\"\r\n                >\r\n                  Cancel\r\n                </Button>\r\n                <Button\r\n                  variant=\"danger\"\r\n                  onClick={handleSubmit}\r\n                  className=\"flex-1\"\r\n                >\r\n                  Continue\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-4\">\r\n              <div className=\"bg-red-500/10 border border-red-500/30 rounded-lg p-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <HiTrash className=\"w-5 h-5 text-red-400\" />\r\n                  <h4 className=\"font-medium text-red-400\">Final Confirmation</h4>\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <p className=\"text-gray-300 text-sm mb-4\">\r\n                  To confirm this action, please type <span className=\"font-mono font-bold text-red-400\">{config.confirmText}</span> in the box below:\r\n                </p>\r\n                \r\n                <Input\r\n                  type=\"text\"\r\n                  value={confirmationText}\r\n                  onChange={(value) => {\r\n                    setConfirmationText(value);\r\n                    setError(null);\r\n                  }}\r\n                  placeholder={config.confirmText}\r\n                  className=\"font-mono\"\r\n                  disabled={isLoading}\r\n                />\r\n              </div>\r\n\r\n              {error && (\r\n                <div className=\"bg-red-500/10 border border-red-500/30 rounded-lg p-3\">\r\n                  <p className=\"text-red-400 text-sm\">{error}</p>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex space-x-3 pt-4\">\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  onClick={handleBack}\r\n                  disabled={isLoading}\r\n                  className=\"flex-1\"\r\n                >\r\n                  Back\r\n                </Button>\r\n                <Button\r\n                  variant=\"danger\"\r\n                  onClick={handleSubmit}\r\n                  isLoading={isLoading}\r\n                  disabled={isLoading || !isConfirmationValid}\r\n                  className=\"flex-1\"\r\n                >\r\n                  {isLoading ? 'Processing...' : config.buttonText}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>eld<PERSON>he<PERSON>,\r\n  HiCreditCard,\r\n  HiMoon,\r\n  HiSun,\r\n  HiKey,\r\n  HiUpload,\r\n  HiDatabase,\r\n  HiExclamationCircle,\r\n  HiCheckCircle,\r\n  HiLogout,\r\n} from \"react-icons/hi\";\r\nimport { Button } from \"../components/common/Button\";\r\nimport { Input } from \"../components/common/Input\";\r\nimport { useAuthStore } from \"../stores/authStore\";\r\nimport { SubscriptionManagement } from \"../components/settings/SubscriptionManagement\";\r\nimport { DataManagement } from \"../components/settings/DataManagement\";\r\nimport { EnhancedBilling } from \"../components/settings/EnhancedBilling\";\r\nimport { TwoFactorAuth } from \"../components/settings/TwoFactorAuth\";\r\nimport { ChangePasswordModal } from \"../components/settings/ChangePasswordModal\";\r\nimport { DangerZoneModal } from \"../components/settings/DangerZoneModal\";\r\nimport { DifficultyLevel } from \"../shared/types\";\r\nimport { DifficultySelector } from \"../components/common/DifficultySelector\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\ninterface SettingsSection {\r\n  id: string;\r\n  label: string;\r\n  icon: React.ComponentType<{ className?: string }>;\r\n  description: string;\r\n}\r\n\r\nconst settingsSections: SettingsSection[] = [\r\n  {\r\n    id: \"profile\",\r\n    label: \"Profile\",\r\n    icon: HiUser,\r\n    description: \"Manage your account information\",\r\n  },\r\n  {\r\n    id: \"preferences\",\r\n    label: \"Preferences\",\r\n    icon: HiCog,\r\n    description: \"Customize your experience\",\r\n  },\r\n  {\r\n    id: \"notifications\",\r\n    label: \"Notifications\",\r\n    icon: HiBell,\r\n    description: \"Control notification settings\",\r\n  },\r\n  {\r\n    id: \"security\",\r\n    label: \"Security\",\r\n    icon: HiShieldCheck,\r\n    description: \"Password and security settings\",\r\n  },\r\n  {\r\n    id: \"subscription\",\r\n    label: \"Subscription\",\r\n    icon: HiCreditCard,\r\n    description: \"Manage your subscription plan\",\r\n  },\r\n  {\r\n    id: \"billing\",\r\n    label: \"Billing\",\r\n    icon: HiCreditCard,\r\n    description: \"Payment history and invoices\",\r\n  },\r\n  {\r\n    id: \"data\",\r\n    label: \"Data Management\",\r\n    icon: HiDatabase,\r\n    description: \"Export, import, and manage your data\",\r\n  },\r\n];\r\n\r\nexport const SettingsPage: React.FC = () => {\r\n  const [activeSection, setActiveSection] = useState(\"profile\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n  const { user, logout } = useAuthStore();\r\n  const navigate = useNavigate();\r\n\r\n  // Form states\r\n  const [profileData, setProfileData] = useState({\r\n    name: user?.name || \"\",\r\n    email: user?.email || \"\",\r\n    bio: \"\",\r\n    avatar: null as File | null,\r\n  });\r\n\r\n  const [preferences, setPreferences] = useState({\r\n    theme: \"dark\",\r\n    language: \"en\",\r\n    studyReminders: true,\r\n    autoSave: true,\r\n    defaultStudyMode: \"flashcards\" as \"flashcards\" | \"quiz\",\r\n    sessionDuration: 30,\r\n    difficultyLevel: DifficultyLevel.MEDIUM,\r\n  });\r\n\r\n  const [notifications, setNotifications] = useState({\r\n    emailNotifications: true,\r\n    studyReminders: true,\r\n    weeklyProgress: false,\r\n    marketingEmails: false,\r\n    achievementNotifications: true,\r\n    streakReminders: true,\r\n  });\r\n\r\n  const [securitySettings, setSecuritySettings] = useState({\r\n    twoFactorEnabled: false,\r\n    loginNotifications: true,\r\n    sessionTimeout: 30,\r\n  });\r\n\r\n  // Modal states\r\n  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =\r\n    useState(false);\r\n  const [isDangerZoneModalOpen, setIsDangerZoneModalOpen] = useState(false);\r\n  const [dangerZoneAction, setDangerZoneAction] = useState<\r\n    \"deactivate\" | \"delete\"\r\n  >(\"deactivate\");\r\n\r\n  // Load user settings on component mount\r\n  useEffect(() => {\r\n    const loadUserSettings = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n\r\n        const token = localStorage.getItem(\"auth_token\");\r\n        if (!token) {\r\n          setError(\"Authentication required\");\r\n          return;\r\n        }\r\n\r\n        // Fetch user preferences from API\r\n        const response = await fetch(\"/api/user/preferences\", {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to load user preferences\");\r\n        }\r\n\r\n        const result = await response.json();\r\n        if (result.success && result.data) {\r\n          // Convert snake_case from backend to camelCase for frontend\r\n          const backendPrefs = result.data;\r\n          setPreferences({\r\n            theme: backendPrefs.theme,\r\n            language: backendPrefs.language,\r\n            studyReminders: backendPrefs.study_reminders,\r\n            autoSave: backendPrefs.auto_save,\r\n            defaultStudyMode: backendPrefs.default_study_mode,\r\n            sessionDuration: backendPrefs.session_duration,\r\n            difficultyLevel: backendPrefs.difficulty_level,\r\n          });\r\n        }\r\n      } catch (err) {\r\n        setError(\"Failed to load user settings\");\r\n        console.error(\"Load user settings error:\", err);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    loadUserSettings();\r\n  }, []);\r\n\r\n  const handleSaveProfile = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(\"name\", profileData.name);\r\n      formData.append(\"bio\", profileData.bio);\r\n      if (profileData.avatar) {\r\n        formData.append(\"avatar\", profileData.avatar);\r\n      }\r\n\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(\"/api/user/profile\", {\r\n        method: \"PUT\",\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to update profile\");\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        setSuccess(\"Profile updated successfully!\");\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : \"Failed to update profile\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSavePreferences = async () => {\r\n    // Guard against duplicate submissions\r\n    if (isLoading) return;\r\n    setIsLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      // Abort if user is not authenticated to avoid unauthenticated rate-limited requests\r\n      if (!token) {\r\n        setError(\"Authentication required\");\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Convert camelCase to snake_case for backend API\r\n      const backendPreferences = {\r\n        theme: preferences.theme,\r\n        language: preferences.language,\r\n        study_reminders: preferences.studyReminders,\r\n        auto_save: preferences.autoSave,\r\n        default_study_mode: preferences.defaultStudyMode,\r\n        session_duration: preferences.sessionDuration,\r\n        difficulty_level: preferences.difficultyLevel,\r\n      };\r\n\r\n      const response = await fetch(\"/api/user/preferences\", {\r\n        method: \"PUT\",\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(backendPreferences),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to update preferences\");\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        setSuccess(\"Preferences updated successfully!\");\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(\r\n        err instanceof Error ? err.message : \"Failed to update preferences\"\r\n      );\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSaveNotifications = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(\"/api/user/notifications\", {\r\n        method: \"PUT\",\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(notifications),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to update notification settings\");\r\n      }\r\n\r\n      const result = await response.json();\r\n      if (result.success) {\r\n        setSuccess(\"Notification settings updated successfully!\");\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (err) {\r\n      setError(\r\n        err instanceof Error\r\n          ? err.message\r\n          : \"Failed to update notification settings\"\r\n      );\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      await logout();\r\n      navigate(\"/login\");\r\n    } catch (error) {\r\n      setError(\"Failed to logout. Please try again.\");\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const renderProfileSection = () => (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\r\n          Profile Information\r\n        </h3>\r\n\r\n        {/* Avatar Upload */}\r\n        <div className=\"mb-6\">\r\n          <label className=\"block text-sm font-medium text-gray-300 mb-3\">\r\n            Profile Picture\r\n          </label>\r\n          <div className=\"flex items-center space-x-4\">\r\n            <div className=\"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold\">\r\n              {user?.name?.charAt(0)?.toUpperCase() || \"U\"}\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <Button\r\n                variant=\"secondary\"\r\n                size=\"sm\"\r\n                onClick={() => {\r\n                  const input = document.createElement(\"input\");\r\n                  input.type = \"file\";\r\n                  input.accept = \"image/*\";\r\n                  input.onchange = (e) => {\r\n                    const file = (e.target as HTMLInputElement).files?.[0];\r\n                    if (file) {\r\n                      setProfileData({ ...profileData, avatar: file });\r\n                    }\r\n                  };\r\n                  input.click();\r\n                }}\r\n              >\r\n                <HiUpload className=\"w-4 h-4 mr-2\" />\r\n                Upload Photo\r\n              </Button>\r\n              <p className=\"text-xs text-gray-500\">JPG, PNG up to 5MB</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          <Input\r\n            label=\"Full Name\"\r\n            value={profileData.name}\r\n            onChange={(value) =>\r\n              setProfileData({ ...profileData, name: value })\r\n            }\r\n            placeholder=\"Enter your full name\"\r\n          />\r\n          <Input\r\n            label=\"Email Address\"\r\n            type=\"email\"\r\n            value={profileData.email}\r\n            onChange={(value) =>\r\n              setProfileData({ ...profileData, email: value })\r\n            }\r\n            placeholder=\"Enter your email\"\r\n            disabled\r\n          />\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n              Bio (Optional)\r\n            </label>\r\n            <textarea\r\n              value={profileData.bio}\r\n              onChange={(e) =>\r\n                setProfileData({ ...profileData, bio: e.target.value })\r\n              }\r\n              placeholder=\"Tell us about yourself...\"\r\n              rows={4}\r\n              className=\"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div className=\"mt-6\">\r\n          <Button onClick={handleSaveProfile} isLoading={isLoading}>\r\n            Save Profile\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderPreferencesSection = () => (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\r\n          App Preferences\r\n        </h3>\r\n        <div className=\"space-y-6\">\r\n          {/* Theme Selection */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-3\">\r\n              Theme\r\n            </label>\r\n            <div className=\"flex space-x-4\">\r\n              <button\r\n                onClick={() =>\r\n                  setPreferences({ ...preferences, theme: \"dark\" })\r\n                }\r\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\r\n                  preferences.theme === \"dark\"\r\n                    ? \"border-primary-500 bg-primary-500/20 text-primary-400\"\r\n                    : \"border-gray-600 text-gray-300 hover:border-gray-500\"\r\n                }`}\r\n              >\r\n                <HiMoon className=\"w-4 h-4\" />\r\n                <span>Dark</span>\r\n              </button>\r\n              <button\r\n                onClick={() =>\r\n                  setPreferences({ ...preferences, theme: \"light\" })\r\n                }\r\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\r\n                  preferences.theme === \"light\"\r\n                    ? \"border-primary-500 bg-primary-500/20 text-primary-400\"\r\n                    : \"border-gray-600 text-gray-300 hover:border-gray-500\"\r\n                }`}\r\n                disabled\r\n              >\r\n                <HiSun className=\"w-4 h-4\" />\r\n                <span>Light (Coming Soon)</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Language Selection */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-3\">\r\n              Language\r\n            </label>\r\n            <select\r\n              value={preferences.language}\r\n              onChange={(e) =>\r\n                setPreferences({ ...preferences, language: e.target.value })\r\n              }\r\n              className=\"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n            >\r\n              <option value=\"en\">English</option>\r\n              <option value=\"es\" disabled>\r\n                Spanish (Coming Soon)\r\n              </option>\r\n              <option value=\"fr\" disabled>\r\n                French (Coming Soon)\r\n              </option>\r\n            </select>\r\n          </div>\r\n\r\n          {/* Study Preferences */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-3\">\r\n              Default Study Mode\r\n            </label>\r\n            <div className=\"flex space-x-4\">\r\n              <button\r\n                onClick={() =>\r\n                  setPreferences({\r\n                    ...preferences,\r\n                    defaultStudyMode: \"flashcards\",\r\n                  })\r\n                }\r\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\r\n                  preferences.defaultStudyMode === \"flashcards\"\r\n                    ? \"border-primary-500 bg-primary-500/20 text-primary-400\"\r\n                    : \"border-gray-600 text-gray-300 hover:border-gray-500\"\r\n                }`}\r\n              >\r\n                <span>Flashcards</span>\r\n              </button>\r\n              <button\r\n                onClick={() =>\r\n                  setPreferences({ ...preferences, defaultStudyMode: \"quiz\" })\r\n                }\r\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${\r\n                  preferences.defaultStudyMode === \"quiz\"\r\n                    ? \"border-primary-500 bg-primary-500/20 text-primary-400\"\r\n                    : \"border-gray-600 text-gray-300 hover:border-gray-500\"\r\n                }`}\r\n              >\r\n                <span>Quiz</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Session Duration */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-300 mb-3\">\r\n              Default Session Duration (minutes)\r\n            </label>\r\n            <select\r\n              value={preferences.sessionDuration}\r\n              onChange={(e) =>\r\n                setPreferences({\r\n                  ...preferences,\r\n                  sessionDuration: parseInt(e.target.value),\r\n                })\r\n              }\r\n              className=\"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n            >\r\n              <option value={15}>15 minutes</option>\r\n              <option value={30}>30 minutes</option>\r\n              <option value={45}>45 minutes</option>\r\n              <option value={60}>1 hour</option>\r\n              <option value={90}>1.5 hours</option>\r\n            </select>\r\n          </div>\r\n\r\n          {/* Difficulty Level */}\r\n          <DifficultySelector\r\n            value={preferences.difficultyLevel}\r\n            onChange={(level) =>\r\n              setPreferences({ ...preferences, difficultyLevel: level })\r\n            }\r\n            label=\"Default Difficulty Level\"\r\n            className=\"bg-background-secondary rounded-lg p-4\"\r\n          />\r\n\r\n          {/* Toggle Settings */}\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-300\">\r\n                  Study Reminders\r\n                </label>\r\n                <p className=\"text-xs text-gray-500\">\r\n                  Get reminded to study regularly\r\n                </p>\r\n              </div>\r\n              <button\r\n                onClick={() =>\r\n                  setPreferences({\r\n                    ...preferences,\r\n                    studyReminders: !preferences.studyReminders,\r\n                  })\r\n                }\r\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\r\n                  preferences.studyReminders ? \"bg-primary-500\" : \"bg-gray-600\"\r\n                }`}\r\n              >\r\n                <span\r\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                    preferences.studyReminders\r\n                      ? \"translate-x-6\"\r\n                      : \"translate-x-1\"\r\n                  }`}\r\n                />\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-300\">\r\n                  Auto-save\r\n                </label>\r\n                <p className=\"text-xs text-gray-500\">\r\n                  Automatically save your progress\r\n                </p>\r\n              </div>\r\n              <button\r\n                onClick={() =>\r\n                  setPreferences({\r\n                    ...preferences,\r\n                    autoSave: !preferences.autoSave,\r\n                  })\r\n                }\r\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\r\n                  preferences.autoSave ? \"bg-primary-500\" : \"bg-gray-600\"\r\n                }`}\r\n              >\r\n                <span\r\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                    preferences.autoSave ? \"translate-x-6\" : \"translate-x-1\"\r\n                  }`}\r\n                />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"mt-6\">\r\n          <Button onClick={handleSavePreferences} isLoading={isLoading}>\r\n            Save Preferences\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderNotificationsSection = () => (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\r\n          Notification Settings\r\n        </h3>\r\n        <div className=\"space-y-4\">\r\n          {Object.entries(notifications).map(([key, value]) => (\r\n            <div key={key} className=\"flex items-center justify-between\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-300 capitalize\">\r\n                  {key.replace(/([A-Z])/g, \" $1\").trim()}\r\n                </label>\r\n                <p className=\"text-xs text-gray-500\">\r\n                  {key === \"emailNotifications\" &&\r\n                    \"Receive important updates via email\"}\r\n                  {key === \"studyReminders\" &&\r\n                    \"Get reminded when it's time to study\"}\r\n                  {key === \"weeklyProgress\" &&\r\n                    \"Weekly summary of your study progress\"}\r\n                  {key === \"marketingEmails\" && \"Product updates and tips\"}\r\n                  {key === \"achievementNotifications\" &&\r\n                    \"Get notified when you unlock achievements\"}\r\n                  {key === \"streakReminders\" &&\r\n                    \"Reminders to maintain your study streak\"}\r\n                </p>\r\n              </div>\r\n              <button\r\n                onClick={() =>\r\n                  setNotifications({ ...notifications, [key]: !value })\r\n                }\r\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\r\n                  value ? \"bg-primary-500\" : \"bg-gray-600\"\r\n                }`}\r\n              >\r\n                <span\r\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                    value ? \"translate-x-6\" : \"translate-x-1\"\r\n                  }`}\r\n                />\r\n              </button>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div className=\"mt-6\">\r\n          <Button onClick={handleSaveNotifications} isLoading={isLoading}>\r\n            Save Notification Settings\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderSecuritySection = () => (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">\r\n          Security Settings\r\n        </h3>\r\n        <div className=\"space-y-6\">\r\n          {/* Two-Factor Authentication */}\r\n          <TwoFactorAuth\r\n            enabled={securitySettings.twoFactorEnabled}\r\n            onToggle={(enabled) =>\r\n              setSecuritySettings({\r\n                ...securitySettings,\r\n                twoFactorEnabled: enabled,\r\n              })\r\n            }\r\n          />\r\n\r\n          {/* Session Settings */}\r\n          <div className=\"bg-background-tertiary rounded-lg p-4 border border-border-primary\">\r\n            <h4 className=\"font-medium text-white mb-4\">Session Management</h4>\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <label className=\"text-sm font-medium text-gray-300\">\r\n                    Login Notifications\r\n                  </label>\r\n                  <p className=\"text-xs text-gray-500\">\r\n                    Get notified when someone logs into your account\r\n                  </p>\r\n                </div>\r\n                <button\r\n                  onClick={() =>\r\n                    setSecuritySettings({\r\n                      ...securitySettings,\r\n                      loginNotifications: !securitySettings.loginNotifications,\r\n                    })\r\n                  }\r\n                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\r\n                    securitySettings.loginNotifications\r\n                      ? \"bg-primary-500\"\r\n                      : \"bg-gray-600\"\r\n                  }`}\r\n                >\r\n                  <span\r\n                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                      securitySettings.loginNotifications\r\n                        ? \"translate-x-6\"\r\n                        : \"translate-x-1\"\r\n                    }`}\r\n                  />\r\n                </button>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\r\n                  Session Timeout (minutes)\r\n                </label>\r\n                <select\r\n                  value={securitySettings.sessionTimeout}\r\n                  onChange={(e) =>\r\n                    setSecuritySettings({\r\n                      ...securitySettings,\r\n                      sessionTimeout: parseInt(e.target.value),\r\n                    })\r\n                  }\r\n                  className=\"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n                >\r\n                  <option value={15}>15 minutes</option>\r\n                  <option value={30}>30 minutes</option>\r\n                  <option value={60}>1 hour</option>\r\n                  <option value={120}>2 hours</option>\r\n                  <option value={480}>8 hours</option>\r\n                  <option value={1440}>1 day</option>\r\n                  <option value={10080}>1 week</option>\r\n                  <option value={20160}>2 weeks</option>\r\n                  <option value={30240}>3 weeks</option>\r\n                  <option value={40320}>4 weeks</option>\r\n                  <option value={50400}>5 weeks</option>\r\n                  <option value={60480}>6 weeks</option>\r\n                  <option value={70560}>7 weeks</option>\r\n                  <option value={80640}>8 weeks</option>\r\n                  <option value={0}>Never expire</option>\r\n                </select>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Password Change */}\r\n          <div className=\"bg-background-tertiary rounded-lg p-4 border border-border-primary\">\r\n            <div className=\"flex items-center space-x-3 mb-3\">\r\n              <HiKey className=\"w-5 h-5 text-primary-400\" />\r\n              <h4 className=\"font-medium text-white\">Change Password</h4>\r\n            </div>\r\n            <p className=\"text-gray-400 text-sm mb-4\">\r\n              Update your password to keep your account secure\r\n            </p>\r\n            <Button\r\n              variant=\"secondary\"\r\n              onClick={() => setIsChangePasswordModalOpen(true)}\r\n            >\r\n              Change Password\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Logout Section */}\r\n          <div className=\"bg-background-tertiary rounded-lg p-4 border border-border-primary\">\r\n            <div className=\"flex items-center space-x-3 mb-3\">\r\n              <HiLogout className=\"w-5 h-5 text-orange-400\" />\r\n              <h4 className=\"font-medium text-white\">Sign Out</h4>\r\n            </div>\r\n            <p className=\"text-gray-400 text-sm mb-4\">\r\n              Sign out of your account on this device\r\n            </p>\r\n            <Button\r\n              variant=\"secondary\"\r\n              onClick={handleLogout}\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? \"Signing Out...\" : \"Sign Out\"}\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Account Deletion */}\r\n          <div className=\"bg-red-500/10 rounded-lg p-4 border border-red-500/30\">\r\n            <div className=\"flex items-center space-x-3 mb-3\">\r\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n              <h4 className=\"font-medium text-white\">Danger Zone</h4>\r\n            </div>\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <h5 className=\"font-medium text-red-400 mb-2\">\r\n                  Account Deactivation\r\n                </h5>\r\n                <p className=\"text-gray-400 text-sm mb-3\">\r\n                  Temporarily deactivate your account. You can reactivate it\r\n                  later.\r\n                </p>\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    setDangerZoneAction(\"deactivate\");\r\n                    setIsDangerZoneModalOpen(true);\r\n                  }}\r\n                >\r\n                  Deactivate Account\r\n                </Button>\r\n              </div>\r\n\r\n              <div>\r\n                <h5 className=\"font-medium text-red-400 mb-2\">\r\n                  Account Deletion\r\n                </h5>\r\n                <p className=\"text-gray-400 text-sm mb-3\">\r\n                  Permanently delete your account and all associated data. This\r\n                  action cannot be undone.\r\n                </p>\r\n                <Button\r\n                  variant=\"danger\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    setDangerZoneAction(\"delete\");\r\n                    setIsDangerZoneModalOpen(true);\r\n                  }}\r\n                >\r\n                  Delete Account\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderSubscriptionSection = () => <SubscriptionManagement />;\r\n\r\n  const renderBillingSection = () => <EnhancedBilling />;\r\n\r\n  const renderDataSection = () => <DataManagement />;\r\n\r\n  const renderContent = () => {\r\n    switch (activeSection) {\r\n      case \"profile\":\r\n        return renderProfileSection();\r\n      case \"preferences\":\r\n        return renderPreferencesSection();\r\n      case \"notifications\":\r\n        return renderNotificationsSection();\r\n      case \"security\":\r\n        return renderSecuritySection();\r\n      case \"subscription\":\r\n        return renderSubscriptionSection();\r\n      case \"billing\":\r\n        return renderBillingSection();\r\n      case \"data\":\r\n        return renderDataSection();\r\n      default:\r\n        return renderProfileSection();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background-primary text-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header */}\r\n        <div className=\"mb-8\">\r\n          <h1 className=\"text-3xl font-bold text-white mb-2\">Settings</h1>\r\n          <p className=\"text-gray-400\">Manage your account and preferences</p>\r\n        </div>\r\n\r\n        {/* Error and Success Messages */}\r\n        {error && (\r\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n              <span className=\"text-red-400 font-medium\">Error</span>\r\n            </div>\r\n            <p className=\"text-red-300 mt-1\">{error}</p>\r\n            <Button\r\n              onClick={() => setError(null)}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n              className=\"mt-2\"\r\n            >\r\n              Dismiss\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        {success && (\r\n          <div className=\"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiCheckCircle className=\"w-5 h-5 text-green-400\" />\r\n              <span className=\"text-green-400 font-medium\">Success</span>\r\n            </div>\r\n            <p className=\"text-green-300 mt-1\">{success}</p>\r\n            <Button\r\n              onClick={() => setSuccess(null)}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n              className=\"mt-2\"\r\n            >\r\n              Dismiss\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\r\n          {/* Settings Navigation */}\r\n          <div className=\"lg:col-span-1\">\r\n            <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n              <nav className=\"space-y-2\">\r\n                {settingsSections.map((section) => {\r\n                  const Icon = section.icon;\r\n                  const isActive = activeSection === section.id;\r\n\r\n                  return (\r\n                    <button\r\n                      key={section.id}\r\n                      onClick={() => setActiveSection(section.id)}\r\n                      className={`\r\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\r\n                        transition-all duration-200\r\n                        ${\r\n                          isActive\r\n                            ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\"\r\n                            : \"text-gray-300 hover:bg-background-tertiary hover:text-white\"\r\n                        }\r\n                      `}\r\n                    >\r\n                      <Icon className=\"w-5 h-5\" />\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <span className=\"font-medium block\">\r\n                          {section.label}\r\n                        </span>\r\n                        <span className=\"text-xs text-gray-500 block truncate\">\r\n                          {section.description}\r\n                        </span>\r\n                      </div>\r\n                    </button>\r\n                  );\r\n                })}\r\n              </nav>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Settings Content */}\r\n          <div className=\"lg:col-span-3\">\r\n            <motion.div\r\n              key={activeSection}\r\n              initial={{ opacity: 0, x: 20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.3 }}\r\n              className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\"\r\n            >\r\n              {renderContent()}\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Modals */}\r\n      <ChangePasswordModal\r\n        isOpen={isChangePasswordModalOpen}\r\n        onClose={() => setIsChangePasswordModalOpen(false)}\r\n        onSuccess={() => {\r\n          // Could show a success toast here\r\n          console.log(\"Password changed successfully\");\r\n        }}\r\n      />\r\n\r\n      <DangerZoneModal\r\n        isOpen={isDangerZoneModalOpen}\r\n        onClose={() => setIsDangerZoneModalOpen(false)}\r\n        action={dangerZoneAction}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n", "// ----------------------------------------\n// User & Authentication Types\n// ----------------------------------------\n\nexport interface UserProfile {\n  id: string; // Corresponds to Supabase auth.uid()\n  email: string;\n  name?: string;\n  subscription_tier: \"Free\" | \"Study Starter\" | \"Study Pro\" | \"Study Master\" | \"Study Elite\";\n  credits_remaining: number;\n  is_active: boolean;\n  last_login?: string;\n  last_credit_reset?: string; // ISO 8601 date string for freemium monthly resets\n  subscription_expires_at?: string; // ISO 8601 date string\n  stripe_customer_id?: string;\n  stripe_subscription_id?: string;\n  created_at: string; // ISO 8601 date string\n  updated_at: string; // ISO 8601 date string\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: UserProfile;\n  token?: string;\n  error?: string;\n}\n\n// ----------------------------------------\n// Subscription & Pricing Types\n// ----------------------------------------\n\nexport type SubscriptionTier = \"Free\" | \"Study Starter\" | \"Study Pro\" | \"Study Master\" | \"Study Elite\";\n\nexport interface PricingTier {\n  id: SubscriptionTier;\n  name: string;\n  price: number; // Monthly price in USD\n  credits: number; // Credits included per month\n  features: string[];\n  isPopular?: boolean;\n  stripePriceId?: string; // Stripe Price ID for subscriptions\n}\n\nexport interface SubscriptionStatus {\n  tier: SubscriptionTier;\n  status: \"active\" | \"canceled\" | \"past_due\" | \"incomplete\" | \"trialing\";\n  current_period_start: string;\n  current_period_end: string;\n  cancel_at_period_end: boolean;\n  credits_remaining: number;\n  credits_reset_date: string;\n}\n\nexport interface PaymentIntent {\n  id: string;\n  amount: number;\n  currency: string;\n  status: string;\n  client_secret: string;\n}\n\nexport interface CreditPurchase {\n  id: string;\n  user_id: string;\n  credits_purchased: number;\n  amount_paid: number;\n  stripe_payment_intent_id: string;\n  created_at: string;\n}\n\n// ----------------------------------------\n// Document Management Types\n// ----------------------------------------\n\nexport type DocumentFileType = \"pdf\" | \"docx\" | \"txt\" | \"pptx\";\n\nexport interface DocumentMetadata {\n  id: string;\n  user_id: string;\n  filename: string;\n  file_type: DocumentFileType;\n  file_size: number; // in bytes\n  supabase_storage_path: string;\n  uploaded_at: string; // ISO 8601 date string\n  is_processed: boolean;\n  processing_error?: string;\n  page_count?: number; // Total number of pages (for PDFs, PPTx, etc.)\n}\n\nexport interface DocumentWithContent extends DocumentMetadata {\n  content_text: string;\n}\n\n// ----------------------------------------\n// Study Sets & Content Types\n// ----------------------------------------\n\nexport type StudySetType = \"flashcards\" | \"quiz\";\n\n// ----------------------------------------\n// Difficulty and Content Length Enums\n// ----------------------------------------\n\nexport enum DifficultyLevel {\n  EASY = \"easy\",\n  MEDIUM = \"medium\",\n  HARD = \"hard\",\n  COLLEGE = \"college\",\n  GRADUATE = \"graduate\",\n  PHD = \"phd\",\n}\n\nexport enum ContentLength {\n  SHORT = \"short\",\n  MEDIUM = \"medium\",\n  LONG = \"long\",\n}\n\n// Type aliases for database compatibility\nexport type DifficultyLevelType = keyof typeof DifficultyLevel;\nexport type ContentLengthType = keyof typeof ContentLength;\n\nexport interface StudySet {\n  id: string;\n  user_id: string;\n  name: string;\n  type: StudySetType;\n  is_ai_generated: boolean;\n  source_documents?: { id: string; filename: string }[]; // Array of document IDs and names\n  custom_prompt?: string;\n  created_at: string; // ISO 8601 date string\n  updated_at: string; // ISO 8601 date string\n  flashcard_count?: number;\n  quiz_question_count?: number;\n  last_studied_at?: string; // ISO 8601 date string\n}\n\nexport interface Flashcard {\n  id: string;\n  study_set_id: string;\n  front: string;\n  back: string;\n  is_flagged: boolean;\n  is_ai_generated: boolean;\n  difficulty_level?: DifficultyLevel;\n  content_length?: ContentLength;\n  times_reviewed: number;\n  last_reviewed_at?: string; // ISO 8601 date string\n}\n\nexport type QuestionType =\n  | \"multiple_choice\"\n  | \"select_all\"\n  | \"true_false\"\n  | \"short_answer\";\n\nexport interface QuizQuestion {\n  id: string;\n  study_set_id: string;\n  question_text: string;\n  question_type: QuestionType;\n  options?: string[]; // For multiple_choice, select_all\n  correct_answers: string[]; // Can be ['True'] or ['False'] for true_false\n  explanation?: string;\n  is_ai_generated: boolean;\n  difficulty_level?: DifficultyLevel;\n  content_length?: ContentLength;\n  times_attempted: number;\n  times_correct: number;\n}\n\n// ----------------------------------------\n// Credit System Types\n// ----------------------------------------\n\nexport interface CreditTransaction {\n  id: string;\n  user_id: string;\n  credits_used: number; // Negative for additions\n  operation_type: string;\n  description: string;\n  metadata?: Record<string, any>;\n  study_set_id?: string;\n  created_at: string; // ISO 8601 date string\n}\n\nexport interface AIOperationCost {\n  operation_type: string;\n  credits_required: number;\n  operations_per_credit: number;\n  is_active: boolean;\n}\n\n// ----------------------------------------\n// API Response Types\n// ----------------------------------------\n\nexport interface APIResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginatedResponse<T> extends APIResponse<T[]> {\n  total: number;\n  page: number;\n  limit: number;\n  hasMore: boolean;\n}\n\n// ----------------------------------------\n// Component Props Types\n// ----------------------------------------\n\nexport interface BaseComponentProps {\n  className?: string;\n  \"data-testid\"?: string;\n}\n\n// Button component props\nexport interface ButtonProps extends BaseComponentProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  variant?: \"primary\" | \"secondary\" | \"danger\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  isLoading?: boolean;\n  disabled?: boolean;\n  type?: \"button\" | \"submit\" | \"reset\";\n}\n\n// Input component props\nexport interface InputProps extends BaseComponentProps {\n  label?: string;\n  placeholder?: string;\n  value: string;\n  onChange: (value: string) => void;\n  type?: \"text\" | \"email\" | \"password\" | \"number\";\n  error?: string;\n  required?: boolean;\n  disabled?: boolean;\n}\n\n// Modal component props\nexport interface ModalProps extends BaseComponentProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title?: string;\n  children: React.ReactNode;\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n}\n\n// ----------------------------------------\n// Study Session Types\n// ----------------------------------------\n\nexport interface StudySession {\n  id: string;\n  user_id: string;\n  study_set_id: string;\n  session_type: StudySetType;\n  started_at: string;\n  ended_at?: string;\n  total_items: number;\n  completed_items: number;\n  correct_answers?: number; // For quiz sessions\n  session_data?: Record<string, any>; // Store session-specific data\n}\n\nexport interface StudyProgress {\n  study_set_id: string;\n  total_flashcards?: number;\n  reviewed_flashcards?: number;\n  flagged_flashcards?: number;\n  total_quiz_questions?: number;\n  answered_questions?: number;\n  correct_answers?: number;\n  last_studied_at?: string;\n  study_streak?: number;\n}\n\n// ----------------------------------------\n// AI Generation Types\n// ----------------------------------------\n\nexport interface AIGenerationRequest {\n  documents: string[]; // Document IDs\n  documentPageRanges?: {\n    [documentId: string]: { startPage: number; endPage: number };\n  }; // Page ranges for documents\n  studySetType: StudySetType;\n  customPrompt?: string;\n  itemCount?: number;\n  difficultyLevel?: DifficultyLevel;\n  contentLength?: ContentLength;\n}\n\nexport interface AIGenerationResult {\n  studySetId: string;\n  itemsGenerated: number;\n  creditsUsed: number;\n  processingTime: number;\n}\n\n// Helper functions for enum conversions\nexport const difficultyLevelToString = (level: DifficultyLevel): string => {\n  const labels: Record<DifficultyLevel, string> = {\n    [DifficultyLevel.EASY]: \"Easy\",\n    [DifficultyLevel.MEDIUM]: \"Medium\",\n    [DifficultyLevel.HARD]: \"Hard\",\n    [DifficultyLevel.COLLEGE]: \"College\",\n    [DifficultyLevel.GRADUATE]: \"Graduate\",\n    [DifficultyLevel.PHD]: \"PhD\",\n  };\n  return labels[level];\n};\n\nexport const contentLengthToString = (length: ContentLength): string => {\n  const labels: Record<ContentLength, string> = {\n    [ContentLength.SHORT]: \"Short\",\n    [ContentLength.MEDIUM]: \"Medium\",\n    [ContentLength.LONG]: \"Long\",\n  };\n  return labels[length];\n};\n\nexport const stringToDifficultyLevel = (str: string): DifficultyLevel => {\n  const normalized = str.toLowerCase();\n  switch (normalized) {\n    case \"easy\":\n      return DifficultyLevel.EASY;\n    case \"medium\":\n      return DifficultyLevel.MEDIUM;\n    case \"hard\":\n      return DifficultyLevel.HARD;\n    case \"college\":\n      return DifficultyLevel.COLLEGE;\n    case \"graduate\":\n      return DifficultyLevel.GRADUATE;\n    case \"phd\":\n      return DifficultyLevel.PHD;\n    default:\n      return DifficultyLevel.MEDIUM;\n  }\n};\n\nexport const stringToContentLength = (str: string): ContentLength => {\n  const normalized = str.toLowerCase();\n  switch (normalized) {\n    case \"short\":\n      return ContentLength.SHORT;\n    case \"medium\":\n      return ContentLength.MEDIUM;\n    case \"long\":\n      return ContentLength.LONG;\n    default:\n      return ContentLength.MEDIUM;\n  }\n};\n\n// Helper functions for number conversions (for backward compatibility)\nexport const difficultyLevelToNumber = (level: DifficultyLevel): number => {\n  const mapping: Record<DifficultyLevel, number> = {\n    [DifficultyLevel.EASY]: 1,\n    [DifficultyLevel.MEDIUM]: 3,\n    [DifficultyLevel.HARD]: 4,\n    [DifficultyLevel.COLLEGE]: 5,\n    [DifficultyLevel.GRADUATE]: 6,\n    [DifficultyLevel.PHD]: 7,\n  };\n  return mapping[level];\n};\n\nexport const numberToDifficultyLevel = (num: number): DifficultyLevel => {\n  switch (num) {\n    case 1:\n      return DifficultyLevel.EASY;\n    case 2:\n      return DifficultyLevel.EASY; // Map 2 to easy for backward compatibility\n    case 3:\n      return DifficultyLevel.MEDIUM;\n    case 4:\n      return DifficultyLevel.HARD;\n    case 5:\n      return DifficultyLevel.COLLEGE;\n    case 6:\n      return DifficultyLevel.GRADUATE;\n    case 7:\n      return DifficultyLevel.PHD;\n    default:\n      return DifficultyLevel.MEDIUM;\n  }\n};\n\n// ----------------------------------------\n// Utility Types\n// ----------------------------------------\n\nexport type LoadingState = \"idle\" | \"loading\" | \"success\" | \"error\";\n\nexport interface ErrorState {\n  message: string;\n  code?: string;\n  details?: Record<string, any>;\n}\n\nexport type SortDirection = \"asc\" | \"desc\";\n\nexport interface SortConfig {\n  field: string;\n  direction: SortDirection;\n}\n\nexport interface FilterConfig {\n  field: string;\n  value: any;\n  operator?: \"eq\" | \"ne\" | \"gt\" | \"lt\" | \"gte\" | \"lte\" | \"in\" | \"like\";\n}\n"], "names": ["difficultyOptions", "value", "DifficultyLevel", "EASY", "label", "description", "MEDIUM", "HARD", "COLLEGE", "GRADUATE", "PHD", "DifficultySelector", "_ref", "onChange", "className", "disabled", "_jsxs", "concat", "children", "_jsx", "map", "option", "isSelected", "colorClasses", "difficulty", "getSelectedDifficultyColor", "getDifficultyColor", "type", "onClick", "title", "fill", "viewBox", "fillRule", "d", "clipRule", "subscriptionPlans", "id", "name", "price", "interval", "features", "popular", "SubscriptionManagement", "_subscriptionData$cur", "_subscriptionData$cur2", "subscriptionData", "setSubscriptionData", "useState", "isLoading", "setIsLoading", "isChanging", "setIsChanging", "error", "setError", "useEffect", "fetchSubscriptionData", "async", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "Error", "result", "json", "success", "data", "err", "message", "currentPlan", "status", "nextBillingDate", "Date", "now", "toISOString", "i", "HiExclamationCircle", "char<PERSON>t", "toUpperCase", "slice", "HiInformationCircle", "cancelAtPeriodEnd", "toLocaleDateString", "<PERSON><PERSON>", "method", "variant", "size", "confirm", "HiRefresh", "plan", "_subscriptionData$cur3", "isCurrent", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "HiStar", "<PERSON><PERSON><PERSON><PERSON>", "feature", "index", "body", "JSON", "stringify", "planId", "handlePlanChange", "DataManagement", "isExporting", "setIsExporting", "isClearing", "setIsClearing", "setSuccess", "dataStats", "setDataStats", "studySets", "flashcards", "quizzes", "documents", "totalSize", "exportData", "setExportData", "analytics", "preferences", "handleClearData", "dataType", "fetchDataStats", "HiCheckCircle", "HiDownload", "Object", "entries", "key", "replace", "trim", "_objectSpread", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "split", "click", "revokeObjectURL", "HiTrash", "EnhancedBilling", "billingData", "setBillingData", "isUpdating", "setIsUpdating", "fetchBillingData", "paymentMethods", "last4", "brand", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "isDefault", "invoices", "number", "amount", "currency", "date", "dueDate", "nextInvoice", "getStatusIcon", "<PERSON><PERSON><PERSON>", "HiX", "getStatusColor", "length", "HiCreditCard", "_method$brand", "toString", "padStart", "paymentMethodId", "handleSetDefaultPaymentMethod", "invoice", "invoiceId", "handleDownloadInvoice", "TwoFactorAuth", "enabled", "onToggle", "isSetupMode", "setIsSetupMode", "setupData", "setSetupData", "verificationCode", "setVerificationCode", "showSecret", "setShowSecret", "showBackupCodes", "setShowBackupCodes", "handleVerify2FA", "createClient", "supabase", "process", "REACT_APP_SUPABASE_URL", "REACT_APP_SUPABASE_ANON_KEY", "challengeData", "challengeError", "auth", "mfa", "challenge", "factorId", "verifyError", "verify", "challengeId", "code", "handleCopyToClipboard", "text", "navigator", "clipboard", "writeText", "setTimeout", "handleCancelSetup", "HiShieldCheck", "backupCodes", "join", "HiClipboard", "src", "qrCode", "alt", "secret", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Input", "placeholder", "factorsData", "factorsError", "listFactors", "factor", "totp", "unenrollError", "unenroll", "console", "enroll", "factorType", "friendlyName", "qr_code", "<PERSON><PERSON><PERSON>", "ChangePasswordModal", "isOpen", "onClose", "onSuccess", "formData", "setFormData", "currentPassword", "newPassword", "confirmPassword", "showPasswords", "setShowPasswords", "current", "new", "handleChange", "field", "handleInputChange", "prev", "togglePasswordVisibility", "resetForm", "handleClose", "onSubmit", "e", "preventDefault", "DangerZoneModal", "action", "confirmationText", "setConfirmationText", "step", "setStep", "config", "deactivate", "confirmText", "buttonText", "warningText", "endpoint", "delete", "isConfirmationValid", "handleSubmit", "confirmation", "removeItem", "location", "handleBack", "settingsSections", "icon", "HiUser", "HiCog", "<PERSON><PERSON><PERSON>", "HiDatabase", "SettingsPage", "activeSection", "setActiveSection", "user", "logout", "useAuthStore", "navigate", "useNavigate", "profileData", "setProfileData", "email", "bio", "avatar", "setPreferences", "theme", "language", "studyReminders", "autoSave", "defaultStudyMode", "sessionDuration", "difficultyLevel", "notifications", "setNotifications", "emailNotifications", "weeklyProgress", "marketingEmails", "achievementNotifications", "streak<PERSON><PERSON><PERSON><PERSON>", "securitySettings", "setSecuritySettings", "twoFactorEnabled", "loginNotifications", "sessionTimeout", "isChangePasswordModalOpen", "setIsChangePasswordModalOpen", "isDangerZoneModalOpen", "setIsDangerZoneModalOpen", "dangerZoneAction", "setDangerZoneAction", "Authorization", "backendPrefs", "study_reminders", "auto_save", "default_study_mode", "session_duration", "difficulty_level", "loadUserSettings", "handleSaveProfile", "FormData", "append", "handleSavePreferences", "backendPreferences", "handleSaveNotifications", "handleLogout", "renderProfileSection", "_user$name", "_user$name$charAt", "input", "accept", "onchange", "_files", "file", "target", "files", "HiUpload", "rows", "section", "Icon", "isActive", "x", "renderContent", "HiMoon", "<PERSON><PERSON><PERSON>", "parseInt", "level", "HiLogout", "log", "ContentLength", "difficultyLevelToString", "difficultyLevelToNumber", "numberToDifficultyLevel", "num"], "sourceRoot": ""}