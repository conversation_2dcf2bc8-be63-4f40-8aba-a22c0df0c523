import React, { useState } from "react";
import { Button } from "../common/Button";
import { CountInput } from "../common/CountInput";
import { AIGenerationProgress } from "../common/ProgressBar";
import { useDialog } from "../../contexts/DialogContext";
import { DocumentSelector } from "../ai/DocumentSelector";
import { DifficultySelector } from "../common/DifficultySelector";
import { ContentLengthSelector } from "../common/ContentLengthSelector";
import useAuthStore from "../../stores/authStore";
import { useAIStore } from "../../stores/aiStore";
import { useUserSettings } from "../../hooks/useUserSettings";
import { BulkActionsToolbar } from "../ui/BulkActionsToolbar";
import { HiEye, HiEyeOff } from "react-icons/hi";
import {
  Flashcard,
  DifficultyLevel,
  ContentLength,
  difficultyLevelToNumber,
  difficultyLevelToString,
  numberToDifficultyLevel,
} from "../../shared/types";

interface FlashcardManagementProps {
  studySetId: string;
  flashcards: Flashcard[];
  onFlashcardAdded: (flashcard: Flashcard) => void;
  onFlashcardUpdated: (flashcard: Flashcard) => void;
  onFlashcardDeleted: (flashcardId: string) => void;
  onFlashcardsGenerated: (flashcards: Flashcard[]) => void;
}

interface NewFlashcard {
  front: string;
  back: string;
  difficulty_level: number;
}

export const FlashcardManagement: React.FC<FlashcardManagementProps> = ({
  studySetId,
  flashcards,
  onFlashcardAdded,
  onFlashcardUpdated,
  onFlashcardDeleted,
  onFlashcardsGenerated,
}) => {
  const { alert, confirm } = useDialog();
  const { user } = useAuthStore();
  const { generateMoreFlashcards } = useAIStore();
  const { settings: userSettings, updateSettings } = useUserSettings();
  const [isAIMode, setIsAIMode] = useState(true);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [documentPageRanges, setDocumentPageRanges] = useState<{
    [documentId: string]: { startPage: number; endPage: number };
  }>({});
  const [flashcardCount, setFlashcardCount] = useState(25);
  const [customPrompt, setCustomPrompt] = useState("");
  const [difficultyLevel, setDifficultyLevel] = useState<DifficultyLevel>(
    DifficultyLevel.MEDIUM
  );
  const [contentLength, setContentLength] = useState<ContentLength>(
    ContentLength.MEDIUM
  );
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newFlashcard, setNewFlashcard] = useState<NewFlashcard>({
    front: "",
    back: "",
    difficulty_level: 3,
  });
  const [editingFlashcard, setEditingFlashcard] = useState<Flashcard | null>(
    null
  );
  const [editForm, setEditForm] = useState<NewFlashcard>({
    front: "",
    back: "",
    difficulty_level: 3,
  });

  // Bulk selection state
  const [selectedFlashcards, setSelectedFlashcards] = useState<string[]>([]);
  const [isSelectAllChecked, setIsSelectAllChecked] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);

  // Individual flashcard reveal state - track which flashcards show their back content
  const [revealedFlashcards, setRevealedFlashcards] = useState<Set<string>>(
    new Set()
  );

  // Toggle individual flashcard back content
  const toggleFlashcardReveal = (flashcardId: string) => {
    setRevealedFlashcards((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(flashcardId)) {
        newSet.delete(flashcardId);
      } else {
        newSet.add(flashcardId);
      }
      return newSet;
    });
  };

  // Reveal all flashcards
  const revealAllFlashcards = () => {
    const allFlashcardIds = new Set(flashcards.map((f) => f.id));
    setRevealedFlashcards(allFlashcardIds);
  };

  // Hide all flashcards
  const hideAllFlashcards = () => {
    setRevealedFlashcards(new Set());
  };

  // Handle page range changes
  const handlePageRangeChange = (
    documentId: string,
    range: { startPage: number; endPage: number }
  ) => {
    setDocumentPageRanges((prev) => ({
      ...prev,
      [documentId]: range,
    }));
  };

  // Bulk selection functions
  const handleSelectFlashcard = (flashcardId: string, checked: boolean) => {
    if (checked) {
      setSelectedFlashcards((prev) => [...prev, flashcardId]);
    } else {
      setSelectedFlashcards((prev) => prev.filter((id) => id !== flashcardId));
      setIsSelectAllChecked(false);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    setIsSelectAllChecked(checked);
    if (checked) {
      setSelectedFlashcards(flashcards.map((f) => f.id));
    } else {
      setSelectedFlashcards([]);
    }
  };

  const clearSelection = () => {
    setSelectedFlashcards([]);
    setIsSelectAllChecked(false);
  };

  const handleBulkDelete = async () => {
    if (selectedFlashcards.length === 0) return;

    // Check if user has disabled delete confirmations
    if (!userSettings?.skip_delete_confirmations) {
      let neverAskAgainChecked = false;

      const confirmed = await confirm({
        title: "Delete Flashcards",
        message: `Are you sure you want to delete ${
          selectedFlashcards.length
        } flashcard${selectedFlashcards.length !== 1 ? "s" : ""}?`,
        variant: "danger",
        confirmText: "Delete",
        cancelText: "Cancel",
        buttonLayout: "corners",
        showNeverAskAgain: true,
        onNeverAskAgainChange: (checked) => {
          neverAskAgainChecked = checked;
        },
      });

      if (!confirmed) return;

      // Update user settings if "Never ask again" was checked
      if (neverAskAgainChecked) {
        try {
          await updateSettings({ skip_delete_confirmations: true });
        } catch (error) {
          console.error("Failed to update user settings:", error);
          // Continue with deletion even if settings update fails
        }
      }
    }

    await performBulkDeletion();
  };

  const performBulkDeletion = async () => {
    setIsBulkDeleting(true);

    try {
      const response = await fetch("/api/flashcards/bulk-delete", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          flashcardIds: selectedFlashcards,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete flashcards");
      }

      const data = await response.json();
      const { deletedCount } = data.data;

      // Update UI - remove deleted flashcards
      selectedFlashcards.forEach((id) => onFlashcardDeleted(id));
      clearSelection();

      await alert({
        title: "Success",
        message: `${deletedCount} flashcard${
          deletedCount !== 1 ? "s" : ""
        } deleted successfully!`,
        variant: "success",
      });
    } catch (error: any) {
      await alert({
        title: "Error",
        message: error.message || "Failed to delete flashcards",
        variant: "error",
      });
    } finally {
      setIsBulkDeleting(false);
    }
  };

  const handleAddFlashcard = async () => {
    if (!newFlashcard.front.trim() || !newFlashcard.back.trim()) {
      await alert({
        title: "Validation Error",
        message: "Both front and back content are required.",
        variant: "error",
      });
      return;
    }

    try {
      const response = await fetch(`/api/flashcards/study-set/${studySetId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({
          front: newFlashcard.front.trim(),
          back: newFlashcard.back.trim(),
          difficulty_level: newFlashcard.difficulty_level,
          is_ai_generated: false,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create flashcard");
      }

      const result = await response.json();
      onFlashcardAdded(result.data);

      // Reset form
      setNewFlashcard({ front: "", back: "", difficulty_level: 3 });
      setShowAddForm(false);

      await alert({
        title: "Success",
        message: "Flashcard added successfully!",
        variant: "success",
      });
    } catch (error: any) {
      await alert({
        title: "Error",
        message: error.message || "Failed to add flashcard",
        variant: "error",
      });
    }
  };

  const handleEditFlashcard = (flashcard: Flashcard) => {
    setEditingFlashcard(flashcard);
    setEditForm({
      front: flashcard.front,
      back: flashcard.back,
      difficulty_level:
        typeof flashcard.difficulty_level === "string"
          ? difficultyLevelToNumber(
              flashcard.difficulty_level as DifficultyLevel
            )
          : flashcard.difficulty_level || 3,
    });
  };

  const handleSaveEdit = async () => {
    if (!editingFlashcard) return;

    if (!editForm.front.trim() || !editForm.back.trim()) {
      await alert({
        title: "Validation Error",
        message: "Both front and back content are required.",
        variant: "error",
      });
      return;
    }

    try {
      const response = await fetch(`/api/flashcards/${editingFlashcard.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({
          front: editForm.front.trim(),
          back: editForm.back.trim(),
          difficulty_level: editForm.difficulty_level,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update flashcard");
      }

      const result = await response.json();
      onFlashcardUpdated(result.data);

      setEditingFlashcard(null);
      setEditForm({ front: "", back: "", difficulty_level: 3 });

      await alert({
        title: "Success",
        message: "Flashcard updated successfully!",
        variant: "success",
      });
    } catch (error: any) {
      await alert({
        title: "Error",
        message: error.message || "Failed to update flashcard",
        variant: "error",
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingFlashcard(null);
    setEditForm({ front: "", back: "", difficulty_level: 3 });
  };

  const handleDeleteFlashcard = async (flashcard: Flashcard) => {
    // Check if user has disabled delete confirmations
    if (userSettings?.skip_delete_confirmations) {
      // Skip confirmation and delete directly
      await performFlashcardDeletion(flashcard);
      return;
    }

    let neverAskAgainChecked = false;

    const confirmed = await confirm({
      title: "Delete Flashcard",
      message: `Are you sure you want to delete this flashcard?\n\nFront: ${flashcard.front.substring(
        0,
        50
      )}${flashcard.front.length > 50 ? "..." : ""}`,
      variant: "danger",
      confirmText: "Delete",
      cancelText: "Cancel",
      buttonLayout: "corners",
      showNeverAskAgain: true,
      onNeverAskAgainChange: (checked) => {
        neverAskAgainChecked = checked;
      },
    });

    if (!confirmed) return;

    // Update user settings if "Never ask again" was checked
    if (neverAskAgainChecked) {
      try {
        await updateSettings({ skip_delete_confirmations: true });
      } catch (error) {
        console.error("Failed to update user settings:", error);
        // Continue with deletion even if settings update fails
      }
    }

    await performFlashcardDeletion(flashcard);
  };

  const performFlashcardDeletion = async (flashcard: Flashcard) => {
    try {
      const response = await fetch(`/api/flashcards/${flashcard.id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to delete flashcard");
      }

      onFlashcardDeleted(flashcard.id);

      await alert({
        title: "Success",
        message: "Flashcard deleted successfully!",
        variant: "success",
      });
    } catch (error: any) {
      await alert({
        title: "Error",
        message: error.message || "Failed to delete flashcard",
        variant: "error",
      });
    }
  };

  const calculateCreditCost = () => {
    // New pricing model: 1 credit = 5 flashcards
    // So cost = Math.ceil(flashcardCount / 5)
    return Math.ceil(flashcardCount / 5);
  };

  const handleGenerateFlashcards = async () => {
    if (selectedDocuments.length === 0) {
      await alert({
        title: "No Documents Selected",
        message:
          "Please select at least one document to generate flashcards from.",
        variant: "warning",
      });
      return;
    }

    const creditCost = calculateCreditCost();
    if (user && user.credits_remaining < creditCost) {
      await alert({
        title: "Insufficient Credits",
        message: `You need ${creditCost} credits to generate ${flashcardCount} flashcards, but you only have ${user.credits_remaining} credits remaining.`,
        variant: "error",
      });
      return;
    }

    const confirmed = await confirm({
      title: "Generate Flashcards",
      message: `Generate ${flashcardCount} flashcards from ${selectedDocuments.length} document(s)?\n\nThis will cost ${creditCost} credits.`,
      confirmText: "Generate",
      cancelText: "Cancel",
    });

    if (!confirmed) return;

    setIsGenerating(true);
    try {
      const result = await generateMoreFlashcards({
        studySetId,
        documentIds: selectedDocuments,
        documentPageRanges,
        count: flashcardCount,
        customPrompt: customPrompt.trim() || undefined,
        difficultyLevel,
        contentLength,
        existingContent: flashcards.map((f) => f.front), // Pass existing flashcard fronts to prevent duplicates
      });

      onFlashcardsGenerated(result.flashcards);

      // Update user credits
      if (user) {
        useAuthStore.getState().updateUser({
          credits_remaining: result.creditsRemaining,
        });
      }

      // Refresh study set cache to ensure new content is loaded
      try {
        const { useStudyStore } = await import("../../stores/studyStore");
        const studyStore = useStudyStore.getState();
        await studyStore.refreshStudySetContent(studySetId);
      } catch (cacheError) {
        console.warn("Failed to refresh study set cache:", cacheError);
      }

      await alert({
        title: "Success",
        message: `Generated ${result.flashcards.length} flashcards successfully!`,
        variant: "success",
      });

      // Reset AI form
      setSelectedDocuments([]);
      setCustomPrompt("");
      setIsAIMode(false);
    } catch (error: any) {
      await alert({
        title: "Generation Error",
        message: error.message || "Failed to generate flashcards",
        variant: "error",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-white">Manage Flashcards</h3>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => setShowAddForm(!showAddForm)}
            variant="secondary"
            size="sm"
          >
            ➕ Add Flashcard
          </Button>

          {/* AI Mode Toggle */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">AI Mode</span>
            <button
              onClick={() => setIsAIMode(!isAIMode)}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${isAIMode ? "bg-primary-500" : "bg-gray-600"}
              `}
            >
              <span
                className={`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${isAIMode ? "translate-x-6" : "translate-x-1"}
                `}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Manual Add Form */}
      {showAddForm && (
        <div className="bg-background-secondary rounded-lg p-4 border border-gray-600">
          <h4 className="text-md font-medium text-white mb-4">
            Add New Flashcard
          </h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Front (Question/Term)
              </label>
              <textarea
                value={newFlashcard.front}
                onChange={(e) =>
                  setNewFlashcard((prev) => ({
                    ...prev,
                    front: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={3}
                placeholder="Enter the front content..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Back (Answer/Definition)
              </label>
              <textarea
                value={newFlashcard.back}
                onChange={(e) =>
                  setNewFlashcard((prev) => ({ ...prev, back: e.target.value }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={3}
                placeholder="Enter the back content..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Difficulty Level
              </label>
              <select
                value={newFlashcard.difficulty_level}
                onChange={(e) =>
                  setNewFlashcard((prev) => ({
                    ...prev,
                    difficulty_level: parseInt(e.target.value),
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              >
                <option value={1}>1 - Very Easy</option>
                <option value={2}>2 - Easy</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Hard</option>
                <option value={5}>5 - Very Hard</option>
              </select>
            </div>

            <div className="flex space-x-3">
              <Button onClick={handleAddFlashcard} variant="primary">
                Add Flashcard
              </Button>
              <Button onClick={() => setShowAddForm(false)} variant="secondary">
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Form */}
      {editingFlashcard && (
        <div className="bg-background-secondary rounded-lg p-4 border border-gray-600">
          <h4 className="text-md font-medium text-white mb-4">
            Edit Flashcard
          </h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Front (Question/Term)
              </label>
              <textarea
                value={editForm.front}
                onChange={(e) =>
                  setEditForm((prev) => ({ ...prev, front: e.target.value }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={3}
                placeholder="Enter the front content..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Back (Answer/Definition)
              </label>
              <textarea
                value={editForm.back}
                onChange={(e) =>
                  setEditForm((prev) => ({ ...prev, back: e.target.value }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={3}
                placeholder="Enter the back content..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Difficulty Level
              </label>
              <select
                value={editForm.difficulty_level}
                onChange={(e) =>
                  setEditForm((prev) => ({
                    ...prev,
                    difficulty_level: parseInt(e.target.value),
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              >
                <option value={1}>1 - Very Easy</option>
                <option value={2}>2 - Easy</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Hard</option>
                <option value={5}>5 - Very Hard</option>
              </select>
            </div>

            <div className="flex space-x-3">
              <Button onClick={handleSaveEdit} variant="primary">
                Save Changes
              </Button>
              <Button onClick={handleCancelEdit} variant="secondary">
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* AI Generation Panel */}
      {isAIMode && (
        <div className="bg-background-secondary rounded-lg p-4 border border-gray-600">
          <h4 className="text-md font-medium text-white mb-4">
            AI Flashcard Generation
          </h4>

          <div className="space-y-4">
            {/* Document Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Select Documents
              </label>
              <DocumentSelector
                selectedDocuments={selectedDocuments}
                onSelectionChange={setSelectedDocuments}
                documentPageRanges={documentPageRanges}
                onPageRangeChange={handlePageRangeChange}
                maxSelection={5}
              />
            </div>

            {/* Generation Controls */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <CountInput
                label="Number of Flashcards"
                value={flashcardCount}
                onChange={setFlashcardCount}
                min={1}
                max={100}
                placeholder="Enter number (1-100)"
                disabled={isGenerating}
              />

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Credit Cost
                </label>
                <div className="px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium">
                  {calculateCreditCost()} credits
                </div>
              </div>
            </div>

            {/* Difficulty Level */}
            <DifficultySelector
              value={difficultyLevel}
              onChange={setDifficultyLevel}
            />

            {/* Content Length */}
            <ContentLengthSelector
              value={contentLength}
              onChange={setContentLength}
            />

            {/* Custom Prompt */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Custom Instructions (Optional)
              </label>
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={3}
                placeholder="Add specific instructions for flashcard generation..."
              />
            </div>

            {/* Generate Button */}
            <Button
              onClick={handleGenerateFlashcards}
              disabled={selectedDocuments.length === 0 || isGenerating}
              className="w-full"
              variant="primary"
            >
              {isGenerating
                ? "Generating..."
                : `Generate ${flashcardCount} Flashcards`}
            </Button>
          </div>
        </div>
      )}

      {/* AI Generation Progress */}
      <AIGenerationProgress
        isGenerating={isGenerating}
        stage={isGenerating ? "Generating flashcards with AI..." : undefined}
        estimatedTime={Math.ceil(flashcardCount / 10)} // Rough estimate: 1 second per 10 cards
      />

      {/* Flashcard List */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="text-md font-medium text-white">
            Current Flashcards ({flashcards.length})
          </h4>

          {flashcards.length > 0 && (
            <div className="flex items-center space-x-3">
              <label className="flex items-center space-x-2 text-sm text-gray-300">
                <input
                  type="checkbox"
                  checked={isSelectAllChecked}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"
                />
                <span>Select All</span>
              </label>
            </div>
          )}
        </div>

        {/* Bulk Actions Toolbar */}
        <BulkActionsToolbar
          selectedCount={selectedFlashcards.length}
          totalCount={flashcards.length}
          onDeleteSelected={handleBulkDelete}
          onClearSelection={clearSelection}
          isLoading={isBulkDeleting}
        />

        {/* Toggle Controls */}
        {flashcards.length > 0 && (
          <div className="flex items-center justify-between mb-4">
            <p className="text-sm text-gray-400">
              💡 Click on any flashcard to reveal/hide its answer
            </p>
            <div className="flex items-center space-x-2">
              <button
                onClick={revealAllFlashcards}
                className="flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm"
              >
                <HiEye className="w-4 h-4" />
                <span>Show All</span>
              </button>
              <button
                onClick={hideAllFlashcards}
                className="flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm"
              >
                <HiEyeOff className="w-4 h-4" />
                <span>Hide All</span>
              </button>
            </div>
          </div>
        )}

        {flashcards.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            No flashcards yet. Add some manually or generate them with AI.
          </div>
        ) : (
          <div className="space-y-2">
            {flashcards.map((flashcard) => (
              <div
                key={flashcard.id}
                className="bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors"
              >
                <div className="flex items-start space-x-3">
                  {/* Selection Checkbox */}
                  <div className="flex-shrink-0 pt-1">
                    <input
                      type="checkbox"
                      checked={selectedFlashcards.includes(flashcard.id)}
                      onChange={(e) =>
                        handleSelectFlashcard(flashcard.id, e.target.checked)
                      }
                      className="rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div
                        className="flex-1 min-w-0 cursor-pointer group"
                        onClick={() => toggleFlashcardReveal(flashcard.id)}
                        title={
                          revealedFlashcards.has(flashcard.id)
                            ? "Click to hide answer"
                            : "Click to reveal answer"
                        }
                      >
                        <div className="mb-2">
                          <span className="text-xs text-gray-400 uppercase tracking-wide">
                            Front
                          </span>
                          <p className="text-white font-medium group-hover:text-primary-300 transition-colors">
                            {flashcard.front}
                          </p>
                        </div>
                        {revealedFlashcards.has(flashcard.id) ? (
                          <div className="mb-2">
                            <span className="text-xs text-gray-400 uppercase tracking-wide">
                              Back
                            </span>
                            <p className="text-gray-300 group-hover:text-gray-200 transition-colors">
                              {flashcard.back}
                            </p>
                          </div>
                        ) : (
                          <div className="mb-2">
                            <p className="text-gray-500 italic text-sm group-hover:text-primary-400 transition-colors">
                              Click to reveal answer...
                            </p>
                          </div>
                        )}
                        <div className="flex items-center space-x-4 text-xs text-gray-400">
                          {flashcard.is_ai_generated && (
                            <span className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
                              AI Generated
                            </span>
                          )}
                          {flashcard.difficulty_level && (
                            <span>
                              Difficulty:{" "}
                              {typeof flashcard.difficulty_level === "string"
                                ? difficultyLevelToString(
                                    flashcard.difficulty_level as DifficultyLevel
                                  )
                                : difficultyLevelToString(
                                    numberToDifficultyLevel(
                                      flashcard.difficulty_level
                                    )
                                  )}
                            </span>
                          )}
                          <span>
                            Reviewed: {flashcard.times_reviewed || 0} times
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleEditFlashcard(flashcard)}
                          className="text-gray-400 hover:text-white p-1"
                          title="Edit flashcard"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => handleDeleteFlashcard(flashcard)}
                          className="text-gray-400 hover:text-red-400 p-1"
                          title="Delete flashcard"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
