import { Request, Response } from 'express';
import { userSettingsService, UserSettingsUpdate, userPreferencesService, UserPreferencesUpdate } from '../services/userSettingsService';

export const getUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const settings = await userSettingsService.getUserSettings(userId);

    res.json({
      success: true,
      data: settings
    });
  } catch (error: any) {
    console.error('Get user settings error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch user settings'
    });
  }
};

export const updateUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const updates: UserSettingsUpdate = req.body;

    // Validate request body
    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'Invalid request body'
      });
    }

    // Validate allowed fields
    const allowedFields = ['skip_delete_confirmations', 'shuffle_flashcards'];
    const providedFields = Object.keys(updates);
    const invalidFields = providedFields.filter(field => !allowedFields.includes(field));

    if (invalidFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Invalid fields: ${invalidFields.join(', ')}`
      });
    }

    if (providedFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid fields provided for update'
      });
    }

    const updatedSettings = await userSettingsService.updateUserSettings(userId, updates);

    res.json({
      success: true,
      data: updatedSettings
    });
  } catch (error: any) {
    console.error('Update user settings error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update user settings'
    });
  }
};

export const getUserPreferences = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const preferences = await userPreferencesService.getUserPreferences(userId);

    res.json({
      success: true,
      data: preferences
    });
  } catch (error: any) {
    console.error('Get user preferences error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch user preferences'
    });
  }
};

export const updateUserPreferences = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const updates: UserPreferencesUpdate = req.body;

    // Validate request body
    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'Invalid request body'
      });
    }

    // Validate allowed fields
    const allowedFields = ['theme', 'language', 'study_reminders', 'auto_save', 'default_study_mode', 'session_duration', 'difficulty_level'];
    const providedFields = Object.keys(updates);
    const invalidFields = providedFields.filter(field => !allowedFields.includes(field));

    if (invalidFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Invalid fields: ${invalidFields.join(', ')}`
      });
    }

    if (providedFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid fields provided for update'
      });
    }

    const updatedPreferences = await userPreferencesService.updateUserPreferences(userId, updates);

    res.json({
      success: true,
      data: updatedPreferences
    });
  } catch (error: any) {
    console.error('Update user preferences error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update user preferences'
    });
  }
};
