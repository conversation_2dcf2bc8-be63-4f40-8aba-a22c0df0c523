import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import useAuthStore from "../stores/authStore";

export const AuthCallback: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const { handleOAuthCallback } = useAuthStore();
  const navigate = useNavigate();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const result = await handleOAuthCallback();

        if (result.success) {
          // Redirect to dashboard on successful authentication
          navigate("/dashboard", { replace: true });
        } else {
          setError(result.error || "Authentication failed");
          // Redirect to login after a delay
          setTimeout(() => {
            navigate("/login", { replace: true });
          }, 3000);
        }
      } catch (error) {
        setError("Failed to process authentication");
        setTimeout(() => {
          navigate("/login", { replace: true });
        }, 3000);
      }
    };

    handleCallback();
  }, [handleOAuthCallback, navigate]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background-primary">
        <div className="max-w-md w-full text-center space-y-4 p-8">
          <div className="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded">
            <h3 className="text-lg font-semibold">Authentication Error</h3>
            <p className="mt-2">{error}</p>
          </div>
          <p className="text-gray-400">Redirecting to login page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background-primary">
      <div className="max-w-md w-full text-center space-y-4 p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
        <h3 className="text-lg font-semibold text-white">
          Completing Authentication...
        </h3>
        <p className="text-gray-400">Please wait while we sign you in.</p>
      </div>
    </div>
  );
};
