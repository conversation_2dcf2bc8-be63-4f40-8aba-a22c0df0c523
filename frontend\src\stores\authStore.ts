import { create } from "zustand";
import { UserProfile } from "../shared/types";
import { authService } from "../services/auth";

interface AuthState {
  user: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (
    email: string,
    password: string,
    rememberMe?: boolean
  ) => Promise<{ success: boolean; error?: string }>;
  signup: (
    email: string,
    password: string,
    name?: string
  ) => Promise<{ success: boolean; error?: string }>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  handleOAuthCallback: () => Promise<{
    success: boolean;
    error?: string;
    user?: UserProfile;
  }>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  updateUser: (updates: Partial<UserProfile>) => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isLoading: true, // Start with loading true to check auth on app startup
  isAuthenticated: false,

  login: async (email, password, rememberMe = true) => {
    set({ isLoading: true });
    try {
      const result = await authService.signIn(email, password, rememberMe);
      if (result.success && result.user) {
        set({ user: result.user, isAuthenticated: true, isLoading: false });
        return { success: true };
      }
      set({ isLoading: false });
      return { success: false, error: result.error || "Login failed" };
    } catch {
      set({ isLoading: false });
      return { success: false, error: "Network error" };
    }
  },

  signup: async (email, password, name) => {
    set({ isLoading: true });
    try {
      const result = await authService.signUp(email, password, name);
      if (result.success && result.user) {
        set({ user: result.user, isAuthenticated: true, isLoading: false });
        return { success: true };
      }
      set({ isLoading: false });
      return { success: false, error: result.error || "Signup failed" };
    } catch {
      set({ isLoading: false });
      return { success: false, error: "Network error" };
    }
  },

  signInWithGoogle: async () => {
    set({ isLoading: true });
    try {
      const result = await authService.signInWithGoogle();
      if (result.error) {
        set({ isLoading: false });
        return { success: false, error: result.error };
      }
      // The actual authentication will be handled by the OAuth callback
      // Don't set loading to false here as the redirect is happening
      return { success: true };
    } catch (error) {
      set({ isLoading: false });
      return { success: false, error: "Failed to initiate Google sign in" };
    }
  },

  handleOAuthCallback: async () => {
    set({ isLoading: true });
    try {
      const result = await authService.handleOAuthCallback();
      if (result.success && result.user) {
        set({ user: result.user, isAuthenticated: true, isLoading: false });
        return { success: true, user: result.user };
      }
      set({ isLoading: false });
      return { success: false, error: result.error || "OAuth callback failed" };
    } catch (error) {
      set({ isLoading: false });
      return { success: false, error: "Network error during OAuth callback" };
    }
  },

  logout: async () => {
    set({ isLoading: true });
    try {
      await authService.signOut();
    } finally {
      set({ user: null, isAuthenticated: false, isLoading: false });
    }
  },

  checkAuth: async () => {
    set({ isLoading: true });
    try {
      // First check if we have a token
      const hasToken = authService.isAuthenticated();
      if (!hasToken) {
        set({ user: null, isAuthenticated: false, isLoading: false });
        return;
      }

      const user = await authService.getCurrentUser();
      if (user) {
        set({ user, isAuthenticated: true, isLoading: false });
      } else {
        // Token exists but is invalid, clear it
        await authService.signOut();
        set({ user: null, isAuthenticated: false, isLoading: false });
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      // Clear potentially invalid token
      await authService.signOut();
      set({ user: null, isAuthenticated: false, isLoading: false });
    }
  },

  updateUser: (updates) => {
    const { user } = get();
    if (user) set({ user: { ...user, ...updates } });
  },
}));

// Provide a default export for compatibility with build tooling
export default useAuthStore;
