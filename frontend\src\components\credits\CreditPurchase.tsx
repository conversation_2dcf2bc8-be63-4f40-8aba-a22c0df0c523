import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  HiCreditCard,
  HiSparkles,
  HiCheck,
  HiStar,
  HiLightningBolt,
  HiAcademicCap,
} from "react-icons/hi";
import { Button } from "../common/Button";
import { useCreditStore } from "../../stores/creditStore";
import useAuthStore from "../../stores/authStore";
import { PRICING_TIERS } from "../../shared/constants";
import type { PricingTier } from "../../shared/types";

interface CreditPurchaseProps {
  currentBalance: number;
  userTier: string;
  onPurchaseComplete: () => void;
}

export const CreditPurchase: React.FC<CreditPurchaseProps> = ({
  currentBalance,
  userTier,
  onPurchaseComplete,
}) => {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [discountCode, setDiscountCode] = useState<string>("");
  const [showDiscountInput, setShowDiscountInput] = useState(false);
  const [activeTab, setActiveTab] = useState<"subscriptions" | "credits">(
    "subscriptions"
  );
  const { refreshAfterPurchase } = useCreditStore();
  const { user } = useAuthStore();

  const handleSubscriptionPurchase = async (tierId: string) => {
    const selectedTier = PRICING_TIERS.find((tier) => tier.id === tierId);
    if (!selectedTier) {
      setError("Selected subscription tier not found. Please try again.");
      return;
    }

    if (!user?.email) {
      setError("User email not available. Please log out and log back in.");
      return;
    }

    setIsProcessing(true);
    setSelectedPackage(tierId);
    setError(null);
    setSuccess(null);

    try {
      // TODO: Implement Stripe subscription creation
      console.log("Creating subscription for tier:", selectedTier);
      setSuccess(
        `Subscription to ${selectedTier.name} will be implemented in the next phase. Price: $${selectedTier.price}/month`
      );

      // Refresh credit data
      await refreshAfterPurchase();
      onPurchaseComplete();
    } catch (error) {
      console.error("Subscription error:", error);
      setError("Failed to create subscription. Please try again.");
    } finally {
      setIsProcessing(false);
      setSelectedPackage(null);
    }
  };

  const getTierIcon = (tierId: string) => {
    switch (tierId) {
      case "Study Starter":
        return <HiAcademicCap className="w-6 h-6" />;
      case "Study Pro":
        return <HiStar className="w-6 h-6" />;
      case "Study Master":
        return <HiLightningBolt className="w-6 h-6" />;
      case "Study Elite":
        return <HiSparkles className="w-6 h-6" />;
      default:
        return <HiCreditCard className="w-6 h-6" />;
    }
  };

  const isCurrentTier = (tierId: string) => {
    return userTier === tierId;
  };

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-400">Error</h3>
              <div className="mt-1 text-sm text-red-300">{error}</div>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={() => setError(null)}
                className="inline-flex rounded-md bg-red-900/20 p-1.5 text-red-400 hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-900"
              >
                <span className="sr-only">Dismiss</span>
                <svg
                  className="h-4 w-4"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Success Display */}
      {success && (
        <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-green-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-400">Success</h3>
              <div className="mt-1 text-sm text-green-300">{success}</div>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={() => setSuccess(null)}
                className="inline-flex rounded-md bg-green-900/20 p-1.5 text-green-400 hover:bg-green-900/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-green-900"
              >
                <span className="sr-only">Dismiss</span>
                <svg
                  className="h-4 w-4"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Current Balance Display */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white">
              Current Plan: {userTier}
            </h3>
            <p className="text-gray-400">Your available credits</p>
          </div>
          <div className="text-right">
            <span className="text-2xl font-bold text-primary-400">
              {currentBalance}
            </span>
            <span className="text-gray-400 ml-2">credits</span>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-background-tertiary rounded-lg p-1">
        <button
          onClick={() => setActiveTab("subscriptions")}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === "subscriptions"
              ? "bg-primary-500 text-white"
              : "text-gray-400 hover:text-white"
          }`}
        >
          Monthly Plans
        </button>
        <button
          onClick={() => setActiveTab("credits")}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === "credits"
              ? "bg-primary-500 text-white"
              : "text-gray-400 hover:text-white"
          }`}
        >
          One-Time Credits
        </button>
      </div>

      {/* Discount Code Section */}
      {activeTab === "credits" && (
        <div className="bg-background-secondary rounded-lg p-4 border border-border-primary mb-6">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-md font-medium text-white">
              Have a discount code?
            </h4>
            <button
              onClick={() => setShowDiscountInput(!showDiscountInput)}
              className="text-sm text-primary-400 hover:text-primary-300 transition-colors"
            >
              {showDiscountInput ? "Hide" : "Enter Code"}
            </button>
          </div>

          {showDiscountInput && (
            <div className="flex space-x-3">
              <input
                type="text"
                value={discountCode}
                onChange={(e) => setDiscountCode(e.target.value.toUpperCase())}
                placeholder="Enter discount code"
                className="flex-1 px-3 py-2 bg-background-tertiary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                maxLength={20}
              />
              <button
                onClick={() => {
                  setDiscountCode("");
                  setShowDiscountInput(false);
                }}
                className="px-3 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Clear
              </button>
            </div>
          )}
        </div>
      )}

      {/* Subscription Plans */}
      {activeTab === "subscriptions" && (
        <div>
          <h3 className="text-lg font-semibold text-white mb-2">
            Choose Your Study Plan
          </h3>
          <p className="text-gray-400 mb-6">
            Monthly subscriptions with automatic credit refills. Cancel anytime.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {PRICING_TIERS.filter((tier) => tier.id !== "Free").map(
              (tier: PricingTier, index: number) => {
                const isSelected = selectedPackage === tier.id;
                const isProcessingThis = isProcessing && isSelected;
                const isCurrent = isCurrentTier(tier.id);

                return (
                  <motion.div
                    key={tier.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className={`
                    relative bg-background-secondary rounded-lg p-6 border transition-all duration-200
                    ${
                      tier.isPopular
                        ? "border-primary-500 ring-2 ring-primary-500/20"
                        : "border-border-primary hover:border-border-secondary"
                    }
                    ${isSelected ? "ring-2 ring-primary-500/50" : ""}
                    ${isCurrent ? "ring-2 ring-green-500/50" : ""}
                  `}
                  >
                    {/* Popular Badge */}
                    {tier.isPopular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <div className="bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          Most Popular
                        </div>
                      </div>
                    )}

                    {/* Current Plan Badge */}
                    {isCurrent && (
                      <div className="absolute -top-3 right-4">
                        <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          Current Plan
                        </div>
                      </div>
                    )}

                    <div className="text-center">
                      {/* Icon */}
                      <div
                        className={`inline-flex p-3 rounded-lg mb-4 ${
                          tier.isPopular
                            ? "bg-primary-500/20 text-primary-400"
                            : "bg-background-tertiary text-gray-400"
                        }`}
                      >
                        {getTierIcon(tier.id)}
                      </div>

                      {/* Tier Name */}
                      <h4 className="text-lg font-semibold text-white mb-2">
                        {tier.name}
                      </h4>

                      {/* Credits */}
                      <div className="mb-4">
                        <span className="text-3xl font-bold text-white">
                          {tier.credits}
                        </span>
                        <div className="text-gray-400 text-sm">
                          credits/month
                        </div>
                        <div className="text-green-400 text-xs">
                          ~{tier.credits * 5} flashcards/quizzes
                        </div>
                      </div>

                      {/* Price */}
                      <div className="mb-4">
                        <span className="text-2xl font-bold text-white">
                          ${tier.price}
                        </span>
                        <div className="text-gray-400 text-sm">/month</div>
                        <div className="text-gray-400 text-xs">
                          ${(tier.price / tier.credits).toFixed(3)} per credit
                        </div>
                      </div>

                      {/* Features */}
                      <div className="space-y-2 mb-6">
                        {tier.features.map(
                          (feature: string, featureIndex: number) => (
                            <div
                              key={featureIndex}
                              className="flex items-center text-sm text-gray-300"
                            >
                              <HiCheck className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                              <span>{feature}</span>
                            </div>
                          )
                        )}
                      </div>

                      {/* Subscribe Button */}
                      <Button
                        onClick={() => handleSubscriptionPurchase(tier.id)}
                        variant={tier.isPopular ? "primary" : "secondary"}
                        className="w-full"
                        isLoading={isProcessingThis}
                        disabled={isProcessing || !user?.email || isCurrent}
                      >
                        {isProcessingThis
                          ? "Processing..."
                          : isCurrent
                          ? "Current Plan"
                          : !user?.email
                          ? "Login Required"
                          : `Subscribe to ${tier.name}`}
                      </Button>
                    </div>
                  </motion.div>
                );
              }
            )}
          </div>
        </div>
      )}

      {/* One-Time Credit Packages */}
      {activeTab === "credits" && (
        <div>
          <h3 className="text-lg font-semibold text-white mb-2">
            One-Time Credit Packages
          </h3>
          <p className="text-gray-400 mb-6">
            Purchase credits that never expire. Perfect for occasional use.
          </p>

          <div className="text-center py-12">
            <HiSparkles className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              One-Time Packages Coming Soon
            </h3>
            <p className="text-gray-600">
              We're working on one-time credit packages. For now, please choose
              a subscription plan above.
            </p>
          </div>
        </div>
      )}

      {/* Additional Information */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <h4 className="text-lg font-semibold text-white mb-4">
          {activeTab === "subscriptions"
            ? "Subscription Benefits"
            : "One-Time Purchase Benefits"}
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="font-medium text-white mb-2">Safe & Secure</h5>
            <p className="text-gray-400 text-sm">
              Student-safe payments through Stripe. Your payment info is never
              stored. Perfect for using your student card or parent's card with
              permission.
            </p>
          </div>
          <div>
            <h5 className="font-medium text-white mb-2">
              {activeTab === "subscriptions"
                ? "Cancel Anytime"
                : "Never Expire"}
            </h5>
            <p className="text-gray-400 text-sm">
              {activeTab === "subscriptions"
                ? "No long-term commitments. Cancel your subscription anytime and keep using credits until they run out."
                : "One-time credit purchases never expire - perfect for semester planning. Use them at your own pace!"}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
