import { supabase } from './supabaseService';
import { Flashcard } from '../../../shared/types';

type FlashcardData = Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged' | 'times_reviewed' | 'last_reviewed_at'>;

export class FlashcardService {
  async getFlashcardsByStudySet(studySetId: string, userId: string): Promise<Flashcard[]> {
    // Verify user owns the study set
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .select('*')
      .eq('study_set_id', studySetId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to get flashcards: ${error.message}`);
    }

    return data || [];
  }

  async createFlashcard(studySetId: string, userId: string, flashcardData: FlashcardData): Promise<Flashcard> {
    // Verify user owns the study set
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', studySetId)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Study set not found or access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .insert({
        study_set_id: studySetId,
        front: flashcardData.front,
        back: flashcardData.back,
        difficulty_level: flashcardData.difficulty_level || 3,
        is_ai_generated: flashcardData.is_ai_generated || false
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create flashcard: ${error.message}`);
    }

    return data;
  }

  async updateFlashcard(flashcardId: string, userId: string, updates: Partial<FlashcardData>): Promise<Flashcard> {
    // Verify user owns the flashcard through study set
    const { data: flashcard, error: flashcardError } = await supabase
      .from('flashcards')
      .select('study_set_id')
      .eq('id', flashcardId)
      .single();

    if (flashcardError || !flashcard) {
      throw new Error('Flashcard not found');
    }

    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', flashcard.study_set_id)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .update(updates)
      .eq('id', flashcardId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update flashcard: ${error.message}`);
    }

    return data;
  }

  async deleteFlashcard(flashcardId: string, userId: string): Promise<void> {
    // Verify user owns the flashcard through study set
    const { data: flashcard, error: flashcardError } = await supabase
      .from('flashcards')
      .select('study_set_id')
      .eq('id', flashcardId)
      .single();

    if (flashcardError || !flashcard) {
      throw new Error('Flashcard not found');
    }

    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', flashcard.study_set_id)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Access denied');
    }

    const { error } = await supabase
      .from('flashcards')
      .delete()
      .eq('id', flashcardId);

    if (error) {
      throw new Error(`Failed to delete flashcard: ${error.message}`);
    }
  }

  async updateFlashcardProgress(flashcardId: string, _userId: string): Promise<Flashcard> {
    // First get current review count
    const { data: current, error: getCurrentError } = await supabase
      .from('flashcards')
      .select('times_reviewed')
      .eq('id', flashcardId)
      .single();

    if (getCurrentError || !current) {
      throw new Error('Flashcard not found');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .update({
        times_reviewed: current.times_reviewed + 1,
        last_reviewed_at: new Date().toISOString()
      })
      .eq('id', flashcardId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update flashcard progress: ${error.message}`);
    }

    return data;
  }

  async toggleFlashcardFlag(flashcardId: string, userId: string): Promise<Flashcard> {
    // Get current flag status
    const { data: current, error: getCurrentError } = await supabase
      .from('flashcards')
      .select('is_flagged, study_set_id')
      .eq('id', flashcardId)
      .single();

    if (getCurrentError || !current) {
      throw new Error('Flashcard not found');
    }

    // Verify ownership
    const { data: studySet, error: studySetError } = await supabase
      .from('study_sets')
      .select('id')
      .eq('id', current.study_set_id)
      .eq('user_id', userId)
      .single();

    if (studySetError || !studySet) {
      throw new Error('Access denied');
    }

    const { data, error } = await supabase
      .from('flashcards')
      .update({ is_flagged: !current.is_flagged })
      .eq('id', flashcardId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to toggle flashcard flag: ${error.message}`);
    }

    return data;
  }

  async bulkDeleteFlashcards(flashcardIds: string[], userId: string): Promise<number> {
    // First, verify all flashcards belong to study sets owned by the user
    const { data: flashcards, error: fetchError } = await supabase
      .from('flashcards')
      .select(`
        id,
        study_set_id,
        study_sets!inner(user_id)
      `)
      .in('id', flashcardIds);

    if (fetchError) {
      throw new Error(`Failed to verify flashcard ownership: ${fetchError.message}`);
    }

    if (!flashcards || flashcards.length === 0) {
      throw new Error('No flashcards found');
    }

    // Verify all flashcards belong to the user
    const unauthorizedFlashcards = flashcards.filter(
      (flashcard: any) => flashcard.study_sets.user_id !== userId
    );

    if (unauthorizedFlashcards.length > 0) {
      throw new Error('Access denied: Some flashcards do not belong to you');
    }

    // Get the IDs of flashcards that actually exist and belong to the user
    const validFlashcardIds = flashcards.map(f => f.id);

    if (validFlashcardIds.length === 0) {
      return 0;
    }

    // Perform bulk delete
    const { error: deleteError } = await supabase
      .from('flashcards')
      .delete()
      .in('id', validFlashcardIds);

    if (deleteError) {
      throw new Error(`Failed to delete flashcards: ${deleteError.message}`);
    }

    return validFlashcardIds.length;
  }
}

export const flashcardService = new FlashcardService();
