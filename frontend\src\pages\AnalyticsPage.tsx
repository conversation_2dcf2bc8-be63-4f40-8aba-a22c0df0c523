import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  Hi<PERSON><PERSON><PERSON><PERSON>,
  HiTrendingUp,
  <PERSON><PERSON><PERSON>,
  HiRefresh,
  HiFilter,
  HiDownload,
  HiExclamationCircle
} from 'react-icons/hi';
import { StudyAnalytics } from '../components/analytics/StudyAnalytics';
import { AnalyticsDashboard } from '../components/analytics/AnalyticsDashboard';
import { PerformanceMetrics } from '../components/analytics/PerformanceMetrics';
import { StudyTrends } from '../components/analytics/StudyTrends';
import { Button } from '../components/common/Button';
import { useStudyStore } from '../stores/studyStore';
import { useDocumentStore } from '../stores/documentStore';

interface TabSection {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const analyticsTabs: TabSection[] = [
  {
    id: 'overview',
    label: 'Overview',
    icon: HiC<PERSON><PERSON><PERSON>,
    description: 'General study analytics and progress'
  },
  {
    id: 'performance',
    label: 'Performance',
    icon: HiTrendingUp,
    description: 'Detailed performance metrics and accuracy'
  },
  {
    id: 'trends',
    label: 'Trends',
    icon: HiClock,
    description: 'Study patterns and time analysis'
  }
];

export const AnalyticsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { studySets, sessions, fetchStudySets, fetchStudySessions } = useStudyStore();
  const { documents } = useDocumentStore();

  useEffect(() => {
    const loadAnalyticsData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        await Promise.all([
          fetchStudySets(),
          fetchStudySessions(timeRange)
        ]);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load analytics data');
      } finally {
        setIsLoading(false);
      }
    };

    loadAnalyticsData();
  }, [timeRange, fetchStudySets, fetchStudySessions]);

  const handleRefresh = async () => {
    setError(null);
    setIsLoading(true);

    try {
      await Promise.all([
        fetchStudySets(),
        fetchStudySessions(timeRange)
      ]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh data');
    } finally {
      setIsLoading(false);
    }
  };

  const exportAnalytics = () => {
    // Create CSV export of analytics data
    const csvData = [
      ['Date', 'Study Set', 'Type', 'Items Reviewed', 'Time Spent (minutes)', 'Accuracy'].join(','),
      ...sessions.map(session => {
        const studySet = studySets.find(set => set.id === session.studySetId);
        return [
          new Date(session.startTime).toLocaleDateString(),
          studySet?.name || 'Unknown',
          session.type,
          session.reviewedItems,
          Math.round(session.timeSpent / 60),
          session.correctAnswers ? `${Math.round((session.correctAnswers / session.totalItems) * 100)}%` : 'N/A'
        ].join(',');
      })
    ].join('\n');

    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `study-analytics-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      <AnalyticsDashboard
        studySets={studySets}
        sessions={sessions}
        documents={documents}
        timeRange={timeRange}
        isLoading={isLoading}
      />
      <StudyAnalytics
        studySets={studySets}
        sessions={sessions}
      />
    </div>
  );

  const renderPerformanceTab = () => (
    <PerformanceMetrics
      studySets={studySets}
      sessions={sessions}
      timeRange={timeRange}
      isLoading={isLoading}
    />
  );

  const renderTrendsTab = () => (
    <StudyTrends
      sessions={sessions}
      timeRange={timeRange}
      isLoading={isLoading}
    />
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewTab();
      case 'performance':
        return renderPerformanceTab();
      case 'trends':
        return renderTrendsTab();
      default:
        return renderOverviewTab();
    }
  };

  return (
    <div className="min-h-screen bg-background-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Analytics</h1>
            <p className="text-gray-400">Track your learning progress and performance insights</p>
          </div>

          <div className="flex items-center space-x-4">
            {/* Time Range Filter */}
            <div className="relative">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d' | 'all')}
                className="appearance-none bg-background-secondary border border-border-primary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="all">All time</option>
              </select>
              <HiFilter className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>

            {/* Export Button */}
            <Button
              onClick={exportAnalytics}
              variant="secondary"
              size="sm"
              disabled={sessions.length === 0}
            >
              <HiDownload className="w-4 h-4 mr-2" />
              Export
            </Button>

            {/* Refresh Button */}
            <Button
              onClick={handleRefresh}
              variant="secondary"
              size="sm"
              disabled={isLoading}
            >
              <HiRefresh className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiExclamationCircle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">Error</span>
            </div>
            <p className="text-red-300 mt-1">{error}</p>
            <Button
              onClick={() => setError(null)}
              variant="secondary"
              size="sm"
              className="mt-2"
            >
              Dismiss
            </Button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Tab Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
              <nav className="space-y-2">
                {analyticsTabs.map((tab) => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;

                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${isActive
                          ? 'bg-primary-500/20 text-primary-400 border border-primary-500/30'
                          : 'text-gray-300 hover:bg-background-tertiary hover:text-white'
                        }
                      `}
                    >
                      <Icon className="w-5 h-5" />
                      <div className="flex-1 min-w-0">
                        <span className="font-medium block">{tab.label}</span>
                        <span className="text-xs text-gray-500 block truncate">
                          {tab.description}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="lg:col-span-3">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {renderContent()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
