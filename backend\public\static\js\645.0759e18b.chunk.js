"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[645],{1759:(e,t,r)=>{r.d(t,{B:()=>i});r(9643);var a=r(9391),s=r(6507);const n=[{value:a.Cr.EASY,label:"Easy",description:"Basic facts and definitions"},{value:a.Cr.MEDIUM,label:"Medium",description:"Moderate understanding required"},{value:a.Cr.HARD,label:"Hard",description:"Deep analysis and critical thinking"},{value:a.Cr.COLLEGE,label:"College",description:"Undergraduate level complexity"},{value:a.Cr.GRADUATE,label:"Graduate",description:"Advanced graduate study"},{value:a.Cr.PHD,label:"PhD",description:"Research-level expertise"}],i=e=>{let{value:t,onChange:r,className:i="",disabled:o=!1,label:c="Difficulty Level"}=e;return(0,s.jsxs)("div",{className:"space-y-3 ".concat(i),children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-text-primary",children:c}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:n.map(e=>{const n=t===e.value,i=n?(e=>{switch(e){case a.Cr.EASY:return"bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20";case a.Cr.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case a.Cr.HARD:return"bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20";case a.Cr.COLLEGE:return"bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20";case a.Cr.GRADUATE:return"bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20";case a.Cr.PHD:return"bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}})(e.value):(e=>{switch(e){case a.Cr.EASY:return"bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50";case a.Cr.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case a.Cr.HARD:return"bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50";case a.Cr.COLLEGE:return"bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50";case a.Cr.GRADUATE:return"bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50";case a.Cr.PHD:return"bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}})(e.value);return(0,s.jsxs)("button",{type:"button",onClick:()=>!o&&r(e.value),disabled:o,className:"\n                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu\n                ".concat(i,"\n                ").concat(o?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105","\n                ").concat(n?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":"","\n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary\n              "),title:e.description,"aria-pressed":n,children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"font-semibold",children:e.label}),(0,s.jsx)("div",{className:"text-xs mt-1 ".concat(n?"text-white/90":"text-text-secondary"),children:e.description})]}),n&&(0,s.jsx)("div",{className:"absolute top-2 right-2",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},e.value)})}),(0,s.jsx)("p",{className:"text-xs text-text-muted",children:"Select the appropriate difficulty level for your flashcards. This affects the complexity of questions and answers generated."})]})}},2171:(e,t,r)=>{r.d(t,{q:()=>s});var a=r(8957);const s=(0,r(5914).vt)(e=>({documents:[],selectedDocuments:new Set,isLoading:!1,uploadProgress:{},fetchDocuments:async()=>{e({isLoading:!0});try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/documents",{headers:{Authorization:"Bearer ".concat(t)}});if(!r.ok)throw new Error("Failed to fetch documents");const a=await r.json();if(!a.success)throw new Error(a.error);e({documents:a.data,isLoading:!1})}catch(t){throw console.error("Fetch documents error:",t),e({isLoading:!1}),t}},uploadDocument:async t=>{const r=new FormData;r.append("document",t);try{const s=localStorage.getItem("auth_token"),n=await fetch("/api/documents/upload",{method:"POST",headers:{Authorization:"Bearer ".concat(s)},body:r});if(!n.ok){const e=await n.json();throw new Error(e.error||"Upload failed")}const i=await n.json();if(i.success)return e(e=>({documents:[i.data,...e.documents],uploadProgress:(0,a.A)((0,a.A)({},e.uploadProgress),{},{[t.name]:100})})),i.data;throw new Error(i.error)}catch(s){throw console.error("Upload document error:",s),s}},deleteDocument:async t=>{try{const r=localStorage.getItem("auth_token"),a=await fetch("/api/documents/".concat(t),{method:"DELETE",headers:{Authorization:"Bearer ".concat(r)}});if(!a.ok){const e=await a.json();throw new Error(e.error||"Delete failed")}e(e=>({documents:e.documents.filter(e=>e.id!==t),selectedDocuments:new Set([...e.selectedDocuments].filter(e=>e!==t))}))}catch(r){throw console.error("Delete document error:",r),r}},searchDocuments:async e=>{try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/documents/search?q=".concat(encodeURIComponent(e)),{headers:{Authorization:"Bearer ".concat(t)}});if(!r.ok)throw new Error("Search failed");const a=await r.json();return a.success?a.data:[]}catch(t){return console.error("Search documents error:",t),[]}},getDocument:async e=>{try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/documents/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}});if(!r.ok)return null;const a=await r.json();return a.success?a.data:null}catch(t){return console.error("Get document error:",t),null}},toggleDocumentSelection:t=>{e(e=>{const r=new Set(e.selectedDocuments);return r.has(t)?r.delete(t):r.add(t),{selectedDocuments:r}})},clearSelection:()=>{e({selectedDocuments:new Set})},selectAll:()=>{e(e=>({selectedDocuments:new Set(e.documents.map(e=>e.id))}))},setUploadProgress:(t,r)=>{e(e=>({uploadProgress:(0,a.A)((0,a.A)({},e.uploadProgress),{},{[t]:r})}))}}))},7240:(e,t,r)=>{r.d(t,{Q:()=>s});var a=r(9643);const s=()=>{const[e,t]=(0,a.useState)(null),[r,s]=(0,a.useState)(!0),[n,i]=(0,a.useState)(null),o=(0,a.useCallback)(async()=>{try{s(!0),i(null);const e=localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token");if(!e)throw new Error("No authentication token found");const r=await fetch("/api/user/settings",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!r.ok){const e=await r.json();throw new Error(e.error||"Failed to fetch user settings")}const a=await r.json();t(a.data)}catch(e){i(e.message),console.error("Error fetching user settings:",e)}finally{s(!1)}},[]),c=(0,a.useCallback)(async e=>{try{i(null);const r=localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token");if(!r)throw new Error("No authentication token found");const a=await fetch("/api/user/settings",{method:"PATCH",headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){const e=await a.json();throw new Error(e.error||"Failed to update user settings")}const s=await a.json();t(s.data)}catch(r){throw i(r.message),console.error("Error updating user settings:",r),r}},[]),l=(0,a.useCallback)(async()=>{await o()},[o]);return(0,a.useEffect)(()=>{o()},[o]),{settings:e,loading:r,error:n,updateSettings:c,refetch:l}}},8645:(e,t,r)=>{r.r(t),r.d(t,{StudySetPage:()=>E});var a=r(9643),s=r(7192),n=r(9855),i=r(4859),o=r(8957),c=r(6507);const l=e=>{let{value:t,onChange:r,label:s,min:n=1,max:i=100,placeholder:o="Enter a number",error:l,className:d="",disabled:u=!1}=e;const[m,h]=(0,a.useState)(t.toString()),[x,g]=(0,a.useState)("");(0,a.useEffect)(()=>{t.toString()!==m&&h(t.toString())},[t]);const p=l||x;return(0,c.jsxs)("div",{className:d,children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:s}),(0,c.jsxs)("div",{className:"relative",children:[(0,c.jsx)("input",{type:"text",inputMode:"numeric",value:m,onChange:e=>{const t=e.target.value;(""===t||/^\d+$/.test(t))&&(e=>{if(h(e),g(""),""===e.trim())return;if(!/^\d+$/.test(e.trim()))return void g("Please enter a whole number");const t=parseInt(e.trim(),10);t<n?g("Number must be at least ".concat(n)):t>i?g("Number must be at most ".concat(i)):r(t)})(t)},onBlur:()=>{(""===m.trim()||x)&&(h(t.toString()),g(""))},onKeyDown:e=>{["Backspace","Delete","Tab","Escape","Enter","ArrowLeft","ArrowRight","ArrowUp","ArrowDown"].includes(e.key)||e.key>="0"&&e.key<="9"||e.ctrlKey&&["a","c","v","x","z"].includes(e.key.toLowerCase())||e.preventDefault()},placeholder:o,disabled:u,className:"\n            w-full px-3 py-2 pr-20 bg-background-primary border rounded-lg text-white \n            placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors\n            ".concat(p?"border-red-500 focus:border-red-500 focus:ring-red-500/50":"border-gray-600 focus:border-primary-500 focus:ring-primary-500/50","\n            ").concat(u?"opacity-50 cursor-not-allowed":"","\n          ")}),(0,c.jsxs)("div",{className:"absolute right-1 top-1 bottom-1 flex flex-col",children:[(0,c.jsx)("button",{type:"button",onClick:()=>{const e=Math.min(t+1,i);r(e)},disabled:u||t>=i,className:"\r flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 \r rounded-tr-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\r focus:outline-none focus:bg-gray-600\r ",tabIndex:-1,children:(0,c.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,c.jsx)("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})})}),(0,c.jsx)("button",{type:"button",onClick:()=>{const e=Math.max(t-1,n);r(e)},disabled:u||t<=n,className:"\r flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 \r rounded-br-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\r focus:outline-none focus:bg-gray-600\r ",tabIndex:-1,children:(0,c.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,c.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]}),p&&(0,c.jsx)("p",{className:"mt-1 text-sm text-red-400",children:p}),!p&&(0,c.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Enter a number between ",n," and ",i]})]})},d=e=>{let{progress:t=0,isIndeterminate:r=!1,label:a,className:s="",size:n="md",variant:i="primary"}=e;const o={sm:"h-1.5",md:"h-2",lg:"h-3"};return(0,c.jsxs)("div",{className:"w-full ".concat(s),children:[a&&(0,c.jsxs)("div",{className:"flex justify-between items-center mb-2 ".concat({sm:"text-xs",md:"text-sm",lg:"text-base"}[n]),children:[(0,c.jsx)("span",{className:"text-gray-300 font-medium",children:a}),!r&&(0,c.jsxs)("span",{className:"text-gray-400",children:[Math.round(t),"%"]})]}),(0,c.jsx)("div",{className:"w-full bg-gray-700 rounded-full overflow-hidden ".concat(o[n]),children:(0,c.jsx)("div",{className:"".concat({primary:"bg-primary-500",success:"bg-green-500",warning:"bg-yellow-500",error:"bg-red-500"}[i]," transition-all duration-300 ease-out rounded-full ").concat(o[n]," ").concat(r?"animate-pulse w-full":"transition-[width] duration-500"),style:r?void 0:{width:"".concat(Math.min(100,Math.max(0,t)),"%")}})})]})},u=e=>{let{isGenerating:t,stage:r,estimatedTime:s,className:n=""}=e;const[i,o]=a.useState(0),[l,u]=a.useState(0);if(a.useEffect(()=>{let e,r;return t?(u(0),o(0),e=setInterval(()=>{u(e=>e+1)},1e3),r=setInterval(()=>{o(e=>{const t=e<30?3:e<60?2:e<85?1:.2;return Math.min(90,e+t)})},1e3)):(o(0),u(0)),()=>{e&&clearInterval(e),r&&clearInterval(r)}},[t]),a.useEffect(()=>{!t&&i>0&&(o(100),setTimeout(()=>o(0),1e3))},[t,i]),!t&&0===i)return null;const m=e=>{if(e<60)return"".concat(e,"s");const t=Math.floor(e/60),r=e%60;return"".concat(t,"m ").concat(r,"s")};return(0,c.jsx)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 ".concat(n),children:(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,c.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-primary-500 border-t-transparent"}),(0,c.jsx)("span",{className:"text-white font-medium",children:r||"Generating with AI..."})]}),(0,c.jsxs)("div",{className:"text-sm text-gray-400",children:[l>0&&m(l),s&&0===l&&" (Est. ".concat(m(s),")")]})]}),(0,c.jsx)(d,{progress:i,isIndeterminate:0===i,variant:"primary",size:"md"}),(0,c.jsx)("div",{className:"text-xs text-gray-500 text-center",children:t?"Please wait while we generate your content...":"Generation complete!"})]})})};var m=r(1721),h=r(2171);const x=e=>{let{value:t,onChange:r,min:s,max:n,label:i,placeholder:o}=e;const[l,d]=(0,a.useState)(t.toString()),[u,m]=(0,a.useState)("");(0,a.useEffect)(()=>{d(t.toString())},[t]);return(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsxs)("label",{className:"text-xs text-gray-400 whitespace-nowrap",children:[i,":"]}),(0,c.jsxs)("div",{className:"relative",children:[(0,c.jsx)("input",{type:"text",value:l,onChange:e=>(e=>{if(d(e),u&&m(""),""===e)return;const t=parseInt(e);isNaN(t)?m("Must be a number"):t<s?m("Minimum is ".concat(s)):t>n?m("Maximum is ".concat(n)):r(t)})(e.target.value),onBlur:()=>{const e=parseInt(l);(isNaN(e)||""===l)&&(d(t.toString()),m(""))},placeholder:o,className:"w-16 px-2 py-1 text-xs rounded text-white text-center focus:outline-none focus:ring-1 focus:ring-primary-500 ".concat(u?"bg-red-900/20 border border-red-500":"bg-background-primary border border-gray-600")}),u&&(0,c.jsx)("div",{className:"absolute top-full left-0 mt-1 text-xs text-red-400 whitespace-nowrap z-10",children:u})]})]})},g=e=>{let{selectedDocuments:t,onSelectionChange:r,documentPageRanges:s,onPageRangeChange:n,maxSelection:i=5}=e;const{documents:l,fetchDocuments:d,isLoading:u}=(0,h.q)(),[m,g]=(0,a.useState)("");(0,a.useEffect)(()=>{0===l.length&&d()},[l.length,d]);const p=l.filter(e=>e.is_processed&&e.filename.toLowerCase().includes(m.toLowerCase())),y=e=>{if(t.includes(e))r(t.filter(t=>t!==e));else if(t.length<i){r([...t,e]);const a=l.find(t=>t.id===e);null!==a&&void 0!==a&&a.page_count&&a.page_count>0&&n(e,{startPage:1,endPage:a.page_count})}},f=(e,t,r)=>{const a=s[e]||{startPage:1,endPage:1},i=l.find(t=>t.id===e),c=(null===i||void 0===i?void 0:i.page_count)||1;let d=(0,o.A)({},a);"startPage"===t?d.startPage=Math.max(1,Math.min(r,c,d.endPage)):d.endPage=Math.max(d.startPage,Math.min(r,c)),n(e,d)};return u?(0,c.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,c.jsx)("div",{className:"text-gray-400",children:"Loading documents..."})}):0===l.length?(0,c.jsxs)("div",{className:"text-center py-8",children:[(0,c.jsx)("div",{className:"text-gray-400 mb-4",children:"No documents found"}),(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Upload some documents first to generate study materials."})]}):(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsx)("div",{children:(0,c.jsx)("input",{type:"text",placeholder:"Search documents...",value:m,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})}),t.length>0&&(0,c.jsxs)("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3",children:[(0,c.jsxs)("div",{className:"text-sm text-primary-400 mb-2",children:["Selected ",t.length," of ",i," documents:"]}),(0,c.jsx)("div",{className:"space-y-3",children:l.filter(e=>t.includes(e.id)).map(e=>{var t;const r=e.page_count&&e.page_count>0,a=s[e.id];return(0,c.jsxs)("div",{className:"bg-background-secondary/50 rounded-lg p-3",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,c.jsx)("span",{className:"text-sm text-gray-300 truncate flex-1 mr-2",children:e.filename}),(0,c.jsx)("button",{onClick:()=>y(e.id),className:"text-red-400 hover:text-red-300 text-sm px-2 py-1 rounded hover:bg-red-400/10 transition-colors",children:"Remove"})]}),r&&(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{className:"text-xs text-gray-400",children:["\ud83d\udcc4 ",e.page_count," ","pptx"===e.file_type?"slides":"pages"," ","available"]}),(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsx)(x,{label:"From",value:(null===a||void 0===a?void 0:a.startPage)||1,onChange:t=>f(e.id,"startPage",t),min:1,max:e.page_count||1,placeholder:"1"}),(0,c.jsx)(x,{label:"To",value:(null===a||void 0===a?void 0:a.endPage)||e.page_count||1,onChange:t=>f(e.id,"endPage",t),min:(null===a||void 0===a?void 0:a.startPage)||1,max:e.page_count||1,placeholder:(null===(t=e.page_count)||void 0===t?void 0:t.toString())||"1"})]}),(0,c.jsx)("button",{onClick:()=>n(e.id,{startPage:1,endPage:e.page_count||1}),className:"text-xs text-primary-400 hover:text-primary-300 px-2 py-1 rounded hover:bg-primary-400/10 transition-colors whitespace-nowrap",children:"Use All"})]}),a&&(0,c.jsxs)("div",{className:"text-xs text-gray-500",children:["Using ",a.endPage-a.startPage+1," of"," ",e.page_count," ","pptx"===e.file_type?"slides":"pages"]})]}),!r&&(0,c.jsxs)("div",{className:"text-xs text-gray-500",children:["\ud83d\udcc4 ",e.file_type.toUpperCase()," \u2022 Full document will be used"]})]},e.id)})})]}),(0,c.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-2",children:p.map(e=>{const r=t.includes(e.id),a=!r&&t.length<i,s=e.page_count&&e.page_count>0;return(0,c.jsx)("div",{className:"\n                p-3 rounded-lg border cursor-pointer transition-all\n                ".concat(r?"bg-primary-500/20 border-primary-500":a?"bg-background-secondary border-gray-600 hover:border-gray-500":"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed","\n              "),onClick:()=>a||r?y(e.id):null,children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsx)("div",{className:"flex-1 min-w-0",children:(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("span",{className:"text-lg",children:"pdf"===e.file_type?"\ud83d\udcc4":"docx"===e.file_type?"\ud83d\udcdd":"txt"===e.file_type?"\ud83d\udcc3":"\ud83d\udcca"}),(0,c.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,c.jsx)("p",{className:"text-white font-medium truncate",children:e.filename}),(0,c.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,c.jsxs)("span",{children:[e.file_type.toUpperCase()," \u2022"," ",Math.round(e.file_size/1024)," KB"]}),s&&(0,c.jsxs)("span",{children:["\u2022 ",e.page_count," ","pptx"===e.file_type?"slides":"pages"]})]})]})]})}),(0,c.jsx)("div",{className:"\n                  w-5 h-5 rounded border-2 flex items-center justify-center\n                  ".concat(r?"bg-primary-500 border-primary-500":"border-gray-500","\n                "),children:r&&(0,c.jsx)("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,c.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})},e.id)})}),0===p.length&&m&&(0,c.jsx)("div",{className:"text-center py-4 text-gray-400",children:"No documents match your search."})]})};var p=r(1759),y=r(9391);const f=[{value:y.qt.SHORT,label:"Short",description:"Concise answers (1-2 sentences)",icon:"\ud83d\udcdd"},{value:y.qt.MEDIUM,label:"Medium",description:"Balanced detail (2-3 sentences)",icon:"\ud83d\udcc4"},{value:y.qt.LONG,label:"Long",description:"Comprehensive answers (3-5 sentences)",icon:"\ud83d\udccb"}],b=e=>{let{value:t,onChange:r,className:a="",disabled:s=!1,label:n="Content Length"}=e;return(0,c.jsxs)("div",{className:"space-y-3 ".concat(a),children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-text-primary",children:n}),(0,c.jsx)("div",{className:"grid grid-cols-3 gap-3",children:f.map(e=>{const a=t===e.value,n=a?(e=>{switch(e){case y.qt.SHORT:return"bg-emerald-500/20 text-emerald-300 border-emerald-500 shadow-lg shadow-emerald-500/20";case y.qt.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case y.qt.LONG:return"bg-indigo-500/20 text-indigo-300 border-indigo-500 shadow-lg shadow-indigo-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}})(e.value):(e=>{switch(e){case y.qt.SHORT:return"bg-background-secondary text-text-primary border-emerald-500/30 hover:bg-emerald-500/10 hover:border-emerald-500/50";case y.qt.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case y.qt.LONG:return"bg-background-secondary text-text-primary border-indigo-500/30 hover:bg-indigo-500/10 hover:border-indigo-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}})(e.value);return(0,c.jsxs)("button",{type:"button",onClick:()=>!s&&r(e.value),disabled:s,className:"\n                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu\n                ".concat(n,"\n                ").concat(s?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105","\n                ").concat(a?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":"","\n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary\n              "),title:e.description,"aria-pressed":a,children:[(0,c.jsxs)("div",{className:"text-center space-y-2",children:[(0,c.jsx)("div",{className:"text-2xl",children:e.icon}),(0,c.jsx)("div",{className:"font-semibold",children:e.label}),(0,c.jsx)("div",{className:"text-xs ".concat(a?"text-white/90":"text-text-secondary"),children:e.description})]}),a&&(0,c.jsx)("div",{className:"absolute top-2 right-2",children:(0,c.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,c.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},e.value)})}),(0,c.jsx)("p",{className:"text-xs text-text-muted",children:"Choose how detailed you want the flashcard answers to be. This affects the length and depth of explanations."})]})};var v=r(2086);const w=(0,r(5914).vt)(e=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async t=>{e({isGenerating:!0,generationProgress:"Preparing documents..."});try{const s=localStorage.getItem("auth_token");e({generationProgress:"Generating flashcards with AI..."});const n=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify(t)});if(!n.ok){const e=await n.json();throw new Error(e.error||"Generation failed")}const i=await n.json();if(i.success){e({lastGenerated:{studySet:i.data.studySet,content:i.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""});try{const{useStudyStore:e}=await Promise.resolve().then(r.bind(r,9855)),t=e.getState();t.refreshStudySetContent(i.data.studySet.id),t.invalidateStudySets()}catch(a){console.warn("Failed to refresh study set cache:",a)}return{studySet:i.data.studySet,flashcards:i.data.flashcards,creditsRemaining:i.data.creditsRemaining}}throw new Error(i.error)}catch(s){throw e({isGenerating:!1,generationProgress:""}),s}},generateQuiz:async t=>{e({isGenerating:!0,generationProgress:"Preparing documents..."});try{const s=localStorage.getItem("auth_token");e({generationProgress:"Generating quiz questions with AI..."});const n=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify(t)});if(!n.ok){const e=await n.json();throw new Error(e.error||"Generation failed")}const i=await n.json();if(i.success){e({lastGenerated:{studySet:i.data.studySet,content:i.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""});try{const{useStudyStore:e}=await Promise.resolve().then(r.bind(r,9855)),t=e.getState();t.refreshStudySetContent(i.data.studySet.id),t.invalidateStudySets()}catch(a){console.warn("Failed to refresh study set cache:",a)}return{studySet:i.data.studySet,questions:i.data.questions,creditsRemaining:i.data.creditsRemaining}}throw new Error(i.error)}catch(s){throw e({isGenerating:!1,generationProgress:""}),s}},generateMoreFlashcards:async t=>{e({isGenerating:!0,generationProgress:"Preparing documents..."});try{const s=localStorage.getItem("auth_token");e({generationProgress:"Generating additional flashcards with AI..."});const n=await fetch("/api/ai/generate-more-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify(t)});if(!n.ok){const e=await n.json();throw new Error(e.error||"Generation failed")}const i=await n.json();if(i.success){e({isGenerating:!1,generationProgress:""});try{const{useStudyStore:e}=await Promise.resolve().then(r.bind(r,9855));e.getState().refreshStudySetContent(t.studySetId)}catch(a){console.warn("Failed to refresh study set cache:",a)}return{flashcards:i.data.flashcards,creditsRemaining:i.data.creditsRemaining}}throw new Error(i.error)}catch(s){throw e({isGenerating:!1,generationProgress:""}),s}},generateMoreQuizQuestions:async t=>{e({isGenerating:!0,generationProgress:"Preparing documents..."});try{const r=localStorage.getItem("auth_token");e({generationProgress:"Generating additional quiz questions with AI..."});const a=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r)},body:JSON.stringify(t)});if(!a.ok){const e=await a.json();throw new Error(e.error||"Generation failed")}const s=await a.json();if(s.success)return e({isGenerating:!1,generationProgress:""}),{questions:s.data.questions,creditsRemaining:s.data.creditsRemaining};throw new Error(s.error)}catch(r){throw e({isGenerating:!1,generationProgress:""}),r}},clearLastGenerated:()=>{e({lastGenerated:null})}}));var j=r(7240);const N=e=>{let{selectedCount:t,totalCount:r,onDeleteSelected:a,onClearSelection:s,isLoading:n=!1,className:o="",itemType:l="item"}=e;return 0===t?null:(0,c.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-4 mb-4 ".concat(o),children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsxs)("span",{className:"text-white font-medium",children:[t," of ",r," ",l,1!==t?"s":""," selected"]}),(0,c.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-white text-sm underline",disabled:n,children:"Clear selection"})]}),(0,c.jsx)("div",{className:"flex items-center space-x-3",children:(0,c.jsx)(i.$,{onClick:a,variant:"danger",size:"sm",isLoading:n,disabled:n,className:"px-4 py-2",children:"Delete Selected"})})]})})};var S=r(344);const k=e=>{let{studySetId:t,flashcards:s,onFlashcardAdded:n,onFlashcardUpdated:d,onFlashcardDeleted:h,onFlashcardsGenerated:x}=e;const{alert:f,confirm:k}=(0,m.s)(),{user:_}=(0,v.A)(),{generateMoreFlashcards:C}=w(),{settings:A,updateSettings:E}=(0,j.Q)(),[q,I]=(0,a.useState)(!0),[D,T]=(0,a.useState)([]),[F,P]=(0,a.useState)({}),[M,G]=(0,a.useState)(25),[z,L]=(0,a.useState)(""),[R,O]=(0,a.useState)(y.Cr.MEDIUM),[Q,U]=(0,a.useState)(y.qt.MEDIUM),[B,H]=(0,a.useState)(!1),[V,$]=(0,a.useState)(!1),[J,Y]=(0,a.useState)({front:"",back:"",difficulty_level:3}),[K,X]=(0,a.useState)(null),[W,Z]=(0,a.useState)({front:"",back:"",difficulty_level:3}),[ee,te]=(0,a.useState)([]),[re,ae]=(0,a.useState)(!1),[se,ne]=(0,a.useState)(!1),[ie,oe]=(0,a.useState)(new Set),ce=()=>{te([]),ae(!1)},le=async()=>{ne(!0);try{const e=await fetch("/api/flashcards/bulk-delete",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token")),"Content-Type":"application/json"},body:JSON.stringify({flashcardIds:ee})});if(!e.ok){const t=await e.json();throw new Error(t.error||"Failed to delete flashcards")}const t=await e.json(),{deletedCount:r}=t.data;ee.forEach(e=>h(e)),ce(),await f({title:"Success",message:"".concat(r," flashcard").concat(1!==r?"s":""," deleted successfully!"),variant:"success"})}catch(e){await f({title:"Error",message:e.message||"Failed to delete flashcards",variant:"error"})}finally{ne(!1)}},de=async e=>{try{if(!(await fetch("/api/flashcards/".concat(e.id),{method:"DELETE",headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}})).ok)throw new Error("Failed to delete flashcard");h(e.id),await f({title:"Success",message:"Flashcard deleted successfully!",variant:"success"})}catch(t){await f({title:"Error",message:t.message||"Failed to delete flashcard",variant:"error"})}},ue=()=>Math.ceil(M/5);return(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsx)("h3",{className:"text-lg font-medium text-white",children:"Manage Flashcards"}),(0,c.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,c.jsx)(i.$,{onClick:()=>$(!V),variant:"secondary",size:"sm",children:"\u2795 Add Flashcard"}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("span",{className:"text-sm text-gray-400",children:"AI Mode"}),(0,c.jsx)("button",{onClick:()=>I(!q),className:"\n                relative inline-flex h-6 w-11 items-center rounded-full transition-colors\n                ".concat(q?"bg-primary-500":"bg-gray-600","\n              "),children:(0,c.jsx)("span",{className:"\n                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform\n                  ".concat(q?"translate-x-6":"translate-x-1","\n                ")})})]})]})]}),V&&(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[(0,c.jsx)("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Flashcard"}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),(0,c.jsx)("textarea",{value:J.front,onChange:e=>Y(t=>(0,o.A)((0,o.A)({},t),{},{front:e.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),(0,c.jsx)("textarea",{value:J.back,onChange:e=>Y(t=>(0,o.A)((0,o.A)({},t),{},{back:e.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),(0,c.jsxs)("select",{value:J.difficulty_level,onChange:e=>Y(t=>(0,o.A)((0,o.A)({},t),{},{difficulty_level:parseInt(e.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[(0,c.jsx)("option",{value:1,children:"1 - Very Easy"}),(0,c.jsx)("option",{value:2,children:"2 - Easy"}),(0,c.jsx)("option",{value:3,children:"3 - Medium"}),(0,c.jsx)("option",{value:4,children:"4 - Hard"}),(0,c.jsx)("option",{value:5,children:"5 - Very Hard"})]})]}),(0,c.jsxs)("div",{className:"flex space-x-3",children:[(0,c.jsx)(i.$,{onClick:async()=>{if(J.front.trim()&&J.back.trim())try{const e=await fetch("/api/flashcards/study-set/".concat(t),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))},body:JSON.stringify({front:J.front.trim(),back:J.back.trim(),difficulty_level:J.difficulty_level,is_ai_generated:!1})});if(!e.ok)throw new Error("Failed to create flashcard");const r=await e.json();n(r.data),Y({front:"",back:"",difficulty_level:3}),$(!1),await f({title:"Success",message:"Flashcard added successfully!",variant:"success"})}catch(e){await f({title:"Error",message:e.message||"Failed to add flashcard",variant:"error"})}else await f({title:"Validation Error",message:"Both front and back content are required.",variant:"error"})},variant:"primary",children:"Add Flashcard"}),(0,c.jsx)(i.$,{onClick:()=>$(!1),variant:"secondary",children:"Cancel"})]})]})]}),K&&(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[(0,c.jsx)("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Flashcard"}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),(0,c.jsx)("textarea",{value:W.front,onChange:e=>Z(t=>(0,o.A)((0,o.A)({},t),{},{front:e.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),(0,c.jsx)("textarea",{value:W.back,onChange:e=>Z(t=>(0,o.A)((0,o.A)({},t),{},{back:e.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),(0,c.jsxs)("select",{value:W.difficulty_level,onChange:e=>Z(t=>(0,o.A)((0,o.A)({},t),{},{difficulty_level:parseInt(e.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[(0,c.jsx)("option",{value:1,children:"1 - Very Easy"}),(0,c.jsx)("option",{value:2,children:"2 - Easy"}),(0,c.jsx)("option",{value:3,children:"3 - Medium"}),(0,c.jsx)("option",{value:4,children:"4 - Hard"}),(0,c.jsx)("option",{value:5,children:"5 - Very Hard"})]})]}),(0,c.jsxs)("div",{className:"flex space-x-3",children:[(0,c.jsx)(i.$,{onClick:async()=>{if(K)if(W.front.trim()&&W.back.trim())try{const e=await fetch("/api/flashcards/".concat(K.id),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))},body:JSON.stringify({front:W.front.trim(),back:W.back.trim(),difficulty_level:W.difficulty_level})});if(!e.ok)throw new Error("Failed to update flashcard");const t=await e.json();d(t.data),X(null),Z({front:"",back:"",difficulty_level:3}),await f({title:"Success",message:"Flashcard updated successfully!",variant:"success"})}catch(e){await f({title:"Error",message:e.message||"Failed to update flashcard",variant:"error"})}else await f({title:"Validation Error",message:"Both front and back content are required.",variant:"error"})},variant:"primary",children:"Save Changes"}),(0,c.jsx)(i.$,{onClick:()=>{X(null),Z({front:"",back:"",difficulty_level:3})},variant:"secondary",children:"Cancel"})]})]})]}),q&&(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[(0,c.jsx)("h4",{className:"text-md font-medium text-white mb-4",children:"AI Flashcard Generation"}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),(0,c.jsx)(g,{selectedDocuments:D,onSelectionChange:T,documentPageRanges:F,onPageRangeChange:(e,t)=>{P(r=>(0,o.A)((0,o.A)({},r),{},{[e]:t}))},maxSelection:5})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,c.jsx)(l,{label:"Number of Flashcards",value:M,onChange:G,min:1,max:100,placeholder:"Enter number (1-100)",disabled:B}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),(0,c.jsxs)("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[ue()," credits"]})]})]}),(0,c.jsx)(p.B,{value:R,onChange:O}),(0,c.jsx)(b,{value:Q,onChange:U}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),(0,c.jsx)("textarea",{value:z,onChange:e=>L(e.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for flashcard generation..."})]}),(0,c.jsx)(i.$,{onClick:async()=>{if(0===D.length)return void await f({title:"No Documents Selected",message:"Please select at least one document to generate flashcards from.",variant:"warning"});const e=ue();if(_&&_.credits_remaining<e)return void await f({title:"Insufficient Credits",message:"You need ".concat(e," credits to generate ").concat(M," flashcards, but you only have ").concat(_.credits_remaining," credits remaining."),variant:"error"});if(await k({title:"Generate Flashcards",message:"Generate ".concat(M," flashcards from ").concat(D.length," document(s)?\n\nThis will cost ").concat(e," credits."),confirmText:"Generate",cancelText:"Cancel"})){H(!0);try{const e=await C({studySetId:t,documentIds:D,documentPageRanges:F,count:M,customPrompt:z.trim()||void 0,difficultyLevel:R,contentLength:Q,existingContent:s.map(e=>e.front)});x(e.flashcards),_&&v.A.getState().updateUser({credits_remaining:e.creditsRemaining});try{const{useStudyStore:e}=await Promise.resolve().then(r.bind(r,9855)),a=e.getState();await a.refreshStudySetContent(t)}catch(a){console.warn("Failed to refresh study set cache:",a)}await f({title:"Success",message:"Generated ".concat(e.flashcards.length," flashcards successfully!"),variant:"success"}),T([]),L(""),I(!1)}catch(n){await f({title:"Generation Error",message:n.message||"Failed to generate flashcards",variant:"error"})}finally{H(!1)}}},disabled:0===D.length||B,className:"w-full",variant:"primary",children:B?"Generating...":"Generate ".concat(M," Flashcards")})]})]}),(0,c.jsx)(u,{isGenerating:B,stage:B?"Generating flashcards with AI...":void 0,estimatedTime:Math.ceil(M/10)}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("h4",{className:"text-md font-medium text-white",children:["Current Flashcards (",s.length,")"]}),s.length>0&&(0,c.jsx)("div",{className:"flex items-center space-x-3",children:(0,c.jsxs)("label",{className:"flex items-center space-x-2 text-sm text-gray-300",children:[(0,c.jsx)("input",{type:"checkbox",checked:re,onChange:e=>{return t=e.target.checked,ae(t),void te(t?s.map(e=>e.id):[]);var t},className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"}),(0,c.jsx)("span",{children:"Select All"})]})})]}),(0,c.jsx)(N,{selectedCount:ee.length,totalCount:s.length,onDeleteSelected:async()=>{if(0!==ee.length){if(null===A||void 0===A||!A.skip_delete_confirmations){let t=!1;if(!await k({title:"Delete Flashcards",message:"Are you sure you want to delete ".concat(ee.length," flashcard").concat(1!==ee.length?"s":"","?"),variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:e=>{t=e}}))return;if(t)try{await E({skip_delete_confirmations:!0})}catch(e){console.error("Failed to update user settings:",e)}}await le()}},onClearSelection:ce,isLoading:se}),s.length>0&&(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsx)("p",{className:"text-sm text-gray-400",children:"\ud83d\udca1 Click on any flashcard to reveal/hide its answer"}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsxs)("button",{onClick:()=>{const e=new Set(s.map(e=>e.id));oe(e)},className:"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm",children:[(0,c.jsx)(S.zeF,{className:"w-4 h-4"}),(0,c.jsx)("span",{children:"Show All"})]}),(0,c.jsxs)("button",{onClick:()=>{oe(new Set)},className:"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm",children:[(0,c.jsx)(S.QLA,{className:"w-4 h-4"}),(0,c.jsx)("span",{children:"Hide All"})]})]})]}),0===s.length?(0,c.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No flashcards yet. Add some manually or generate them with AI."}):(0,c.jsx)("div",{className:"space-y-2",children:s.map(e=>(0,c.jsx)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:(0,c.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,c.jsx)("div",{className:"flex-shrink-0 pt-1",children:(0,c.jsx)("input",{type:"checkbox",checked:ee.includes(e.id),onChange:t=>{return r=e.id,void(t.target.checked?te(e=>[...e,r]):(te(e=>e.filter(e=>e!==r)),ae(!1)));var r},className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"})}),(0,c.jsx)("div",{className:"flex-1 min-w-0",children:(0,c.jsxs)("div",{className:"flex items-start justify-between",children:[(0,c.jsxs)("div",{className:"flex-1 min-w-0 cursor-pointer group",onClick:()=>{return t=e.id,void oe(e=>{const r=new Set(e);return r.has(t)?r.delete(t):r.add(t),r});var t},title:ie.has(e.id)?"Click to hide answer":"Click to reveal answer",children:[(0,c.jsxs)("div",{className:"mb-2",children:[(0,c.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Front"}),(0,c.jsx)("p",{className:"text-white font-medium group-hover:text-primary-300 transition-colors",children:e.front})]}),ie.has(e.id)?(0,c.jsxs)("div",{className:"mb-2",children:[(0,c.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Back"}),(0,c.jsx)("p",{className:"text-gray-300 group-hover:text-gray-200 transition-colors",children:e.back})]}):(0,c.jsx)("div",{className:"mb-2",children:(0,c.jsx)("p",{className:"text-gray-500 italic text-sm group-hover:text-primary-400 transition-colors",children:"Click to reveal answer..."})}),(0,c.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[e.is_ai_generated&&(0,c.jsx)("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),e.difficulty_level&&(0,c.jsxs)("span",{children:["Difficulty:"," ","string"===typeof e.difficulty_level?(0,y.eX)(e.difficulty_level):(0,y.eX)((0,y.KF)(e.difficulty_level))]}),(0,c.jsxs)("span",{children:["Reviewed: ",e.times_reviewed||0," times"]})]})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,c.jsx)("button",{onClick:()=>(e=>{X(e),Z({front:e.front,back:e.back,difficulty_level:"string"===typeof e.difficulty_level?(0,y.sQ)(e.difficulty_level):e.difficulty_level||3})})(e),className:"text-gray-400 hover:text-white p-1",title:"Edit flashcard",children:"\u270f\ufe0f"}),(0,c.jsx)("button",{onClick:()=>(async e=>{if(null!==A&&void 0!==A&&A.skip_delete_confirmations)return void await de(e);let t=!1;if(await k({title:"Delete Flashcard",message:"Are you sure you want to delete this flashcard?\n\nFront: ".concat(e.front.substring(0,50)).concat(e.front.length>50?"...":""),variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:e=>{t=e}})){if(t)try{await E({skip_delete_confirmations:!0})}catch(r){console.error("Failed to update user settings:",r)}await de(e)}})(e),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete flashcard",children:"\ud83d\uddd1\ufe0f"})]})]})})]})},e.id))})]})]})};var _=r(1820);const C=e=>{let{studySetId:t,questions:s,onQuestionAdded:n,onQuestionUpdated:d,onQuestionDeleted:h,onQuestionsGenerated:x}=e;const{alert:f,confirm:w}=(0,m.s)(),{user:j}=(0,v.A)(),[k,C]=(0,a.useState)(!0),[A,E]=(0,a.useState)([]),[q,I]=(0,a.useState)({}),[D,T]=(0,a.useState)(25),[F,P]=(0,a.useState)(""),[M,G]=(0,a.useState)(y.Cr.MEDIUM),[z,L]=(0,a.useState)(y.qt.MEDIUM),[R,O]=(0,a.useState)(["multiple_choice","select_all","true_false","short_answer"]),[Q,U]=(0,a.useState)(!1),[B,H]=(0,a.useState)(!1),[V,$]=(0,a.useState)({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),[J,Y]=(0,a.useState)(null),[K,X]=(0,a.useState)([]),[W,Z]=(0,a.useState)(!1),[ee,te]=(0,a.useState)(!1),[re,ae]=(0,a.useState)({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),[se,ne]=(0,a.useState)(new Set),ie=()=>{X([]),Z(!1)},oe=()=>Math.ceil(D/5),ce=e=>{O(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Question Management"}),(0,c.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,c.jsx)(i.$,{onClick:()=>H(!B),variant:"secondary",size:"sm",children:B?"Cancel":"Add Question"}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("span",{className:"text-sm text-gray-300",children:"AI Mode"}),(0,c.jsx)("button",{onClick:()=>C(!k),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(k?"bg-primary-500":"bg-gray-600"),children:(0,c.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(k?"translate-x-6":"translate-x-1")})})]})]})]}),B&&(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[(0,c.jsx)("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Question"}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),(0,c.jsx)("textarea",{value:V.question_text,onChange:e=>$(t=>(0,o.A)((0,o.A)({},t),{},{question_text:e.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),(0,c.jsxs)("select",{value:V.question_type,onChange:e=>{return t=e.target.value,void $(e=>(0,o.A)((0,o.A)({},e),{},{question_type:t,options:"multiple_choice"===t||"select_all"===t?["","","",""]:[],correct_answers:[]}));var t},className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[(0,c.jsx)("option",{value:"multiple_choice",children:"Multiple Choice"}),(0,c.jsx)("option",{value:"select_all",children:"Select All That Apply"}),(0,c.jsx)("option",{value:"true_false",children:"True/False"}),(0,c.jsx)("option",{value:"short_answer",children:"Short Answer"})]})]}),("multiple_choice"===V.question_type||"select_all"===V.question_type)&&(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),(0,c.jsx)("div",{className:"space-y-2",children:V.options.map((e,t)=>(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("button",{type:"button",onClick:()=>{return t=e,void $(e=>{const r=e.correct_answers.includes(t);return"multiple_choice"===e.question_type?(0,o.A)((0,o.A)({},e),{},{correct_answers:r?[]:[t]}):(0,o.A)((0,o.A)({},e),{},{correct_answers:r?e.correct_answers.filter(e=>e!==t):[...e.correct_answers,t]})});var t},className:"flex-shrink-0 w-5 h-5 border-2 ".concat("multiple_choice"===V.question_type?"rounded-full":"rounded"," ").concat(V.correct_answers.includes(e)?"bg-primary-500 border-primary-500":"border-gray-400"," flex items-center justify-center"),disabled:!e.trim(),children:V.correct_answers.includes(e)&&(0,c.jsx)("span",{className:"text-white text-xs",children:"\u2713"})}),(0,c.jsx)("input",{type:"text",value:e,onChange:e=>((e,t)=>{$(r=>(0,o.A)((0,o.A)({},r),{},{options:r.options.map((r,a)=>a===e?t:r)}))})(t,e.target.value),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:"Option ".concat(t+1)})]},t))}),(0,c.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"multiple_choice"===V.question_type?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),"true_false"===V.question_type&&(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),(0,c.jsxs)("div",{className:"flex space-x-4",children:[(0,c.jsx)("button",{type:"button",onClick:()=>$(e=>(0,o.A)((0,o.A)({},e),{},{correct_answers:["True"]})),className:"px-4 py-2 rounded-lg border ".concat(V.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"),children:"True"}),(0,c.jsx)("button",{type:"button",onClick:()=>$(e=>(0,o.A)((0,o.A)({},e),{},{correct_answers:["False"]})),className:"px-4 py-2 rounded-lg border ".concat(V.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"),children:"False"})]})]}),"short_answer"===V.question_type&&(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),(0,c.jsx)("textarea",{value:V.correct_answers.join("\n"),onChange:e=>$(t=>(0,o.A)((0,o.A)({},t),{},{correct_answers:e.target.value.split("\n").filter(e=>e.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),(0,c.jsx)("textarea",{value:V.explanation,onChange:e=>$(t=>(0,o.A)((0,o.A)({},t),{},{explanation:e.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),(0,c.jsxs)("select",{value:V.difficulty_level,onChange:e=>$(t=>(0,o.A)((0,o.A)({},t),{},{difficulty_level:parseInt(e.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[(0,c.jsx)("option",{value:1,children:"1 - Very Easy"}),(0,c.jsx)("option",{value:2,children:"2 - Easy"}),(0,c.jsx)("option",{value:3,children:"3 - Medium"}),(0,c.jsx)("option",{value:4,children:"4 - Hard"}),(0,c.jsx)("option",{value:5,children:"5 - Very Hard"})]})]}),(0,c.jsxs)("div",{className:"flex space-x-3",children:[(0,c.jsx)(i.$,{onClick:async()=>{if(V.question_text.trim())if(0!==V.correct_answers.length){if("multiple_choice"===V.question_type||"select_all"===V.question_type){if(V.options.filter(e=>e.trim().length>0).length<2)return void await f({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"})}try{const e=await fetch("/api/quiz-questions/study-set/".concat(t),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))},body:JSON.stringify({question_text:V.question_text.trim(),question_type:V.question_type,options:"multiple_choice"===V.question_type||"select_all"===V.question_type?V.options.filter(e=>e.trim().length>0):null,correct_answers:V.correct_answers,explanation:V.explanation.trim()||null,difficulty_level:V.difficulty_level})});if(!e.ok)throw new Error("Failed to create question");const r=await e.json();n(r.data),$({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),H(!1),await f({title:"Success",message:"Question added successfully!",variant:"success"})}catch(e){await f({title:"Error",message:e.message||"Failed to add question",variant:"error"})}}else await f({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});else await f({title:"Validation Error",message:"Question text is required.",variant:"error"})},variant:"primary",children:"Add Question"}),(0,c.jsx)(i.$,{onClick:()=>H(!1),variant:"secondary",children:"Cancel"})]})]})]}),J&&(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[(0,c.jsx)("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Question"}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),(0,c.jsx)("textarea",{value:re.question_text,onChange:e=>ae(t=>(0,o.A)((0,o.A)({},t),{},{question_text:e.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),(0,c.jsxs)("select",{value:re.question_type,onChange:e=>ae(t=>(0,o.A)((0,o.A)({},t),{},{question_type:e.target.value,options:"multiple_choice"===e.target.value||"select_all"===e.target.value?["","","",""]:[],correct_answers:[]})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[(0,c.jsx)("option",{value:"multiple_choice",children:"Multiple Choice"}),(0,c.jsx)("option",{value:"select_all",children:"Select All That Apply"}),(0,c.jsx)("option",{value:"true_false",children:"True/False"}),(0,c.jsx)("option",{value:"short_answer",children:"Short Answer"})]})]}),("multiple_choice"===re.question_type||"select_all"===re.question_type)&&(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),(0,c.jsx)("div",{className:"space-y-2",children:re.options.map((e,t)=>(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("button",{type:"button",onClick:()=>{const t=re.correct_answers.includes(e);"multiple_choice"===re.question_type?ae(r=>(0,o.A)((0,o.A)({},r),{},{correct_answers:t?[]:[e]})):ae(r=>(0,o.A)((0,o.A)({},r),{},{correct_answers:t?r.correct_answers.filter(t=>t!==e):[...r.correct_answers,e]}))},className:"flex-shrink-0 w-5 h-5 border-2 ".concat("multiple_choice"===re.question_type?"rounded-full":"rounded"," ").concat(re.correct_answers.includes(e)?"bg-primary-500 border-primary-500":"border-gray-400"," flex items-center justify-center"),disabled:!e.trim(),children:re.correct_answers.includes(e)&&(0,c.jsx)("span",{className:"text-white text-xs",children:"\u2713"})}),(0,c.jsx)("input",{type:"text",value:e,onChange:e=>ae(r=>(0,o.A)((0,o.A)({},r),{},{options:r.options.map((r,a)=>a===t?e.target.value:r)})),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:"Option ".concat(t+1)})]},t))}),(0,c.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"multiple_choice"===re.question_type?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),"true_false"===re.question_type&&(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),(0,c.jsxs)("div",{className:"flex space-x-4",children:[(0,c.jsx)("button",{type:"button",onClick:()=>ae(e=>(0,o.A)((0,o.A)({},e),{},{correct_answers:["True"]})),className:"px-4 py-2 rounded-lg border ".concat(re.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"),children:"True"}),(0,c.jsx)("button",{type:"button",onClick:()=>ae(e=>(0,o.A)((0,o.A)({},e),{},{correct_answers:["False"]})),className:"px-4 py-2 rounded-lg border ".concat(re.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"),children:"False"})]})]}),"short_answer"===re.question_type&&(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),(0,c.jsx)("textarea",{value:re.correct_answers.join("\n"),onChange:e=>ae(t=>(0,o.A)((0,o.A)({},t),{},{correct_answers:e.target.value.split("\n").filter(e=>e.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),(0,c.jsx)("textarea",{value:re.explanation,onChange:e=>ae(t=>(0,o.A)((0,o.A)({},t),{},{explanation:e.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),(0,c.jsxs)("select",{value:re.difficulty_level,onChange:e=>ae(t=>(0,o.A)((0,o.A)({},t),{},{difficulty_level:parseInt(e.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[(0,c.jsx)("option",{value:1,children:"1 - Very Easy"}),(0,c.jsx)("option",{value:2,children:"2 - Easy"}),(0,c.jsx)("option",{value:3,children:"3 - Medium"}),(0,c.jsx)("option",{value:4,children:"4 - Hard"}),(0,c.jsx)("option",{value:5,children:"5 - Very Hard"})]})]}),(0,c.jsxs)("div",{className:"flex space-x-3",children:[(0,c.jsx)(i.$,{onClick:async()=>{if(J)if(re.question_text.trim())if(0!==re.correct_answers.length){if("multiple_choice"===re.question_type||"select_all"===re.question_type){if(re.options.filter(e=>e.trim().length>0).length<2)return void await f({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"})}try{const e=await fetch("/api/quiz-questions/".concat(J.id),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))},body:JSON.stringify({question_text:re.question_text.trim(),question_type:re.question_type,options:"multiple_choice"===re.question_type||"select_all"===re.question_type?re.options.filter(e=>e.trim().length>0):null,correct_answers:re.correct_answers,explanation:re.explanation.trim()||null,difficulty_level:re.difficulty_level})});if(!e.ok)throw new Error("Failed to update question");const t=await e.json();d(t.data),Y(null),await f({title:"Success",message:"Question updated successfully!",variant:"success"})}catch(e){await f({title:"Error",message:e.message||"Failed to update question",variant:"error"})}}else await f({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});else await f({title:"Validation Error",message:"Question text is required.",variant:"error"})},variant:"primary",children:"Save Changes"}),(0,c.jsx)(i.$,{onClick:()=>{Y(null),ae({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3})},variant:"secondary",children:"Cancel"})]})]})]}),k&&(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[(0,c.jsx)("h4",{className:"text-md font-medium text-white mb-4",children:"AI Question Generation"}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),(0,c.jsx)(g,{selectedDocuments:A,onSelectionChange:E,documentPageRanges:q,onPageRangeChange:(e,t)=>{I(r=>(0,o.A)((0,o.A)({},r),{},{[e]:t}))},maxSelection:5})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,c.jsx)(l,{label:"Number of Questions",value:D,onChange:T,min:1,max:100,placeholder:"Enter number (1-100)",disabled:Q}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),(0,c.jsxs)("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[oe()," credits"]})]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Types"}),(0,c.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:[(0,c.jsx)(_.S,{label:"Multiple Choice",checked:R.includes("multiple_choice"),onChange:()=>ce("multiple_choice"),size:"sm"}),(0,c.jsx)(_.S,{label:"Select All",checked:R.includes("select_all"),onChange:()=>ce("select_all"),size:"sm"}),(0,c.jsx)(_.S,{label:"True False",checked:R.includes("true_false"),onChange:()=>ce("true_false"),size:"sm"}),(0,c.jsx)(_.S,{label:"Short Answer",checked:R.includes("short_answer"),onChange:()=>ce("short_answer"),size:"sm"})]})]}),(0,c.jsx)(p.B,{value:M,onChange:G}),(0,c.jsx)(b,{value:z,onChange:L}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),(0,c.jsx)("textarea",{value:F,onChange:e=>P(e.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for question generation..."})]}),(0,c.jsx)(i.$,{onClick:async()=>{if(0===A.length)return void await f({title:"No Documents Selected",message:"Please select at least one document to generate questions from.",variant:"warning"});if(0===R.length)return void await f({title:"No Question Types Selected",message:"Please select at least one question type to generate.",variant:"warning"});const e=oe();if(j&&j.credits_remaining<e)return void await f({title:"Insufficient Credits",message:"You need ".concat(e," credits to generate ").concat(D," questions, but you only have ").concat(j.credits_remaining," credits remaining."),variant:"warning"});if(await w({title:"Generate Questions",message:"Generate ".concat(D," questions for ").concat(e," credits?"),confirmText:"Generate",cancelText:"Cancel"})){U(!0);try{const e=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))},body:JSON.stringify({studySetId:t,documentIds:A,documentPageRanges:q,count:D,customPrompt:F.trim()||void 0,difficultyLevel:M,contentLength:z,questionTypes:R,existingContent:s.map(e=>e.question_text)})});if(!e.ok)throw new Error("Failed to generate questions");const n=await e.json();x(n.data.questions),j&&v.A.getState().updateUser({credits_remaining:n.data.creditsRemaining});try{const{useStudyStore:e}=await Promise.resolve().then(r.bind(r,9855)),a=e.getState();await a.refreshStudySetContent(t)}catch(a){console.warn("Failed to refresh study set cache:",a)}await f({title:"Success",message:"Generated ".concat(n.data.questions.length," questions successfully!"),variant:"success"}),E([]),P(""),C(!1)}catch(n){await f({title:"Error",message:n.message||"Failed to generate questions",variant:"error"})}finally{U(!1)}}},disabled:0===A.length||0===R.length||Q,className:"w-full",variant:"primary",children:Q?"Generating...":"Generate ".concat(D," Questions")})]})]}),(0,c.jsx)(u,{isGenerating:Q,stage:Q?"Generating quiz questions with AI...":void 0,estimatedTime:Math.ceil(D/8)}),(0,c.jsx)(N,{selectedCount:K.length,totalCount:s.length,onDeleteSelected:async()=>{if(0===K.length)return;await w({title:"Delete Questions",message:"Are you sure you want to delete ".concat(K.length," question$").concat(1!==K.length?"s":"","?"),variant:"danger",confirmText:"Delete",cancelText:"Cancel"})&&(te(!0),await(async()=>{try{await Promise.all(K.map(e=>fetch("/api/quiz-questions/".concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}}))),K.forEach(e=>h(e)),ie()}catch(e){console.error("Bulk deletion failed",e),await f({title:"Error",message:"Failed to delete selected questions.",variant:"error"})}finally{te(!1)}})())},onClearSelection:ie,isLoading:ee,className:"mb-4",itemType:"question"}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)(_.S,{checked:W,onChange:e=>(e=>{Z(e),X(e?s.map(e=>e.id):[])})(e)}),(0,c.jsxs)("h4",{className:"text-md font-medium text-white",children:["Current Questions (",s.length,")"]})]}),s.length>0&&(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsx)("p",{className:"text-sm text-gray-400",children:"\ud83d\udca1 Click on any question to reveal/hide its answer"}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsxs)("button",{onClick:()=>{const e=new Set(s.map(e=>e.id));ne(e)},className:"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm",children:[(0,c.jsx)(S.zeF,{className:"w-4 h-4"}),(0,c.jsx)("span",{children:"Show All"})]}),(0,c.jsxs)("button",{onClick:()=>{ne(new Set)},className:"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm",children:[(0,c.jsx)(S.QLA,{className:"w-4 h-4"}),(0,c.jsx)("span",{children:"Hide All"})]})]})]})]}),0===s.length?(0,c.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No questions yet. Add some manually or generate them with AI."}):(0,c.jsx)("div",{className:"space-y-2",children:s.map(e=>(0,c.jsx)("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:(0,c.jsxs)("div",{className:"flex items-start justify-between",children:[(0,c.jsx)(_.S,{checked:K.includes(e.id),onChange:t=>((e,t)=>{t?X(t=>[...t,e]):(X(t=>t.filter(t=>t!==e)),Z(!1))})(e.id,t),className:"mr-3 mt-1"}),(0,c.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,c.jsxs)("div",{className:"cursor-pointer group",onClick:()=>{return t=e.id,void ne(e=>{const r=new Set(e);return r.has(t)?r.delete(t):r.add(t),r});var t},title:se.has(e.id)?"Click to hide answer":"Click to reveal answer",children:[(0,c.jsxs)("div",{className:"mb-2",children:[(0,c.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Question"}),(0,c.jsx)("p",{className:"text-white font-medium group-hover:text-primary-300 transition-colors",children:e.question_text})]}),(0,c.jsxs)("div",{className:"mb-2",children:[(0,c.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Type"}),(0,c.jsx)("p",{className:"text-gray-300 capitalize group-hover:text-gray-200 transition-colors",children:e.question_type.replace("_"," ")})]}),se.has(e.id)?(0,c.jsxs)(c.Fragment,{children:["true_false"===e.question_type&&(0,c.jsxs)("div",{className:"mb-2",children:[(0,c.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Correct Answer"}),(0,c.jsx)("p",{className:"text-green-400 text-sm font-medium",children:e.correct_answers[0]})]}),e.options&&(0,c.jsxs)("div",{className:"mb-2",children:[(0,c.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Options"}),(0,c.jsx)("ul",{className:"text-gray-300 text-sm group-hover:text-gray-200 transition-colors",children:e.options.map((t,r)=>(0,c.jsxs)("li",{className:"".concat(e.correct_answers.includes(t)?"text-green-400 font-medium":""),children:[r+1,". ",t]},"".concat(e.id,"-option-").concat(r)))})]}),e.explanation&&(0,c.jsxs)("div",{className:"mb-2",children:[(0,c.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Explanation"}),(0,c.jsx)("p",{className:"text-gray-300 text-sm group-hover:text-gray-200 transition-colors",children:e.explanation})]})]}):(0,c.jsx)("div",{className:"mb-2",children:(0,c.jsx)("p",{className:"text-gray-500 italic text-sm group-hover:text-primary-400 transition-colors",children:"Click to reveal answer details..."})})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[e.is_ai_generated&&(0,c.jsx)("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),e.difficulty_level&&(0,c.jsxs)("span",{children:["Difficulty:"," ","string"===typeof e.difficulty_level?(0,y.eX)(e.difficulty_level):(0,y.eX)((0,y.KF)(e.difficulty_level))]}),(0,c.jsxs)("span",{children:["Attempted: ",e.times_attempted||0," times"]}),(0,c.jsxs)("span",{children:["Correct: ",e.times_correct||0," times"]})]})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,c.jsx)("button",{onClick:()=>(e=>{Y(e),ae({question_text:e.question_text,question_type:e.question_type,options:e.options||["","","",""],correct_answers:e.correct_answers,explanation:e.explanation||"",difficulty_level:"string"===typeof e.difficulty_level?(0,y.sQ)(e.difficulty_level):e.difficulty_level||3})})(e),className:"text-gray-400 hover:text-primary-400 p-1",title:"Edit question",children:"\u270f\ufe0f"}),(0,c.jsx)("button",{onClick:()=>(async e=>{if(await w({title:"Delete Question",message:"Are you sure you want to delete this question?\n\n".concat(e.question_text.substring(0,100)).concat(e.question_text.length>100?"...":""),variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch("/api/quiz-questions/".concat(e.id),{method:"DELETE",headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}})).ok)throw new Error("Failed to delete question");h(e.id),await f({title:"Success",message:"Question deleted successfully!",variant:"success"})}catch(t){await f({title:"Error",message:t.message||"Failed to delete question",variant:"error"})}})(e),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete question",children:"\ud83d\uddd1\ufe0f"})]})]})},e.id))})]})]})},A=e=>{let{enabled:t,onChange:r,disabled:a=!1,className:s="",label:n="Shuffle Cards",description:i="Randomize the order of flashcards during study sessions"}=e;const o=()=>{a||r(!t)};return(0,c.jsxs)("div",{className:"flex items-center justify-between ".concat(s),children:[(0,c.jsx)("div",{className:"flex-1",children:(0,c.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,c.jsx)("span",{className:"text-sm font-medium text-white",children:n}),i&&(0,c.jsx)("span",{className:"text-xs text-gray-400",children:i})]})}),(0,c.jsx)("button",{type:"button",role:"switch","aria-checked":t,"aria-label":"".concat(t?"Disable":"Enable"," ").concat(n.toLowerCase()),onClick:o,onKeyDown:e=>{" "!==e.key&&"Enter"!==e.key||(e.preventDefault(),o())},disabled:a,className:"\n          relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out\n          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800\n          ".concat(a?"opacity-50 cursor-not-allowed bg-gray-600":t?"bg-primary-500 hover:bg-primary-600":"bg-gray-600 hover:bg-gray-500","\n        "),children:(0,c.jsx)("span",{className:"\n            inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out\n            ".concat(t?"translate-x-6":"translate-x-1","\n          ")})})]})},E=()=>{const{id:e}=(0,s.g)(),t=(0,s.Zp)(),{studySetContent:r,isLoading:o,error:l,fetchStudySetContent:d}=(0,n.useStudyStore)(),{alert:u,confirm:h,prompt:x}=(0,m.s)(),{settings:g,updateSettings:p}=(0,j.Q)(),[y,f]=(0,a.useState)(null),[b,v]=(0,a.useState)("study"),[w,N]=(0,a.useState)("flashcards"),[S,_]=(0,a.useState)([]),[E,q]=(0,a.useState)([]),[I,D]=(0,a.useState)("");(0,a.useEffect)(()=>{e&&d(e).catch(console.error)},[e,d]),(0,a.useEffect)(()=>{null!==r&&void 0!==r&&r.studySet&&(D(r.studySet.name),_(r.flashcards||[]),q(r.questions||[]))},[r]);if(o)return(0,c.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,c.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,c.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),(0,c.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading study set..."})]})});if(l||null===r||void 0===r||!r.studySet)return(0,c.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,c.jsxs)("div",{className:"text-center py-12",children:[(0,c.jsx)("div",{className:"text-red-400 mb-4",children:l||"Study set not found"}),(0,c.jsx)(i.$,{onClick:()=>t("/dashboard"),variant:"secondary",children:"Back to Study Sets"})]})});const{studySet:T}=r,F=S&&S.length>0,P=E&&E.length>0;return(0,c.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,c.jsxs)("div",{className:"mb-8",children:[(0,c.jsx)("button",{onClick:()=>t("/dashboard"),className:"text-gray-400 hover:text-white mb-4 flex items-center",children:"\u2190 Back to Study Sets"}),(0,c.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,c.jsx)("h1",{className:"text-3xl font-bold text-white",children:I}),(0,c.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,c.jsx)(i.$,{onClick:async()=>{if(!e||null===r||void 0===r||!r.studySet)return;const t=await x({title:"Rename Study Set",message:"Enter a new name for this study set:",defaultValue:r.studySet.name});if(null!==t&&t.trim()!==r.studySet.name)if(t.trim())try{if(!(await fetch("/api/study-sets/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))},body:JSON.stringify({name:t.trim()})})).ok)throw new Error("Failed to rename study set");D(t.trim()),await u({title:"Success",message:"Study set renamed successfully!",variant:"success"}),await d(e)}catch(l){await u({title:"Error",message:l.message||"Failed to rename study set",variant:"error"})}else await u({title:"Invalid Name",message:"Study set name cannot be empty.",variant:"error"})},variant:"secondary",size:"sm",children:"\u270f\ufe0f Rename"}),(0,c.jsx)(i.$,{onClick:async()=>{if(!e||null===r||void 0===r||!r.studySet)return;if(await h({title:"Delete Study Set",message:'Are you sure you want to delete "'.concat(r.studySet.name,'"?\n\nThis action cannot be undone and will delete all flashcards and quiz questions in this set.'),variant:"danger",confirmText:"Delete Study Set",cancelText:"Cancel"}))try{if(!(await fetch("/api/study-sets/".concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}})).ok)throw new Error("Failed to delete study set");await u({title:"Success",message:"Study set deleted successfully!",variant:"success"}),t("/dashboard")}catch(l){await u({title:"Error",message:l.message||"Failed to delete study set",variant:"error"})}},variant:"danger",size:"sm",children:"\ud83d\uddd1\ufe0f Delete"})]})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[(0,c.jsx)("span",{className:"capitalize",children:T.type}),T.is_ai_generated&&(0,c.jsx)("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),(0,c.jsxs)("span",{children:["Created ",new Date(T.created_at).toLocaleDateString()]})]})]}),(0,c.jsx)("div",{className:"mb-6",children:(0,c.jsx)("div",{className:"border-b border-gray-600",children:(0,c.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,c.jsx)("button",{onClick:()=>v("study"),className:"\n                py-2 px-1 border-b-2 font-medium text-sm transition-colors\n                ".concat("study"===b?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300","\n              "),children:"\ud83d\udcda Study Mode"}),(0,c.jsx)("button",{onClick:()=>v("manage"),className:"\n                py-2 px-1 border-b-2 font-medium text-sm transition-colors\n                ".concat("manage"===b?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300","\n              "),children:"\u2699\ufe0f Manage Content"})]})})}),"study"===b&&(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[(0,c.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"Choose Study Mode"}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[F&&(0,c.jsx)("div",{className:"\n                    p-4 rounded-lg border-2 cursor-pointer transition-all\n                    ".concat("flashcards"===y?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500","\n                  "),onClick:()=>f("flashcards"),children:(0,c.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,c.jsx)("div",{className:"text-2xl",children:"\ud83c\udccf"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"font-medium text-white",children:"Flashcard Review"}),(0,c.jsxs)("p",{className:"text-sm text-gray-400",children:[null===S||void 0===S?void 0:S.length," flashcards \u2022 Interactive review"]})]})]})}),P&&(0,c.jsx)("div",{className:"\n                    p-4 rounded-lg border-2 cursor-pointer transition-all\n                    ".concat("quiz"===y?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500","\n                  "),onClick:()=>f("quiz"),children:(0,c.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,c.jsx)("div",{className:"text-2xl",children:"\ud83d\udcdd"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"font-medium text-white",children:"Quiz Practice"}),(0,c.jsxs)("p",{className:"text-sm text-gray-400",children:[null===E||void 0===E?void 0:E.length," questions \u2022 Test your knowledge"]})]})]})})]}),y&&g&&(0,c.jsx)("div",{className:"mb-6 p-4 bg-background-tertiary rounded-lg border border-gray-600",children:(0,c.jsx)(A,{enabled:g.shuffle_flashcards,onChange:async e=>{try{await p({shuffle_flashcards:e})}catch(l){console.error("Failed to update shuffle setting:",l)}},label:"Shuffle Cards",description:"Randomize the order of flashcards during study sessions"})}),(0,c.jsx)(i.$,{onClick:async()=>{if(e&&y)try{const r=(null===g||void 0===g?void 0:g.shuffle_flashcards)||!1;await n.useStudyStore.getState().startStudySession(e,y,r),t("/study/".concat(e,"/").concat(y))}catch(l){await u({title:"Error",message:l.message||"Failed to start study session",variant:"error"})}},disabled:!y,className:"w-full",size:"lg",children:y?"Start ".concat("flashcards"===y?"Flashcard Review":"Quiz Practice"):"Select a study mode"})]})}),"manage"===b&&e&&(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[(0,c.jsxs)("div",{className:"flex space-x-1 mb-6",children:[(0,c.jsxs)("button",{onClick:()=>N("flashcards"),className:"\n                py-2 px-4 rounded-lg font-medium text-sm transition-colors\n                ".concat("flashcards"===w?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300","\n              "),children:["\ud83d\udcda Flashcards (",S.length,")"]}),(0,c.jsxs)("button",{onClick:()=>N("quiz"),className:"\n                py-2 px-4 rounded-lg font-medium text-sm transition-colors\n                ".concat("quiz"===w?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300","\n              "),children:["\u2753 Quiz Questions (",E.length,")"]})]}),"flashcards"===w&&(0,c.jsx)(k,{studySetId:e,flashcards:S,onFlashcardAdded:e=>{_(t=>[...t,e])},onFlashcardUpdated:e=>{_(t=>t.map(t=>t.id===e.id?e:t))},onFlashcardDeleted:e=>{_(t=>t.filter(t=>t.id!==e))},onFlashcardsGenerated:e=>{_(t=>[...t,...e])}}),"quiz"===w&&(0,c.jsx)(C,{studySetId:e,questions:E,onQuestionAdded:e=>{q(t=>[...t,e])},onQuestionUpdated:e=>{q(t=>t.map(t=>t.id===e.id?e:t))},onQuestionDeleted:e=>{q(t=>t.filter(t=>t.id!==e))},onQuestionsGenerated:e=>{q(t=>[...t,...e])}})]}),(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6",children:[(0,c.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Study Set Details"}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Content"}),(0,c.jsxs)("div",{className:"space-y-1 text-sm text-gray-400",children:[F&&(0,c.jsxs)("div",{children:[S.length," flashcards"]}),P&&(0,c.jsxs)("div",{children:[null===E||void 0===E?void 0:E.length," quiz questions"]}),!F&&!P&&(0,c.jsx)("div",{className:"text-gray-500",children:"No content yet"})]})]}),T.source_documents&&T.source_documents.length>0&&(0,c.jsxs)("div",{children:[(0,c.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Source Documents"}),(0,c.jsx)("div",{className:"space-y-1 text-sm text-gray-400",children:T.source_documents.map((e,t)=>(0,c.jsx)("div",{children:e.filename},t))})]}),T.custom_prompt&&(0,c.jsxs)("div",{className:"md:col-span-2",children:[(0,c.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),(0,c.jsx)("p",{className:"text-sm text-gray-400",children:T.custom_prompt})]})]})]})]})}},9391:(e,t,r)=>{r.d(t,{Cr:()=>a,KF:()=>o,eX:()=>n,qt:()=>s,sQ:()=>i});let a=function(e){return e.EASY="easy",e.MEDIUM="medium",e.HARD="hard",e.COLLEGE="college",e.GRADUATE="graduate",e.PHD="phd",e}({}),s=function(e){return e.SHORT="short",e.MEDIUM="medium",e.LONG="long",e}({});const n=e=>({[a.EASY]:"Easy",[a.MEDIUM]:"Medium",[a.HARD]:"Hard",[a.COLLEGE]:"College",[a.GRADUATE]:"Graduate",[a.PHD]:"PhD"}[e]),i=e=>({[a.EASY]:1,[a.MEDIUM]:3,[a.HARD]:4,[a.COLLEGE]:5,[a.GRADUATE]:6,[a.PHD]:7}[e]),o=e=>{switch(e){case 1:case 2:return a.EASY;case 3:default:return a.MEDIUM;case 4:return a.HARD;case 5:return a.COLLEGE;case 6:return a.GRADUATE;case 7:return a.PHD}}},9855:(e,t,r)=>{r.d(t,{useStudyStore:()=>i});var a=r(8957),s=r(5914);const n=e=>{const t=[...e];for(let r=t.length-1;r>0;r--){const e=Math.floor(Math.random()*(r+1));[t[r],t[e]]=[t[e],t[r]]}return t},i=(0,s.vt)((e,t)=>({currentSession:null,studySetContent:null,studySets:[],sessions:[],isLoading:!1,error:null,actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1,fetchStudySetContent:async function(r){var a;let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{studySetContent:n}=t();if(s||!n||(null===(a=n.studySet)||void 0===a?void 0:a.id)!==r){e({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),a=await fetch("/api/study-sets/".concat(r,"/content"),{headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){const e=await a.json();throw new Error(e.error||"Failed to fetch study set content")}const s=await a.json();if(!s.success)throw new Error(s.error);e({studySetContent:{studySet:s.data.studySet,flashcards:s.data.flashcards||[],questions:s.data.questions||[]},isLoading:!1})}catch(i){throw e({error:i.message||"Failed to fetch study set content",isLoading:!1}),i}}},startStudySession:async function(r,s){var i,o,c;let l=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const{studySetContent:d,fetchStudySetContent:u}=t();d&&(null===(i=d.studySet)||void 0===i?void 0:i.id)===r||await u(r);const m=t().studySetContent;if(!m)throw new Error("Failed to load study set content");const h="flashcards"===s?(null===(o=m.flashcards)||void 0===o?void 0:o.length)||0:(null===(c=m.questions)||void 0===c?void 0:c.length)||0;if(0===h)throw new Error("No study materials found in this set");let x;if(l)if(x=Array.from({length:h},(e,t)=>t),"flashcards"===s&&m.flashcards){const t=n(m.flashcards);e(e=>({studySetContent:(0,a.A)((0,a.A)({},e.studySetContent),{},{flashcards:t})}))}else if("quiz"===s&&m.questions){const t=n(m.questions);e(e=>({studySetContent:(0,a.A)((0,a.A)({},e.studySetContent),{},{questions:t})}))}e({currentSession:{studySetId:r,type:s,startTime:new Date,currentIndex:0,totalItems:h,reviewedItems:[],flaggedItems:[],correctAnswers:"quiz"===s?0:void 0,timeSpent:0,isShuffled:l,originalOrder:x}})},endStudySession:()=>{e({currentSession:null})},nextItem:()=>{const{currentSession:r,addToHistory:s}=t();if(!r)return;const n=r.currentIndex===r.totalItems-1?0:r.currentIndex+1;s({type:"NEXT_ITEM",payload:{fromIndex:r.currentIndex,toIndex:n},previousState:{currentIndex:r.currentIndex},timestamp:Date.now()}),e({currentSession:(0,a.A)((0,a.A)({},r),{},{currentIndex:n})})},previousItem:()=>{const{currentSession:r,addToHistory:s}=t();if(!r)return;const n=0===r.currentIndex?r.totalItems-1:r.currentIndex-1;s({type:"PREVIOUS_ITEM",payload:{fromIndex:r.currentIndex,toIndex:n},previousState:{currentIndex:r.currentIndex},timestamp:Date.now()}),e({currentSession:(0,a.A)((0,a.A)({},r),{},{currentIndex:n})})},goToItem:r=>{const{currentSession:s}=t();if(!s)return;const n=Math.max(0,Math.min(r,s.totalItems-1));e({currentSession:(0,a.A)((0,a.A)({},s),{},{currentIndex:n})})},toggleFlag:r=>{const{currentSession:s,addToHistory:n}=t();if(!s)return;const i=s.flaggedItems.includes(r),o=i?s.flaggedItems.filter(e=>e!==r):[...s.flaggedItems,r];n({type:"TOGGLE_FLAG",payload:{itemId:r,wasFlagged:i},previousState:{flaggedItems:s.flaggedItems},timestamp:Date.now()}),e({currentSession:(0,a.A)((0,a.A)({},s),{},{flaggedItems:o})})},markReviewed:r=>{const{currentSession:s}=t();s&&(s.reviewedItems.includes(s.currentIndex)||e({currentSession:(0,a.A)((0,a.A)({},s),{},{reviewedItems:[...s.reviewedItems,s.currentIndex]})}))},submitQuizAnswer:(r,s,n)=>{const{currentSession:i,markReviewed:o}=t();i&&"quiz"===i.type&&(o(r),n&&e({currentSession:(0,a.A)((0,a.A)({},i),{},{correctAnswers:(i.correctAnswers||0)+1})}))},updateTimeSpent:r=>{const{currentSession:s}=t();s&&e({currentSession:(0,a.A)((0,a.A)({},s),{},{timeSpent:s.timeSpent+r})})},addToHistory:r=>{const{actionHistory:a,currentActionIndex:s}=t(),n=a.slice(0,s+1);n.push(r);const i=n.slice(-50);e({actionHistory:i,currentActionIndex:i.length-1,canUndo:i.length>0,canRedo:!1})},undo:()=>{const{actionHistory:r,currentActionIndex:s,currentSession:n}=t();if(s<0||!n)return;const i=r[s];e({currentSession:(0,a.A)((0,a.A)({},n),i.previousState),currentActionIndex:s-1,canUndo:s>0,canRedo:!0})},redo:()=>{const{actionHistory:r,currentActionIndex:a,currentSession:s}=t();if(a>=r.length-1||!s)return;const n=a+1,i=r[n];switch(i.type){case"NEXT_ITEM":t().nextItem();break;case"PREVIOUS_ITEM":t().previousItem();break;case"TOGGLE_FLAG":t().toggleFlag(i.payload.itemId);break;case"MARK_REVIEWED":t().markReviewed(i.payload.itemId)}e({currentActionIndex:n,canUndo:!0,canRedo:n<r.length-1})},clearHistory:()=>{e({actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1})},fetchStudySets:async()=>{e({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/study-sets",{headers:{Authorization:"Bearer ".concat(t)}});if(!r.ok){const e=await r.json();throw new Error(e.error||"Failed to fetch study sets")}const a=await r.json();if(!a.success)throw new Error(a.error);e({studySets:a.data,isLoading:!1})}catch(t){throw e({error:t.message||"Failed to fetch study sets",isLoading:!1}),t}},fetchStudySessions:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"30d";e({isLoading:!0,error:null});try{const r=localStorage.getItem("auth_token"),s=await fetch("/api/study-sessions?timeRange=".concat(t),{headers:{Authorization:"Bearer ".concat(r)}});if(!s.ok){const e=await s.json();throw new Error(e.error||"Failed to fetch study sessions")}const n=await s.json();if(!n.success)throw new Error(n.error);{const t=n.data.map(e=>(0,a.A)((0,a.A)({},e),{},{startTime:new Date(e.startTime),endTime:e.endTime?new Date(e.endTime):void 0}));e({sessions:t,isLoading:!1})}}catch(r){throw e({error:r.message||"Failed to fetch study sessions",isLoading:!1}),r}},invalidateStudySetContent:r=>{const{studySetContent:a}=t();var s;r?(null===a||void 0===a||null===(s=a.studySet)||void 0===s?void 0:s.id)===r&&e({studySetContent:null}):e({studySetContent:null})},refreshStudySetContent:async e=>{await t().fetchStudySetContent(e,!0)},invalidateStudySets:()=>{e({studySets:[]})}}))}}]);
//# sourceMappingURL=645.0759e18b.chunk.js.map