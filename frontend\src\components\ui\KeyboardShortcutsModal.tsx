import React, { useEffect, memo } from 'react';
import { Button } from '../common/Button';

interface KeyboardShortcut {
  keys: string[];
  description: string;
  context?: string;
}

interface KeyboardShortcutsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const shortcuts: KeyboardShortcut[] = [
  // Global shortcuts
  { keys: ['?'], description: 'Show keyboard shortcuts', context: 'Global' },
  { keys: ['Escape'], description: 'Close modal or cancel action', context: 'Global' },
  
  // Study shortcuts
  { keys: ['←', '→'], description: 'Navigate between cards/questions', context: 'Study' },
  { keys: ['Space', '↑', '↓'], description: 'Flip flashcard or select answer', context: 'Study' },
  { keys: ['F'], description: 'Flag current item for review', context: 'Study' },
  { keys: ['Enter'], description: 'Submit answer or continue', context: 'Study' },
  { keys: ['Ctrl', 'Z'], description: 'Undo last action', context: 'Study' },
  { keys: ['Ctrl', 'Y'], description: 'Redo last action', context: 'Study' },
  { keys: ['1', '2', '3', '4'], description: 'Select quiz answer option', context: 'Quiz' },
  
  // Navigation shortcuts
  { keys: ['G', 'D'], description: 'Go to Dashboard', context: 'Navigation' },
  { keys: ['G', 'O'], description: 'Go to Documents', context: 'Navigation' },
  { keys: ['G', 'S'], description: 'Go to Study Sets', context: 'Navigation' },
  { keys: ['G', 'A'], description: 'Go to Analytics', context: 'Navigation' },
  
  // Document shortcuts
  { keys: ['Ctrl', 'U'], description: 'Upload new document', context: 'Documents' },
  { keys: ['Ctrl', 'F'], description: 'Search documents', context: 'Documents' },
  { keys: ['Delete'], description: 'Delete selected document', context: 'Documents' },
];

const KeyboardKey: React.FC<{ keys: string[] }> = memo(({ keys }) => (
  <div className="flex items-center space-x-1">
    {keys.map((key, index) => (
      <React.Fragment key={key}>
        {index > 0 && <span className="text-gray-400 text-xs">+</span>}
        <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-200 border border-gray-300 rounded-lg">
          {key}
        </kbd>
      </React.Fragment>
    ))}
  </div>
));

KeyboardKey.displayName = 'KeyboardKey';

export const KeyboardShortcutsModal: React.FC<KeyboardShortcutsModalProps> = memo(({
  isOpen,
  onClose
}) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    const context = shortcut.context || 'General';
    if (!acc[context]) {
      acc[context] = [];
    }
    acc[context].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="shortcuts-title"
    >
      <div 
        className="bg-background-secondary border border-gray-600 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <h2 id="shortcuts-title" className="text-xl font-semibold text-white">
            Keyboard Shortcuts
          </h2>
          <Button
            onClick={onClose}
            variant="secondary"
            size="sm"
            aria-label="Close shortcuts modal"
          >
            ✕
          </Button>
        </div>

        <div className="space-y-6">
          {Object.entries(groupedShortcuts).map(([context, contextShortcuts]) => (
            <div key={context}>
              <h3 className="text-lg font-medium text-gray-300 mb-3 border-b border-gray-600 pb-1">
                {context}
              </h3>
              <div className="space-y-2">
                {contextShortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between py-2">
                    <span className="text-gray-400 flex-1">{shortcut.description}</span>
                    <KeyboardKey keys={shortcut.keys} />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 pt-4 border-t border-gray-600">
          <p className="text-sm text-gray-500 text-center">
            Press <kbd className="px-1 py-0.5 text-xs bg-gray-200 text-gray-800 rounded">?</kbd> anytime to show this help
          </p>
        </div>
      </div>
    </div>
  );
});

KeyboardShortcutsModal.displayName = 'KeyboardShortcutsModal';
