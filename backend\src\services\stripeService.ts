import Stripe from "stripe";
import { supabase } from "./supabaseService";
import { creditService } from "./creditService";
import { BACKEND_PRICING_TIERS } from "../config/constants";
import { SubscriptionTier } from "../../../shared/types";

class StripeService {
  private stripe: Stripe;

  constructor() {
    const secretKey = process.env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error("STRIPE_SECRET_KEY environment variable is required");
    }

    this.stripe = new Stripe(secretKey, {
      apiVersion: "2022-11-15",
    });
  }

  // Create or retrieve Stripe customer
  async createOrGetCustomer(
    userId: string,
    email: string,
    name?: string
  ): Promise<string> {
    try {
      // Check if user already has a Stripe customer ID
      const { data: profile } = await supabase
        .from("users")
        .select("stripe_customer_id")
        .eq("id", userId)
        .single();

      if (profile?.stripe_customer_id) {
        return profile.stripe_customer_id;
      }

      // Create new Stripe customer
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          user_id: userId,
        },
      });

      // Update user profile with Stripe customer ID
      await supabase
        .from("users")
        .update({ stripe_customer_id: customer.id })
        .eq("id", userId);

      return customer.id;
    } catch (error) {
      console.error("Error creating/getting Stripe customer:", error);
      throw new Error("Failed to create or retrieve customer");
    }
  }

  // Create subscription with new pricing tiers
  async createSubscription(customerId: string, priceId: string) {
    try {
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: "default_incomplete",
        payment_settings: { save_default_payment_method: "on_subscription" },
        expand: ["latest_invoice.payment_intent"],
        metadata: {
          created_via: "chewyai_app",
        },
      });

      return subscription;
    } catch (error) {
      console.error("Error creating subscription:", error);
      throw new Error("Failed to create subscription");
    }
  }

  // Get customer subscriptions
  async getCustomerSubscriptions(customerId: string) {
    try {
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        status: "all",
        expand: ["data.default_payment_method"],
      });

      return subscriptions.data;
    } catch (error) {
      console.error("Error getting customer subscriptions:", error);
      throw new Error("Failed to get subscriptions");
    }
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string) {
    try {
      const subscription = await this.stripe.subscriptions.update(
        subscriptionId,
        {
          cancel_at_period_end: true,
        }
      );

      return subscription;
    } catch (error) {
      console.error("Error canceling subscription:", error);
      throw new Error("Failed to cancel subscription");
    }
  }

  // Validate discount code (enhanced for new pricing)
  private validateDiscountCode(code: string): {
    isValid: boolean;
    discountPercent: number;
  } {
    // Hidden discount codes for testing and promotions
    const discountCodes: Record<string, number> = {
      BALLS: 100, // Full discount for testing
      WELCOME25: 25, // 25% off for new users
      STUDENT50: 50, // 50% off for students
      EARLY20: 20, // 20% off early adopter discount
    };

    const upperCode = code.toUpperCase();
    if (upperCode in discountCodes) {
      return { isValid: true, discountPercent: discountCodes[upperCode] };
    }

    return { isValid: false, discountPercent: 0 };
  }

  // Create payment intent for credit purchase
  async createCreditPurchaseIntent(
    customerId: string,
    amount: number,
    credits: number,
    discountCode?: string
  ) {
    try {
      let finalAmount = amount;
      let discountApplied = false;
      let discountPercent = 0;

      // Apply discount code if provided
      if (discountCode) {
        const discount = this.validateDiscountCode(discountCode);
        if (discount.isValid) {
          discountPercent = discount.discountPercent;
          finalAmount = amount * (1 - discountPercent / 100);
          discountApplied = true;
        }
      }

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(finalAmount * 100), // Convert to cents and round
        currency: "usd",
        customer: customerId,
        metadata: {
          type: "credit_purchase",
          credits: credits.toString(),
          original_amount: amount.toString(),
          discount_code: discountCode || "",
          discount_applied: discountApplied.toString(),
          discount_percent: discountPercent.toString(),
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return paymentIntent;
    } catch (error) {
      console.error("Error creating payment intent:", error);
      throw new Error("Failed to create payment intent");
    }
  }

  // Get customer invoices
  async getCustomerInvoices(customerId: string, limit: number = 10) {
    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
        limit,
        expand: ["data.payment_intent"],
      });

      return invoices.data;
    } catch (error) {
      console.error("Error getting customer invoices:", error);
      throw new Error("Failed to get invoices");
    }
  }

  // Get customer payment methods
  async getCustomerPaymentMethods(customerId: string) {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: "card",
      });

      return paymentMethods.data;
    } catch (error) {
      console.error("Error getting payment methods:", error);
      throw new Error("Failed to get payment methods");
    }
  }

  // Handle webhook events with enhanced subscription management
  async handleWebhook(body: string, signature: string) {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      throw new Error("STRIPE_WEBHOOK_SECRET environment variable is required");
    }

    try {
      const event = this.stripe.webhooks.constructEvent(
        body,
        signature,
        webhookSecret
      );

      switch (event.type) {
        case "payment_intent.succeeded":
          await this.handlePaymentSuccess(
            event.data.object as Stripe.PaymentIntent
          );
          break;
        case "invoice.payment_succeeded":
          await this.handleInvoicePaymentSuccess(
            event.data.object as Stripe.Invoice
          );
          break;
        case "customer.subscription.created":
          await this.handleSubscriptionCreated(
            event.data.object as Stripe.Subscription
          );
          break;
        case "customer.subscription.updated":
          await this.handleSubscriptionUpdate(
            event.data.object as Stripe.Subscription
          );
          break;
        case "customer.subscription.deleted":
          await this.handleSubscriptionCancellation(
            event.data.object as Stripe.Subscription
          );
          break;
        case "invoice.payment_failed":
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      console.error("Webhook error:", error);
      throw error;
    }
  }

  // Handle successful payment
  private async handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    if (paymentIntent.metadata.type === "credit_purchase") {
      const credits = parseInt(paymentIntent.metadata.credits);
      const customerId = paymentIntent.customer as string;

      // Get user ID from customer
      const { data: user } = await supabase
        .from("users")
        .select("id")
        .eq("stripe_customer_id", customerId)
        .single();

      if (user) {
        await creditService.addCredits(
          user.id,
          credits,
          "stripe_purchase",
          paymentIntent.id
        );

        // Log the credit purchase
        await supabase.from("credit_purchases").insert({
          user_id: user.id,
          credits_purchased: credits,
          amount_paid: paymentIntent.amount / 100, // Convert from cents
          stripe_payment_intent_id: paymentIntent.id,
          created_at: new Date().toISOString(),
        });
      }
    }
  }

  // Handle subscription creation
  private async handleSubscriptionCreated(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    const tier = this.getSubscriptionTierFromPrice(
      subscription.items.data[0].price.id
    );

    // Get user ID from customer
    const { data: user } = await supabase
      .from("users")
      .select("id")
      .eq("stripe_customer_id", customerId)
      .single();

    if (user && tier) {
      // Update user subscription
      await supabase
        .from("users")
        .update({
          subscription_tier: tier,
          stripe_subscription_id: subscription.id,
          subscription_expires_at: new Date(
            subscription.current_period_end * 1000
          ).toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      // Add initial subscription credits
      const tierConfig = BACKEND_PRICING_TIERS.find((t) => t.id === tier);
      if (tierConfig && tierConfig.credits > 0) {
        await creditService.resetMonthlyCredits(user.id, tierConfig.credits);
      }
    }
  }

  // Handle successful invoice payment (subscriptions)
  private async handleInvoicePaymentSuccess(invoice: Stripe.Invoice) {
    const customerId = invoice.customer as string;
    const subscriptionId = invoice.subscription as string;

    if (!subscriptionId) return; // Not a subscription invoice

    // Get subscription details
    const subscription = await this.stripe.subscriptions.retrieve(
      subscriptionId
    );
    const tier = this.getSubscriptionTierFromPrice(
      subscription.items.data[0].price.id
    );

    // Get user ID from customer
    const { data: user } = await supabase
      .from("users")
      .select("id")
      .eq("stripe_customer_id", customerId)
      .single();

    if (user && tier) {
      // Reset monthly credits for subscription renewal
      const tierConfig = BACKEND_PRICING_TIERS.find((t) => t.id === tier);
      if (tierConfig) {
        await creditService.resetMonthlyCredits(user.id, tierConfig.credits);
      }

      // Update subscription expiry
      await supabase
        .from("users")
        .update({
          subscription_expires_at: new Date(
            subscription.current_period_end * 1000
          ).toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);
    }
  }

  // Handle subscription updates
  private async handleSubscriptionUpdate(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    const tier = this.getSubscriptionTierFromPrice(
      subscription.items.data[0].price.id
    );

    // Update user subscription status in database
    const updateData: any = {
      stripe_subscription_id: subscription.id,
      subscription_expires_at: new Date(
        subscription.current_period_end * 1000
      ).toISOString(),
      updated_at: new Date().toISOString(),
    };

    if (tier) {
      updateData.subscription_tier = tier;
    }

    await supabase
      .from("users")
      .update(updateData)
      .eq("stripe_customer_id", customerId);
  }

  // Handle subscription cancellation
  private async handleSubscriptionCancellation(
    subscription: Stripe.Subscription
  ) {
    const customerId = subscription.customer as string;

    // Update user to Free tier but keep credits until period end
    await supabase
      .from("users")
      .update({
        subscription_tier: "Free",
        stripe_subscription_id: null,
        // Keep subscription_expires_at to allow access until period end
        updated_at: new Date().toISOString(),
      })
      .eq("stripe_customer_id", customerId);
  }

  // Handle payment failures
  private async handlePaymentFailed(invoice: Stripe.Invoice) {
    const customerId = invoice.customer as string;

    // Get user and send notification (could implement email service here)
    const { data: user } = await supabase
      .from("users")
      .select("id, email")
      .eq("stripe_customer_id", customerId)
      .single();

    if (user) {
      console.log(`Payment failed for user ${user.id} (${user.email})`);
      // TODO: Implement email notification service
    }
  }

  // Get subscription tier from Stripe price ID
  private getSubscriptionTierFromPrice(
    priceId: string
  ): SubscriptionTier | null {
    const tierMapping: Record<string, SubscriptionTier> = {
      [process.env.STRIPE_PRICE_ID_STARTER || "price_starter_placeholder"]:
        "Study Starter",
      [process.env.STRIPE_PRICE_ID_PRO || "price_pro_placeholder"]: "Study Pro",
      [process.env.STRIPE_PRICE_ID_MASTER || "price_master_placeholder"]:
        "Study Master",
      [process.env.STRIPE_PRICE_ID_ELITE || "price_elite_placeholder"]:
        "Study Elite",
    };

    return tierMapping[priceId] || null;
  }

  // Get Stripe prices for products
  async getPrices() {
    try {
      const prices = await this.stripe.prices.list({
        active: true,
        expand: ["data.product"],
      });

      return prices.data;
    } catch (error) {
      console.error("Error getting prices:", error);
      throw new Error("Failed to get prices");
    }
  }

  // Create checkout session for subscription
  async createCheckoutSession(
    customerId: string,
    priceId: string,
    successUrl: string,
    cancelUrl: string
  ) {
    try {
      const session = await this.stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: "subscription",
        success_url: successUrl,
        cancel_url: cancelUrl,
        allow_promotion_codes: true,
        billing_address_collection: "auto",
        metadata: {
          created_via: "chewyai_app",
        },
      });

      return session;
    } catch (error) {
      console.error("Error creating checkout session:", error);
      throw new Error("Failed to create checkout session");
    }
  }

  // Additional Stripe methods for billing management
  async getCustomer(customerId: string) {
    return await this.stripe.customers.retrieve(customerId);
  }

  async getUpcomingInvoice(customerId: string) {
    return await this.stripe.invoices.retrieveUpcoming({
      customer: customerId,
    });
  }

  async getInvoice(invoiceId: string) {
    return await this.stripe.invoices.retrieve(invoiceId);
  }

  async getPaymentMethod(paymentMethodId: string) {
    return await this.stripe.paymentMethods.retrieve(paymentMethodId);
  }

  async setDefaultPaymentMethod(customerId: string, paymentMethodId: string) {
    return await this.stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });
  }

  async attachPaymentMethod(paymentMethodId: string, customerId: string) {
    return await this.stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    });
  }

  async detachPaymentMethod(paymentMethodId: string) {
    return await this.stripe.paymentMethods.detach(paymentMethodId);
  }

  async reactivateSubscription(subscriptionId: string) {
    return await this.stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });
  }

  // Update subscription to new tier
  async updateSubscription(subscriptionId: string, newPriceId: string) {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(
        subscriptionId
      );

      const updatedSubscription = await this.stripe.subscriptions.update(
        subscriptionId,
        {
          items: [
            {
              id: subscription.items.data[0].id,
              price: newPriceId,
            },
          ],
          proration_behavior: "create_prorations",
        }
      );

      return updatedSubscription;
    } catch (error) {
      console.error("Error updating subscription:", error);
      throw new Error("Failed to update subscription");
    }
  }
}

export const stripeService = new StripeService();
