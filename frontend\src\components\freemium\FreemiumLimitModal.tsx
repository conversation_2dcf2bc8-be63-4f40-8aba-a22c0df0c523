import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { HiX, Hi<PERSON><PERSON><PERSON>, HiLockClosed, HiTrendingUp } from "react-icons/hi";
import { Button } from "../common/Button";
import { PRICING_TIERS } from "../../../../shared/constants";
import type { PricingTier } from "../../../../shared/types";

interface FreemiumLimitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  limitType: "documents" | "studySets" | "credits" | "fileSize";
}

export const FreemiumLimitModal: React.FC<FreemiumLimitModalProps> = ({
  isOpen,
  onClose,
  onUpgrade,
  limitType,
}) => {
  if (!isOpen) return null;

  const paidTiers = PRICING_TIERS.filter(
    (tier: PricingTier) => tier.id !== "Free"
  ).slice(0, 2);

  const getTitle = () => {
    switch (limitType) {
      case "documents":
        return "Document Upload Limit Reached";
      case "studySets":
        return "Study Set Limit Reached";
      case "credits":
        return "Monthly Credits Exhausted";
      case "fileSize":
        return "File Size Limit Exceeded";
      default:
        return "Upgrade Required";
    }
  };

  const getDescription = () => {
    switch (limitType) {
      case "documents":
        return "You've reached the maximum number of documents allowed on the Free plan. Upgrade to upload more documents and unlock advanced features.";
      case "studySets":
        return "You've reached the maximum number of study sets allowed on the Free plan. Upgrade to create unlimited study sets.";
      case "credits":
        return "You've used all your monthly credits for the Free plan. Upgrade to get more credits and unlimited access.";
      case "fileSize":
        return "The file you're trying to upload exceeds the 5MB limit for Free users. Upgrade to upload larger files.";
      default:
        return "Upgrade your plan to unlock this feature and many more advanced capabilities.";
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 transition-opacity bg-black bg-opacity-75"
              onClick={onClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-background-secondary border border-border-primary rounded-lg shadow-xl"
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <HiLockClosed className="w-5 h-5 text-purple-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-white">
                    {getTitle()}
                  </h3>
                </div>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <HiX className="w-5 h-5" />
                </button>
              </div>

              {/* Content */}
              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <HiSparkles className="w-8 h-8 text-purple-400" />
                  </div>
                  <p className="text-gray-300 mb-2">{getDescription()}</p>
                </div>

                {/* Upgrade Options */}
                <div className="space-y-3">
                  {paidTiers.map((tier: PricingTier) => (
                    <div
                      key={tier.id}
                      className={`bg-background-tertiary rounded-lg p-4 border border-purple-500/30`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-white">
                          {tier.name}
                        </h4>
                        <div className="text-right">
                          <span className="text-lg font-bold text-white">
                            ${tier.price}
                          </span>
                          <span className="text-gray-400 text-sm">/month</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-300">
                        <HiTrendingUp className="w-4 h-4 text-green-400" />
                        <span>{tier.credits} credits/month</span>
                        <span className="text-gray-500">•</span>
                        <span className="text-green-400">
                          {tier &&
                            `${((tier.credits / 25) * 100).toFixed(
                              0
                            )}x more credits!`}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Benefits */}
                <div className="bg-background-tertiary rounded-lg p-4">
                  <h5 className="font-medium text-white mb-2">
                    What you'll get:
                  </h5>
                  <ul className="space-y-1 text-sm text-gray-300">
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full" />
                      <span>
                        Unlimited{" "}
                        {limitType === "credits" ? "credits" : "access"}
                      </span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full" />
                      <span>All AI study tools included</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full" />
                      <span>Priority support</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full" />
                      <span>Cancel anytime</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-3 mt-6">
                <Button
                  onClick={onClose}
                  variant="secondary"
                  className="flex-1"
                >
                  Continue with Free
                </Button>
                <Button
                  onClick={onUpgrade}
                  variant="primary"
                  className="flex-1 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                >
                  <HiSparkles className="w-4 h-4 mr-2" />
                  Upgrade Now
                </Button>
              </div>

              {/* Footer */}
              <div className="mt-4 text-center">
                <p className="text-xs text-gray-500">
                  Your free credits will reset next month. Upgrade for immediate
                  access.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};
