"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[316],{1316:(e,t,s)=>{s.r(t),s.d(t,{AnalyticsPage:()=>A});var a=s(9643),r=s(8002),n=s(344),i=s(6507);const o=e=>{const t=Math.floor(e/3600),s=Math.floor(e%3600/60),a=e%60;return t>0?"".concat(t,"h ").concat(s,"m"):s>0?"".concat(s,"m ").concat(a,"s"):"".concat(a,"s")},c=(0,a.memo)(e=>{let{title:t,value:s,subtitle:a,icon:r}=e;return(0,i.jsx)("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-400",children:t}),(0,i.jsx)("p",{className:"text-2xl font-semibold text-white mt-1",children:s}),a&&(0,i.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a})]}),r&&(0,i.jsx)("div",{className:"text-2xl",children:r})]})})});c.displayName="StatCard";const d=(0,a.memo)(e=>{let{sessions:t}=e;const s=(0,a.useMemo)(()=>{const e=Array.from({length:7},(e,t)=>{const s=new Date;return s.setDate(s.getDate()-(6-t)),{date:s.toLocaleDateString("en-US",{weekday:"short"}),sessions:0,timeSpent:0}});return t.forEach(t=>{const s=new Date(t.startTime),a=Math.floor((Date.now()-s.getTime())/864e5);if(a>=0&&a<7){const s=6-a;e[s].sessions+=1,e[s].timeSpent+=t.timeSpent}}),e},[t]),r=Math.max(...s.map(e=>e.timeSpent));return(0,i.jsxs)("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Study Activity (Last 7 Days)"}),(0,i.jsx)("div",{className:"flex items-end justify-between h-32 space-x-2",children:s.map((e,t)=>(0,i.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[(0,i.jsx)("div",{className:"w-full bg-gray-700 rounded-t relative",style:{height:"100px"},children:(0,i.jsx)("div",{className:"bg-primary-500 rounded-t transition-all duration-300",style:{height:r>0?"".concat(e.timeSpent/r*100,"%"):"0%",position:"absolute",bottom:0,left:0,right:0},title:"".concat(e.sessions," sessions, ").concat(o(e.timeSpent))})}),(0,i.jsx)("div",{className:"text-xs text-gray-400 mt-2",children:e.date})]},t))})]})});d.displayName="ProgressChart";const l=(0,a.memo)(e=>{let{studySets:t,sessions:s}=e;const r=(0,a.useMemo)(()=>{var e,a;const r=s.reduce((e,t)=>e+t.timeSpent,0),n=s.length,i=n>0?r/n:0,o=s.reduce((e,t)=>e+t.reviewedItems,0),c=s.filter(e=>"quiz"===e.type&&void 0!==e.correctAnswers),d=c.reduce((e,t)=>e+t.totalItems,0),l=c.reduce((e,t)=>e+(t.correctAnswers||0),0),m=d>0?l/d*100:0,u=new Date;let x=0;for(let t=0;t<365;t++){const e=new Date(u);e.setDate(u.getDate()-t);if(s.some(t=>new Date(t.startTime).toDateString()===e.toDateString()))x++;else if(t>0)break}const h=s.reduce((e,t)=>(e[t.studySetId]=(e[t.studySetId]||0)+1,e),{}),g=null===(e=Object.entries(h).sort((e,t)=>{let[,s]=e,[,a]=t;return a-s})[0])||void 0===e?void 0:e[0];return{totalStudyTime:r,totalSessions:n,averageSessionTime:i,totalItemsReviewed:o,averageAccuracy:m,studyStreak:x,mostStudiedSet:(null===(a=t.find(e=>e.id===g))||void 0===a?void 0:a.name)||"None",recentActivity:s.sort((e,t)=>new Date(t.startTime).getTime()-new Date(e.startTime).getTime()).slice(0,5)}},[s,t]);return(0,i.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Study Analytics"}),(0,i.jsx)("p",{className:"text-gray-400",children:"Track your learning progress and performance"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,i.jsx)(c,{title:"Total Study Time",value:o(r.totalStudyTime),icon:"\u23f1\ufe0f"}),(0,i.jsx)(c,{title:"Study Sessions",value:r.totalSessions,subtitle:"All time",icon:"\ud83d\udcda"}),(0,i.jsx)(c,{title:"Average Session",value:o(Math.round(r.averageSessionTime)),icon:"\u23f0"}),(0,i.jsx)(c,{title:"Study Streak",value:"".concat(r.studyStreak," days"),icon:"\ud83d\udd25"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,i.jsx)(c,{title:"Items Reviewed",value:r.totalItemsReviewed,subtitle:"Flashcards & questions",icon:"\u2705"}),(0,i.jsx)(c,{title:"Quiz Accuracy",value:"".concat(Math.round(r.averageAccuracy),"%"),subtitle:"Average across all quizzes",icon:"\ud83c\udfaf"})]}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)(d,{sessions:s})}),(0,i.jsxs)("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Activity"}),0===r.recentActivity.length?(0,i.jsx)("p",{className:"text-gray-400",children:"No recent study sessions"}):(0,i.jsx)("div",{className:"space-y-3",children:r.recentActivity.map((e,s)=>{const a=t.find(t=>t.id===e.studySetId);return(0,i.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-700 last:border-b-0",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:(null===a||void 0===a?void 0:a.name)||"Unknown Set"}),(0,i.jsxs)("p",{className:"text-sm text-gray-400",children:["quiz"===e.type?"Quiz":"Flashcards"," \u2022 ",e.reviewedItems,"/",e.totalItems," items"]})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("p",{className:"text-sm text-gray-300",children:o(e.timeSpent)}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.startTime).toLocaleDateString()})]})]},s)})})]})]})});l.displayName="StudyAnalytics";const m=e=>{const t=Math.floor(e/3600),s=Math.floor(e%3600/60);return t>0?"".concat(t,"h ").concat(s,"m"):s>0?"".concat(s,"m"):"".concat(e,"s")},u=e=>{let{title:t,value:s,subtitle:a,icon:o,trend:c,isLoading:d}=e;return(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:(0,i.jsx)(o,{className:"w-6 h-6 text-primary-400"})}),c&&(0,i.jsxs)("div",{className:"flex items-center space-x-1 ".concat(c.isPositive?"text-green-400":"text-red-400"),children:[(0,i.jsx)(n.IG3,{className:"w-4 h-4 ".concat(c.isPositive?"":"rotate-180")}),(0,i.jsxs)("span",{className:"text-sm font-medium",children:[Math.abs(c.value),"%"]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-400 mb-1",children:t}),d?(0,i.jsx)("div",{className:"animate-pulse",children:(0,i.jsx)("div",{className:"h-8 w-20 bg-gray-600 rounded"})}):(0,i.jsx)("p",{className:"text-2xl font-bold text-white",children:s}),a&&(0,i.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a})]})]})},x=e=>{let{studySets:t,sessions:s,documents:r,timeRange:o,isLoading:c}=e;const d=(0,a.useMemo)(()=>{const e=new Date,a={"7d":6048e5,"30d":2592e6,"90d":7776e6,all:1/0}[o],n=s.filter(t=>{const s=new Date(t.startTime).getTime();return e.getTime()-s<=a}),i=n.reduce((e,t)=>e+t.timeSpent,0),c=n.length,d=c>0?i/c:0,l=n.reduce((e,t)=>e+t.totalItems,0),m=n.reduce((e,t)=>e+t.reviewedItems,0),u=l>0?m/l*100:0,x=t.filter(e=>e.is_ai_generated).length,h=s.filter(t=>{const s=new Date(t.startTime).getTime();return e.getTime()-s<=864e5}).length;return{totalStudySets:t.length,totalDocuments:r.length,totalSessions:c,totalStudyTime:i,averageSessionTime:d,completionRate:u,aiGeneratedSets:x,recentActivity:h}},[t,s,r,o]),l=(0,a.useMemo)(()=>{const e=new Date,t={"7d":6048e5,"30d":2592e6,"90d":7776e6,all:1/0}[o];if(t===1/0)return{};const a=e.getTime()-t,r=a-t,n=s.filter(e=>new Date(e.startTime).getTime()>=a),i=s.filter(e=>{const t=new Date(e.startTime).getTime();return t>=r&&t<a}),c=n.reduce((e,t)=>e+t.timeSpent,0),d=i.reduce((e,t)=>e+t.timeSpent,0),l=d>0?(c-d)/d*100:0,m=i.length>0?(n.length-i.length)/i.length*100:0;return{studyTime:{value:Math.round(l),isPositive:l>=0},sessions:{value:Math.round(m),isPositive:m>=0}}},[s,o]);return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsx)(u,{title:"Study Sets",value:d.totalStudySets,subtitle:"Total created",icon:n.eVK,isLoading:c}),(0,i.jsx)(u,{title:"Documents",value:d.totalDocuments,subtitle:"Uploaded files",icon:n.gpD,isLoading:c}),(0,i.jsx)(u,{title:"Study Time",value:m(d.totalStudyTime),subtitle:"".concat("all"===o?"All time":"Last ".concat(o.replace("d"," days"))),icon:n.mqD,trend:l.studyTime,isLoading:c}),(0,i.jsx)(u,{title:"Sessions",value:d.totalSessions,subtitle:"".concat("all"===o?"All time":"Last ".concat(o.replace("d"," days"))),icon:n.dHv,trend:l.sessions,isLoading:c})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsx)(u,{title:"Avg Session",value:m(Math.round(d.averageSessionTime)),subtitle:"Time per session",icon:n.mqD,isLoading:c}),(0,i.jsx)(u,{title:"Completion Rate",value:"".concat(Math.round(d.completionRate),"%"),subtitle:"Items reviewed",icon:n.ABC,isLoading:c}),(0,i.jsx)(u,{title:"AI Generated",value:d.aiGeneratedSets,subtitle:"Study sets created by AI",icon:n.pCw,isLoading:c})]}),(0,i.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Quick Stats"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-primary-400",children:d.recentActivity}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Sessions today"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-green-400",children:t.filter(e=>"flashcards"===e.type).length}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Flashcard sets"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:t.filter(e=>"quiz"===e.type).length}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Quiz sets"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-purple-400",children:[Math.round(d.aiGeneratedSets/Math.max(d.totalStudySets,1)*100),"%"]}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"AI generated"})]})]})]})]})},h=e=>e<1?"".concat(Math.round(60*e),"s/item"):"".concat(e.toFixed(1)," items/min"),g=e=>{let{title:t,value:s,subtitle:a,icon:o,trend:c,isLoading:d,color:l="primary"}=e;return(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:"p-3 rounded-lg ".concat({primary:"bg-primary-500/20 text-primary-400",green:"bg-green-500/20 text-green-400",red:"bg-red-500/20 text-red-400",yellow:"bg-yellow-500/20 text-yellow-400"}[l]),children:(0,i.jsx)(o,{className:"w-6 h-6"})}),void 0!==c&&(0,i.jsxs)("div",{className:"flex items-center space-x-1 ".concat(c>=0?"text-green-400":"text-red-400"),children:[c>=0?(0,i.jsx)(n.IG3,{className:"w-4 h-4"}):(0,i.jsx)(n.thT,{className:"w-4 h-4"}),(0,i.jsxs)("span",{className:"text-sm font-medium",children:[Math.abs(c).toFixed(1),"%"]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-400 mb-1",children:t}),d?(0,i.jsx)("div",{className:"animate-pulse",children:(0,i.jsx)("div",{className:"h-8 w-20 bg-gray-600 rounded"})}):(0,i.jsx)("p",{className:"text-2xl font-bold text-white",children:s}),a&&(0,i.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a})]})]})},y=e=>{let{subjects:t,title:s,color:a}=e;return(0,i.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:s}),(0,i.jsx)("div",{className:"space-y-3",children:0===t.length?(0,i.jsx)("p",{className:"text-gray-400 text-center py-4",children:"No data available"}):t.map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-gray-300 truncate flex-1 mr-4",children:e.name}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-20 bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"h-2 rounded-full ".concat(a),style:{width:"".concat(e.accuracy,"%")}})}),(0,i.jsxs)("span",{className:"text-white font-medium w-12 text-right",children:[Math.round(e.accuracy),"%"]})]})]},t))})]})},p=e=>{let{studySets:t,sessions:s,timeRange:o,isLoading:c}=e;const d=(0,a.useMemo)(()=>{const e=new Date,a={"7d":6048e5,"30d":2592e6,"90d":7776e6,all:1/0}[o],r=s.filter(t=>{const s=new Date(t.startTime).getTime();return e.getTime()-s<=a}),n=r.filter(e=>"quiz"===e.type&&void 0!==e.correctAnswers),i=n.reduce((e,t)=>e+t.totalItems,0),c=n.reduce((e,t)=>e+(t.correctAnswers||0),0),d=i>0?c/i*100:0,l=r.reduce((e,t)=>e+t.reviewedItems,0),m=r.reduce((e,t)=>e+t.timeSpent,0)/60,u=m>0?l/m:0,x=new Map;n.forEach(e=>{const s=t.find(t=>t.id===e.studySetId);if(s){const t=x.get(s.name)||{correct:0,total:0};x.set(s.name,{correct:t.correct+(e.correctAnswers||0),total:t.total+e.totalItems})}});const h=Array.from(x.entries()).map(e=>{let[t,s]=e;return{name:t,accuracy:s.total>0?s.correct/s.total*100:0}}).sort((e,t)=>t.accuracy-e.accuracy),g=h.slice(0,5),y=h.slice(-5).reverse(),p=Array.from({length:7},(e,t)=>{const s=new Date;s.setDate(s.getDate()-(6-t));const a=s.toISOString().split("T")[0],n=r.filter(e=>new Date(e.startTime).toISOString().split("T")[0]===a),i=n.filter(e=>"quiz"===e.type&&void 0!==e.correctAnswers),o=i.reduce((e,t)=>e+t.totalItems,0),c=i.reduce((e,t)=>e+(t.correctAnswers||0),0),d=o>0?c/o*100:0,l=n.reduce((e,t)=>e+t.reviewedItems,0),m=n.reduce((e,t)=>e+t.timeSpent,0)/60,u=m>0?l/m:0;return{date:s.toLocaleDateString("en-US",{weekday:"short"}),accuracy:d,speed:u}}),v=p.slice(-3).reduce((e,t)=>e+t.accuracy,0)/3,f=p.slice(0,3).reduce((e,t)=>e+t.accuracy,0)/3,j=f>0?(v-f)/f*100:0,w=p.slice(-3).reduce((e,t)=>e+t.speed,0)/3,S=p.slice(0,3).reduce((e,t)=>e+t.speed,0)/3,b=S>0?(w-S)/S*100:0,N=p.reduce((e,t)=>{const s=t.accuracy-d;return e+s*s},0)/p.length,I=Math.max(0,100-Math.sqrt(N));return{overallAccuracy:d,accuracyTrend:j,averageSpeed:u,speedTrend:b,consistencyScore:I,improvementRate:j,strongestSubjects:g,weakestSubjects:y,dailyPerformance:p}},[s,t,o]);return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsx)(g,{title:"Overall Accuracy",value:"".concat(Math.round(d.overallAccuracy),"%"),subtitle:"Quiz performance",icon:n.OLr,trend:d.accuracyTrend,isLoading:c,color:"green"}),(0,i.jsx)(g,{title:"Average Speed",value:h(d.averageSpeed),subtitle:"Items per minute",icon:n.rqL,trend:d.speedTrend,isLoading:c,color:"yellow"}),(0,i.jsx)(g,{title:"Consistency",value:"".concat(Math.round(d.consistencyScore),"%"),subtitle:"Performance stability",icon:n.Lyu,isLoading:c,color:"primary"}),(0,i.jsx)(g,{title:"Improvement",value:"".concat(d.improvementRate>=0?"+":"").concat(d.improvementRate.toFixed(1),"%"),subtitle:"Recent trend",icon:n.IG3,isLoading:c,color:d.improvementRate>=0?"green":"red"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsx)(y,{subjects:d.strongestSubjects,title:"Strongest Subjects",color:"bg-green-500"}),(0,i.jsx)(y,{subjects:d.weakestSubjects,title:"Areas for Improvement",color:"bg-red-500"})]}),(0,i.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Daily Performance (Last 7 Days)"}),(0,i.jsx)("div",{className:"flex items-end justify-between h-32 space-x-2",children:d.dailyPerformance.map((e,t)=>{const s=Math.max(...d.dailyPerformance.map(e=>e.accuracy),1),a=e.accuracy/s*100;return(0,i.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[(0,i.jsx)("div",{className:"w-full bg-gray-700 rounded-t relative",style:{height:"100px"},children:(0,i.jsx)(r.P.div,{initial:{height:0},animate:{height:"".concat(a,"%")},transition:{duration:.5,delay:.1*t},className:"bg-gradient-to-t from-green-500 to-green-400 rounded-t transition-all duration-300 absolute bottom-0 left-0 right-0",title:"".concat(Math.round(e.accuracy),"% accuracy, ").concat(h(e.speed))})}),(0,i.jsx)("div",{className:"text-xs text-gray-400 mt-2",children:e.date})]},t)})})]})]})},v=e=>{const t=Math.floor(e/3600),s=Math.floor(e%3600/60);return t>0?"".concat(t,"h ").concat(s,"m"):s>0?"".concat(s,"m"):"".concat(e,"s")},f=e=>{let{title:t,value:s,subtitle:a,icon:n,isLoading:o,color:c="text-primary-400"}=e;return(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,i.jsx)("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:(0,i.jsx)(n,{className:"w-6 h-6 ".concat(c)})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-400",children:t}),o?(0,i.jsx)("div",{className:"animate-pulse",children:(0,i.jsx)("div",{className:"h-6 w-16 bg-gray-600 rounded"})}):(0,i.jsx)("p",{className:"text-xl font-bold text-white",children:s})]})]}),a&&(0,i.jsx)("p",{className:"text-xs text-gray-500",children:a})]})},j=e=>{let{title:t,data:s,color:a}=e;const n=Math.max(...s.map(e=>e.value),1);return(0,i.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:t}),(0,i.jsx)("div",{className:"space-y-3",children:s.map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-gray-300 text-sm w-20",children:e.label}),(0,i.jsx)("div",{className:"flex-1 mx-4",children:(0,i.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,i.jsx)(r.P.div,{initial:{width:0},animate:{width:"".concat(e.value/n*100,"%")},transition:{duration:.5,delay:.1*t},className:"h-2 rounded-full ".concat(a)})})}),(0,i.jsxs)("div",{className:"text-right w-20",children:[(0,i.jsx)("span",{className:"text-white font-medium text-sm",children:e.value}),e.time&&(0,i.jsx)("div",{className:"text-xs text-gray-400",children:v(e.time)})]})]},t))})]})},w=e=>{let{sessions:t,timeRange:s,isLoading:o}=e;const c=(0,a.useMemo)(()=>{const e=new Date;let a=0,r=0,n=0;for(let s=0;s<365;s++){const i=new Date(e);i.setDate(e.getDate()-s);if(t.some(e=>new Date(e.startTime).toDateString()===i.toDateString()))0!==s&&a!==s||a++,n++,r=Math.max(r,n);else{if(0===s){const s=new Date(e);s.setDate(e.getDate()-1);t.some(e=>new Date(e.startTime).toDateString()===s.toDateString())||(a=0)}n=0}}const i={"7d":6048e5,"30d":2592e6,"90d":7776e6,all:1/0}[s],o=t.filter(t=>{const s=new Date(t.startTime).getTime();return e.getTime()-s<=i}),c=i===1/0?Math.max(1,Math.ceil((e.getTime()-Math.min(...t.map(e=>new Date(e.startTime).getTime())))/864e5)):Math.ceil(i/864e5),d=o.length/c,l=Array.from({length:24},(e,t)=>{const s=o.filter(e=>new Date(e.startTime).getHours()===t);return{hour:t,sessions:s.length,time:s.reduce((e,t)=>e+t.timeSpent,0)}}),m=l.reduce((e,t)=>t.sessions>e.sessions?t:e).hour,u=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map((e,t)=>{const s=o.filter(e=>new Date(e.startTime).getDay()===t);return{day:e,sessions:s.length,time:s.reduce((e,t)=>e+t.timeSpent,0)}}),x=u.reduce((e,t)=>t.sessions>e.sessions?t:e).day,h=Array.from({length:6},(s,a)=>{const r=new Date(e);r.setMonth(e.getMonth()-(5-a));const n=r.toLocaleDateString("en-US",{month:"short"}),i=t.filter(e=>{const t=new Date(e.startTime);return t.getMonth()===r.getMonth()&&t.getFullYear()===r.getFullYear()});return{month:n,sessions:i.length,time:i.reduce((e,t)=>e+t.timeSpent,0)}});return{studyStreak:a,longestStreak:r,averageSessionsPerDay:d,mostProductiveHour:m,mostProductiveDay:x,weeklyPattern:u,hourlyPattern:l.filter(e=>e.sessions>0),monthlyTrend:h}},[t,s]),d=e=>0===e?"12 AM":e<12?"".concat(e," AM"):12===e?"12 PM":"".concat(e-12," PM");return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsx)(f,{title:"Current Streak",value:"".concat(c.studyStreak," days"),subtitle:"Consecutive study days",icon:n.UA$,isLoading:o,color:"text-orange-400"}),(0,i.jsx)(f,{title:"Longest Streak",value:"".concat(c.longestStreak," days"),subtitle:"Personal best",icon:n.IG3,isLoading:o,color:"text-green-400"}),(0,i.jsx)(f,{title:"Daily Average",value:c.averageSessionsPerDay.toFixed(1),subtitle:"Sessions per day",icon:n.Ylv,isLoading:o,color:"text-blue-400"}),(0,i.jsx)(f,{title:"Peak Hour",value:d(c.mostProductiveHour),subtitle:"Most active time",icon:c.mostProductiveHour<12?n.Q3K:n.Zt5,isLoading:o,color:"text-yellow-400"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsx)(j,{title:"Weekly Study Pattern",data:c.weeklyPattern.map(e=>({label:e.day,value:e.sessions,time:e.time})),type:"bar",color:"bg-primary-500"}),(0,i.jsx)(j,{title:"Active Study Hours",data:c.hourlyPattern.slice(0,8).map(e=>({label:d(e.hour),value:e.sessions,time:e.time})),type:"bar",color:"bg-yellow-500"})]}),(0,i.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"6-Month Study Trend"}),(0,i.jsx)("div",{className:"flex items-end justify-between h-40 space-x-2",children:c.monthlyTrend.map((e,t)=>{const s=Math.max(...c.monthlyTrend.map(e=>e.sessions),1),a=e.sessions/s*100;return(0,i.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[(0,i.jsx)("div",{className:"w-full bg-gray-700 rounded-t relative",style:{height:"120px"},children:(0,i.jsx)(r.P.div,{initial:{height:0},animate:{height:"".concat(a,"%")},transition:{duration:.5,delay:.1*t},className:"bg-gradient-to-t from-purple-500 to-purple-400 rounded-t transition-all duration-300 absolute bottom-0 left-0 right-0",title:"".concat(e.sessions," sessions, ").concat(v(e.time))})}),(0,i.jsx)("div",{className:"text-xs text-gray-400 mt-2",children:e.month}),(0,i.jsx)("div",{className:"text-xs text-white font-medium",children:e.sessions})]},t)})})]}),(0,i.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Study Insights"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-white mb-2",children:"Best Study Day"}),(0,i.jsx)("p",{className:"text-gray-300",children:c.mostProductiveDay}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Most sessions completed on this day of the week"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-white mb-2",children:"Optimal Study Time"}),(0,i.jsx)("p",{className:"text-gray-300",children:d(c.mostProductiveHour)}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Peak productivity hour based on session frequency"})]})]})]})]})};var S=s(4859),b=s(9855),N=s(2171);const I=[{id:"overview",label:"Overview",icon:n.Lyu,description:"General study analytics and progress"},{id:"performance",label:"Performance",icon:n.IG3,description:"Detailed performance metrics and accuracy"},{id:"trends",label:"Trends",icon:n.mqD,description:"Study patterns and time analysis"}],A=()=>{const[e,t]=(0,a.useState)("overview"),[s,o]=(0,a.useState)("30d"),[c,d]=(0,a.useState)(!0),[m,u]=(0,a.useState)(null),{studySets:h,sessions:g,fetchStudySets:y,fetchStudySessions:v}=(0,b.useStudyStore)(),{documents:f}=(0,N.q)();(0,a.useEffect)(()=>{(async()=>{d(!0),u(null);try{await Promise.all([y(),v(s)])}catch(e){u(e instanceof Error?e.message:"Failed to load analytics data")}finally{d(!1)}})()},[s,y,v]);const j=()=>(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)(x,{studySets:h,sessions:g,documents:f,timeRange:s,isLoading:c}),(0,i.jsx)(l,{studySets:h,sessions:g})]});return(0,i.jsx)("div",{className:"min-h-screen bg-background-primary text-white",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Analytics"}),(0,i.jsx)("p",{className:"text-gray-400",children:"Track your learning progress and performance insights"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsxs)("select",{value:s,onChange:e=>o(e.target.value),className:"appearance-none bg-background-secondary border border-border-primary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[(0,i.jsx)("option",{value:"7d",children:"Last 7 days"}),(0,i.jsx)("option",{value:"30d",children:"Last 30 days"}),(0,i.jsx)("option",{value:"90d",children:"Last 90 days"}),(0,i.jsx)("option",{value:"all",children:"All time"})]}),(0,i.jsx)(n.wXT,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),(0,i.jsxs)(S.$,{onClick:()=>{const e=[["Date","Study Set","Type","Items Reviewed","Time Spent (minutes)","Accuracy"].join(","),...g.map(e=>{const t=h.find(t=>t.id===e.studySetId);return[new Date(e.startTime).toLocaleDateString(),(null===t||void 0===t?void 0:t.name)||"Unknown",e.type,e.reviewedItems,Math.round(e.timeSpent/60),e.correctAnswers?"".concat(Math.round(e.correctAnswers/e.totalItems*100),"%"):"N/A"].join(",")})].join("\n"),t=new Blob([e],{type:"text/csv"}),s=window.URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download="study-analytics-".concat((new Date).toISOString().split("T")[0],".csv"),a.click(),window.URL.revokeObjectURL(s)},variant:"secondary",size:"sm",disabled:0===g.length,children:[(0,i.jsx)(n.cIQ,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,i.jsxs)(S.$,{onClick:async()=>{u(null),d(!0);try{await Promise.all([y(),v(s)])}catch(e){u(e instanceof Error?e.message:"Failed to refresh data")}finally{d(!1)}},variant:"secondary",size:"sm",disabled:c,children:[(0,i.jsx)(n.pMz,{className:"w-4 h-4 mr-2 ".concat(c?"animate-spin":"")}),"Refresh"]})]})]}),m&&(0,i.jsxs)("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(n.TMu,{className:"w-5 h-5 text-red-400"}),(0,i.jsx)("span",{className:"text-red-400 font-medium",children:"Error"})]}),(0,i.jsx)("p",{className:"text-red-300 mt-1",children:m}),(0,i.jsx)(S.$,{onClick:()=>u(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsx)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:(0,i.jsx)("nav",{className:"space-y-2",children:I.map(s=>{const a=s.icon,r=e===s.id;return(0,i.jsxs)("button",{onClick:()=>t(s.id),className:"\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\n                        transition-all duration-200\n                        ".concat(r?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white","\n                      "),children:[(0,i.jsx)(a,{className:"w-5 h-5"}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsx)("span",{className:"font-medium block",children:s.label}),(0,i.jsx)("span",{className:"text-xs text-gray-500 block truncate",children:s.description})]})]},s.id)})})})}),(0,i.jsx)("div",{className:"lg:col-span-3",children:(0,i.jsx)(r.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:(()=>{switch(e){case"overview":default:return j();case"performance":return(0,i.jsx)(p,{studySets:h,sessions:g,timeRange:s,isLoading:c});case"trends":return(0,i.jsx)(w,{sessions:g,timeRange:s,isLoading:c})}})()},e)})]})]})})}},2171:(e,t,s)=>{s.d(t,{q:()=>r});var a=s(8957);const r=(0,s(5914).vt)(e=>({documents:[],selectedDocuments:new Set,isLoading:!1,uploadProgress:{},fetchDocuments:async()=>{e({isLoading:!0});try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents",{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)throw new Error("Failed to fetch documents");const a=await s.json();if(!a.success)throw new Error(a.error);e({documents:a.data,isLoading:!1})}catch(t){throw console.error("Fetch documents error:",t),e({isLoading:!1}),t}},uploadDocument:async t=>{const s=new FormData;s.append("document",t);try{const r=localStorage.getItem("auth_token"),n=await fetch("/api/documents/upload",{method:"POST",headers:{Authorization:"Bearer ".concat(r)},body:s});if(!n.ok){const e=await n.json();throw new Error(e.error||"Upload failed")}const i=await n.json();if(i.success)return e(e=>({documents:[i.data,...e.documents],uploadProgress:(0,a.A)((0,a.A)({},e.uploadProgress),{},{[t.name]:100})})),i.data;throw new Error(i.error)}catch(r){throw console.error("Upload document error:",r),r}},deleteDocument:async t=>{try{const s=localStorage.getItem("auth_token"),a=await fetch("/api/documents/".concat(t),{method:"DELETE",headers:{Authorization:"Bearer ".concat(s)}});if(!a.ok){const e=await a.json();throw new Error(e.error||"Delete failed")}e(e=>({documents:e.documents.filter(e=>e.id!==t),selectedDocuments:new Set([...e.selectedDocuments].filter(e=>e!==t))}))}catch(s){throw console.error("Delete document error:",s),s}},searchDocuments:async e=>{try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents/search?q=".concat(encodeURIComponent(e)),{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)throw new Error("Search failed");const a=await s.json();return a.success?a.data:[]}catch(t){return console.error("Search documents error:",t),[]}},getDocument:async e=>{try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)return null;const a=await s.json();return a.success?a.data:null}catch(t){return console.error("Get document error:",t),null}},toggleDocumentSelection:t=>{e(e=>{const s=new Set(e.selectedDocuments);return s.has(t)?s.delete(t):s.add(t),{selectedDocuments:s}})},clearSelection:()=>{e({selectedDocuments:new Set})},selectAll:()=>{e(e=>({selectedDocuments:new Set(e.documents.map(e=>e.id))}))},setUploadProgress:(t,s)=>{e(e=>({uploadProgress:(0,a.A)((0,a.A)({},e.uploadProgress),{},{[t]:s})}))}}))},9855:(e,t,s)=>{s.d(t,{useStudyStore:()=>i});var a=s(8957),r=s(5914);const n=e=>{const t=[...e];for(let s=t.length-1;s>0;s--){const e=Math.floor(Math.random()*(s+1));[t[s],t[e]]=[t[e],t[s]]}return t},i=(0,r.vt)((e,t)=>({currentSession:null,studySetContent:null,studySets:[],sessions:[],isLoading:!1,error:null,actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1,fetchStudySetContent:async function(s){var a;let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{studySetContent:n}=t();if(r||!n||(null===(a=n.studySet)||void 0===a?void 0:a.id)!==s){e({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),a=await fetch("/api/study-sets/".concat(s,"/content"),{headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){const e=await a.json();throw new Error(e.error||"Failed to fetch study set content")}const r=await a.json();if(!r.success)throw new Error(r.error);e({studySetContent:{studySet:r.data.studySet,flashcards:r.data.flashcards||[],questions:r.data.questions||[]},isLoading:!1})}catch(i){throw e({error:i.message||"Failed to fetch study set content",isLoading:!1}),i}}},startStudySession:async function(s,r){var i,o,c;let d=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const{studySetContent:l,fetchStudySetContent:m}=t();l&&(null===(i=l.studySet)||void 0===i?void 0:i.id)===s||await m(s);const u=t().studySetContent;if(!u)throw new Error("Failed to load study set content");const x="flashcards"===r?(null===(o=u.flashcards)||void 0===o?void 0:o.length)||0:(null===(c=u.questions)||void 0===c?void 0:c.length)||0;if(0===x)throw new Error("No study materials found in this set");let h;if(d)if(h=Array.from({length:x},(e,t)=>t),"flashcards"===r&&u.flashcards){const t=n(u.flashcards);e(e=>({studySetContent:(0,a.A)((0,a.A)({},e.studySetContent),{},{flashcards:t})}))}else if("quiz"===r&&u.questions){const t=n(u.questions);e(e=>({studySetContent:(0,a.A)((0,a.A)({},e.studySetContent),{},{questions:t})}))}e({currentSession:{studySetId:s,type:r,startTime:new Date,currentIndex:0,totalItems:x,reviewedItems:[],flaggedItems:[],correctAnswers:"quiz"===r?0:void 0,timeSpent:0,isShuffled:d,originalOrder:h}})},endStudySession:()=>{e({currentSession:null})},nextItem:()=>{const{currentSession:s,addToHistory:r}=t();if(!s)return;const n=s.currentIndex===s.totalItems-1?0:s.currentIndex+1;r({type:"NEXT_ITEM",payload:{fromIndex:s.currentIndex,toIndex:n},previousState:{currentIndex:s.currentIndex},timestamp:Date.now()}),e({currentSession:(0,a.A)((0,a.A)({},s),{},{currentIndex:n})})},previousItem:()=>{const{currentSession:s,addToHistory:r}=t();if(!s)return;const n=0===s.currentIndex?s.totalItems-1:s.currentIndex-1;r({type:"PREVIOUS_ITEM",payload:{fromIndex:s.currentIndex,toIndex:n},previousState:{currentIndex:s.currentIndex},timestamp:Date.now()}),e({currentSession:(0,a.A)((0,a.A)({},s),{},{currentIndex:n})})},goToItem:s=>{const{currentSession:r}=t();if(!r)return;const n=Math.max(0,Math.min(s,r.totalItems-1));e({currentSession:(0,a.A)((0,a.A)({},r),{},{currentIndex:n})})},toggleFlag:s=>{const{currentSession:r,addToHistory:n}=t();if(!r)return;const i=r.flaggedItems.includes(s),o=i?r.flaggedItems.filter(e=>e!==s):[...r.flaggedItems,s];n({type:"TOGGLE_FLAG",payload:{itemId:s,wasFlagged:i},previousState:{flaggedItems:r.flaggedItems},timestamp:Date.now()}),e({currentSession:(0,a.A)((0,a.A)({},r),{},{flaggedItems:o})})},markReviewed:s=>{const{currentSession:r}=t();r&&(r.reviewedItems.includes(r.currentIndex)||e({currentSession:(0,a.A)((0,a.A)({},r),{},{reviewedItems:[...r.reviewedItems,r.currentIndex]})}))},submitQuizAnswer:(s,r,n)=>{const{currentSession:i,markReviewed:o}=t();i&&"quiz"===i.type&&(o(s),n&&e({currentSession:(0,a.A)((0,a.A)({},i),{},{correctAnswers:(i.correctAnswers||0)+1})}))},updateTimeSpent:s=>{const{currentSession:r}=t();r&&e({currentSession:(0,a.A)((0,a.A)({},r),{},{timeSpent:r.timeSpent+s})})},addToHistory:s=>{const{actionHistory:a,currentActionIndex:r}=t(),n=a.slice(0,r+1);n.push(s);const i=n.slice(-50);e({actionHistory:i,currentActionIndex:i.length-1,canUndo:i.length>0,canRedo:!1})},undo:()=>{const{actionHistory:s,currentActionIndex:r,currentSession:n}=t();if(r<0||!n)return;const i=s[r];e({currentSession:(0,a.A)((0,a.A)({},n),i.previousState),currentActionIndex:r-1,canUndo:r>0,canRedo:!0})},redo:()=>{const{actionHistory:s,currentActionIndex:a,currentSession:r}=t();if(a>=s.length-1||!r)return;const n=a+1,i=s[n];switch(i.type){case"NEXT_ITEM":t().nextItem();break;case"PREVIOUS_ITEM":t().previousItem();break;case"TOGGLE_FLAG":t().toggleFlag(i.payload.itemId);break;case"MARK_REVIEWED":t().markReviewed(i.payload.itemId)}e({currentActionIndex:n,canUndo:!0,canRedo:n<s.length-1})},clearHistory:()=>{e({actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1})},fetchStudySets:async()=>{e({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/study-sets",{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok){const e=await s.json();throw new Error(e.error||"Failed to fetch study sets")}const a=await s.json();if(!a.success)throw new Error(a.error);e({studySets:a.data,isLoading:!1})}catch(t){throw e({error:t.message||"Failed to fetch study sets",isLoading:!1}),t}},fetchStudySessions:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"30d";e({isLoading:!0,error:null});try{const s=localStorage.getItem("auth_token"),r=await fetch("/api/study-sessions?timeRange=".concat(t),{headers:{Authorization:"Bearer ".concat(s)}});if(!r.ok){const e=await r.json();throw new Error(e.error||"Failed to fetch study sessions")}const n=await r.json();if(!n.success)throw new Error(n.error);{const t=n.data.map(e=>(0,a.A)((0,a.A)({},e),{},{startTime:new Date(e.startTime),endTime:e.endTime?new Date(e.endTime):void 0}));e({sessions:t,isLoading:!1})}}catch(s){throw e({error:s.message||"Failed to fetch study sessions",isLoading:!1}),s}},invalidateStudySetContent:s=>{const{studySetContent:a}=t();var r;s?(null===a||void 0===a||null===(r=a.studySet)||void 0===r?void 0:r.id)===s&&e({studySetContent:null}):e({studySetContent:null})},refreshStudySetContent:async e=>{await t().fetchStudySetContent(e,!0)},invalidateStudySets:()=>{e({studySets:[]})}}))}}]);
//# sourceMappingURL=316.6b56635f.chunk.js.map