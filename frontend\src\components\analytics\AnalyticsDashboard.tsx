import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  HiAcademicCap, 
  HiClock, 
  HiT<PERSON>dingUp, 
  <PERSON><PERSON>oll<PERSON><PERSON>,
  HiDocumentT<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HiChart<PERSON>ie
} from 'react-icons/hi';
import { StudySet, DocumentMetadata } from '../../../../shared/types';

interface StudySession {
  id: string;
  studySetId: string;
  type: 'flashcards' | 'quiz';
  startTime: Date;
  endTime?: Date;
  totalItems: number;
  reviewedItems: number;
  flaggedItems: number;
  correctAnswers?: number;
  timeSpent: number;
}

interface AnalyticsDashboardProps {
  studySets: StudySet[];
  sessions: StudySession[];
  documents: DocumentMetadata[];
  timeRange: '7d' | '30d' | '90d' | 'all';
  isLoading: boolean;
}

interface DashboardMetrics {
  totalStudySets: number;
  totalDocuments: number;
  totalSessions: number;
  totalStudyTime: number;
  averageSessionTime: number;
  completionRate: number;
  aiGeneratedSets: number;
  recentActivity: number;
}

const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m`;
  } else {
    return `${seconds}s`;
  }
};

const MetricCard: React.FC<{
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: { value: number; isPositive: boolean };
  isLoading?: boolean;
}> = ({ title, value, subtitle, icon: Icon, trend, isLoading }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
    className="bg-background-secondary rounded-lg p-6 border border-border-primary"
  >
    <div className="flex items-center justify-between mb-4">
      <div className="p-3 bg-primary-500/20 rounded-lg">
        <Icon className="w-6 h-6 text-primary-400" />
      </div>
      
      {trend && (
        <div className={`flex items-center space-x-1 ${
          trend.isPositive ? 'text-green-400' : 'text-red-400'
        }`}>
          <HiTrendingUp className={`w-4 h-4 ${trend.isPositive ? '' : 'rotate-180'}`} />
          <span className="text-sm font-medium">{Math.abs(trend.value)}%</span>
        </div>
      )}
    </div>

    <div>
      <h3 className="text-sm font-medium text-gray-400 mb-1">{title}</h3>
      {isLoading ? (
        <div className="animate-pulse">
          <div className="h-8 w-20 bg-gray-600 rounded"></div>
        </div>
      ) : (
        <p className="text-2xl font-bold text-white">{value}</p>
      )}
      {subtitle && (
        <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
      )}
    </div>
  </motion.div>
);

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  studySets,
  sessions,
  documents,
  timeRange,
  isLoading
}) => {
  const metrics = useMemo((): DashboardMetrics => {
    const now = new Date();
    const timeRangeMs = {
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000,
      'all': Infinity
    }[timeRange];

    const filteredSessions = sessions.filter(session => {
      const sessionTime = new Date(session.startTime).getTime();
      return now.getTime() - sessionTime <= timeRangeMs;
    });

    const totalStudyTime = filteredSessions.reduce((sum, session) => sum + session.timeSpent, 0);
    const totalSessions = filteredSessions.length;
    const averageSessionTime = totalSessions > 0 ? totalStudyTime / totalSessions : 0;

    const totalItems = filteredSessions.reduce((sum, session) => sum + session.totalItems, 0);
    const reviewedItems = filteredSessions.reduce((sum, session) => sum + session.reviewedItems, 0);
    const completionRate = totalItems > 0 ? (reviewedItems / totalItems) * 100 : 0;

    const aiGeneratedSets = studySets.filter(set => set.is_ai_generated).length;

    // Recent activity (sessions in last 24 hours)
    const last24Hours = 24 * 60 * 60 * 1000;
    const recentActivity = sessions.filter(session => {
      const sessionTime = new Date(session.startTime).getTime();
      return now.getTime() - sessionTime <= last24Hours;
    }).length;

    return {
      totalStudySets: studySets.length,
      totalDocuments: documents.length,
      totalSessions,
      totalStudyTime,
      averageSessionTime,
      completionRate,
      aiGeneratedSets,
      recentActivity
    };
  }, [studySets, sessions, documents, timeRange]);

  // Calculate trends (compare with previous period)
  const trends = useMemo(() => {
    const now = new Date();
    const timeRangeMs = {
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000,
      'all': Infinity
    }[timeRange];

    if (timeRangeMs === Infinity) return {};

    const currentPeriodStart = now.getTime() - timeRangeMs;
    const previousPeriodStart = currentPeriodStart - timeRangeMs;

    const currentSessions = sessions.filter(session => {
      const sessionTime = new Date(session.startTime).getTime();
      return sessionTime >= currentPeriodStart;
    });

    const previousSessions = sessions.filter(session => {
      const sessionTime = new Date(session.startTime).getTime();
      return sessionTime >= previousPeriodStart && sessionTime < currentPeriodStart;
    });

    const currentTime = currentSessions.reduce((sum, session) => sum + session.timeSpent, 0);
    const previousTime = previousSessions.reduce((sum, session) => sum + session.timeSpent, 0);

    const timeTrend = previousTime > 0 ? ((currentTime - previousTime) / previousTime) * 100 : 0;
    const sessionsTrend = previousSessions.length > 0 ? 
      ((currentSessions.length - previousSessions.length) / previousSessions.length) * 100 : 0;

    return {
      studyTime: { value: Math.round(timeTrend), isPositive: timeTrend >= 0 },
      sessions: { value: Math.round(sessionsTrend), isPositive: sessionsTrend >= 0 }
    };
  }, [sessions, timeRange]);

  return (
    <div className="space-y-6">
      {/* Main Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Study Sets"
          value={metrics.totalStudySets}
          subtitle="Total created"
          icon={HiCollection}
          isLoading={isLoading}
        />
        
        <MetricCard
          title="Documents"
          value={metrics.totalDocuments}
          subtitle="Uploaded files"
          icon={HiDocumentText}
          isLoading={isLoading}
        />
        
        <MetricCard
          title="Study Time"
          value={formatTime(metrics.totalStudyTime)}
          subtitle={`${timeRange === 'all' ? 'All time' : `Last ${timeRange.replace('d', ' days')}`}`}
          icon={HiClock}
          trend={trends.studyTime}
          isLoading={isLoading}
        />
        
        <MetricCard
          title="Sessions"
          value={metrics.totalSessions}
          subtitle={`${timeRange === 'all' ? 'All time' : `Last ${timeRange.replace('d', ' days')}`}`}
          icon={HiAcademicCap}
          trend={trends.sessions}
          isLoading={isLoading}
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <MetricCard
          title="Avg Session"
          value={formatTime(Math.round(metrics.averageSessionTime))}
          subtitle="Time per session"
          icon={HiClock}
          isLoading={isLoading}
        />
        
        <MetricCard
          title="Completion Rate"
          value={`${Math.round(metrics.completionRate)}%`}
          subtitle="Items reviewed"
          icon={HiChartPie}
          isLoading={isLoading}
        />
        
        <MetricCard
          title="AI Generated"
          value={metrics.aiGeneratedSets}
          subtitle="Study sets created by AI"
          icon={HiSparkles}
          isLoading={isLoading}
        />
      </div>

      {/* Quick Stats */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <h3 className="text-lg font-semibold text-white mb-4">Quick Stats</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-400">{metrics.recentActivity}</div>
            <div className="text-sm text-gray-400">Sessions today</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">
              {studySets.filter(set => set.type === 'flashcards').length}
            </div>
            <div className="text-sm text-gray-400">Flashcard sets</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {studySets.filter(set => set.type === 'quiz').length}
            </div>
            <div className="text-sm text-gray-400">Quiz sets</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">
              {Math.round((metrics.aiGeneratedSets / Math.max(metrics.totalStudySets, 1)) * 100)}%
            </div>
            <div className="text-sm text-gray-400">AI generated</div>
          </div>
        </div>
      </div>
    </div>
  );
};
