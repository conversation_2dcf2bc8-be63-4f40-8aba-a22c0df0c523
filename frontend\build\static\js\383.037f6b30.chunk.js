"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[383],{340:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;const n=r(s(1042));t.PostgrestClient=n.default;const i=r(s(8034));t.PostgrestQueryBuilder=i.default;const o=r(s(8416));t.PostgrestFilterBuilder=o.default;const a=r(s(1926));t.PostgrestTransformBuilder=a.default;const c=r(s(3374));t.PostgrestBuilder=c.default;const l=r(s(1595));t.PostgrestError=l.default,t.default={PostgrestClient:n.default,PostgrestQueryBuilder:i.default,PostgrestFilterBuilder:o.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:c.default,PostgrestError:l.default}},383:(e,t,s)=>{s.d(t,{createClient:()=>zt});class r extends Error{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"FunctionsError",s=arguments.length>2?arguments[2]:void 0;super(e),this.name=t,this.context=s}}class n extends r{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class i extends r{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class o extends r{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var a;!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(a||(a={}));var c=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function o(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(o,a)}c((r=r.apply(e,t||[])).next())})};class l{constructor(e){let{headers:t={},customFetch:r,region:n=a.Any}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=e,this.headers=t,this.region=n,this.fetch=(e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(s.bind(s,3937)).then(e=>{let{default:s}=e;return s(...t)})}:fetch),function(){return t(...arguments)}})(r)}setAuth(e){this.headers.Authorization="Bearer ".concat(e)}invoke(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var s;return c(this,void 0,void 0,function*(){try{const{headers:r,method:a,body:c}=t;let l,h={},{region:u}=t;u||(u=this.region),u&&"any"!==u&&(h["x-region"]=u),c&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!==typeof Blob&&c instanceof Blob||c instanceof ArrayBuffer?(h["Content-Type"]="application/octet-stream",l=c):"string"===typeof c?(h["Content-Type"]="text/plain",l=c):"undefined"!==typeof FormData&&c instanceof FormData?l=c:(h["Content-Type"]="application/json",l=JSON.stringify(c)));const d=yield this.fetch("".concat(this.url,"/").concat(e),{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},h),this.headers),r),body:l}).catch(e=>{throw new n(e)}),f=d.headers.get("x-relay-error");if(f&&"true"===f)throw new i(d);if(!d.ok)throw new o(d);let p,g=(null!==(s=d.headers.get("Content-Type"))&&void 0!==s?s:"text/plain").split(";")[0].trim();return p="application/json"===g?yield d.json():"application/octet-stream"===g?yield d.blob():"text/event-stream"===g?d:"multipart/form-data"===g?yield d.formData():yield d.text(),{data:p,error:null}}catch(r){return{data:null,error:r}}})}}var h=s(340);const{PostgrestClient:u,PostgrestQueryBuilder:d,PostgrestFilterBuilder:f,PostgrestTransformBuilder:p,PostgrestBuilder:g,PostgrestError:v}=h;const y=function(){if("undefined"!==typeof WebSocket)return WebSocket;if("undefined"!==typeof global.WebSocket)return global.WebSocket;if("undefined"!==typeof window.WebSocket)return window.WebSocket;if("undefined"!==typeof self.WebSocket)return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}(),w="realtime-js/".concat("2.11.15");var m,_,b,k,S,T;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(m||(m={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(_||(_={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(b||(b={})),function(e){e.websocket="websocket"}(k||(k={})),function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(S||(S={}));class j{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"===typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){const r=t.getUint8(1),n=t.getUint8(2);let i=this.HEADER_LENGTH+2;const o=s.decode(e.slice(i,i+r));i+=r;const a=s.decode(e.slice(i,i+n));i+=n;return{ref:null,topic:o,event:a,payload:JSON.parse(s.decode(e.slice(i,e.byteLength)))}}}class E{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(T||(T={}));const P=function(e,t){var s;const r=null!==(s=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce((s,n)=>(s[n]=O(n,e,t,r),s),{})},O=(e,t,s,r)=>{const n=t.find(t=>t.name===e),i=null===n||void 0===n?void 0:n.type,o=s[e];return i&&!r.includes(i)?A(i,o):C(o)},A=(e,t)=>{if("_"===e.charAt(0)){const s=e.slice(1,e.length);return U(t,s)}switch(e){case T.bool:return I(t);case T.float4:case T.float8:case T.int2:case T.int4:case T.int8:case T.numeric:case T.oid:return R(t);case T.json:case T.jsonb:return x(t);case T.timestamp:return L(t);case T.abstime:case T.date:case T.daterange:case T.int4range:case T.int8range:case T.money:case T.reltime:case T.text:case T.time:case T.timestamptz:case T.timetz:case T.tsrange:case T.tstzrange:default:return C(t)}},C=e=>e,I=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},R=e=>{if("string"===typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},x=e=>{if("string"===typeof e)try{return JSON.parse(e)}catch(t){return console.log("JSON parse error: ".concat(t)),e}return e},U=(e,t)=>{if("string"!==typeof e)return e;const s=e.length-1,r=e[s];if("{"===e[0]&&"}"===r){let r;const i=e.slice(1,s);try{r=JSON.parse("["+i+"]")}catch(n){r=i?i.split(","):[]}return r.map(e=>A(t,e))}return e},L=e=>"string"===typeof e?e.replace(" ","T"):e,D=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class N{constructor(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e4;this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null===(s=this.receivedResp)||void 0===s?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive(e){let{status:t,response:s}=e;this.recHooks.filter(e=>e.status===t).forEach(e=>e.callback(s))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var q,B,M,F;!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(q||(q={}));class W{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(null===t||void 0===t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},e=>{const{onJoin:t,onLeave:s,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=W.syncState(this.state,e,t,s),this.pendingDiffs.forEach(e=>{this.state=W.syncDiff(this.state,e,t,s)}),this.pendingDiffs=[],r()}),this.channel._on(s.diff,{},e=>{const{onJoin:t,onLeave:s,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=W.syncDiff(this.state,e,t,s),r())}),this.onJoin((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})}),this.onLeave((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){const n=this.cloneDeep(e),i=this.transformState(t),o={},a={};return this.map(n,(e,t)=>{i[e]||(a[e]=t)}),this.map(i,(e,t)=>{const s=n[e];if(s){const r=t.map(e=>e.presence_ref),n=s.map(e=>e.presence_ref),i=t.filter(e=>n.indexOf(e.presence_ref)<0),c=s.filter(e=>r.indexOf(e.presence_ref)<0);i.length>0&&(o[e]=i),c.length>0&&(a[e]=c)}else o[e]=t}),this.syncDiff(n,{joins:o,leaves:a},s,r)}static syncDiff(e,t,s,r){const{joins:n,leaves:i}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(n,(t,r)=>{var n;const i=null!==(n=e[t])&&void 0!==n?n:[];if(e[t]=this.cloneDeep(r),i.length>0){const s=e[t].map(e=>e.presence_ref),r=i.filter(e=>s.indexOf(e.presence_ref)<0);e[t].unshift(...r)}s(t,i,r)}),this.map(i,(t,s)=>{let n=e[t];if(!n)return;const i=s.map(e=>e.presence_ref);n=n.filter(e=>i.indexOf(e.presence_ref)<0),e[t]=n,r(t,n,s),0===n.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,s)=>{const r=e[s];return t[s]="metas"in r?r.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(B||(B={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(M||(M={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(F||(F={}));class z{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}},s=arguments.length>2?arguments[2]:void 0;this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=_.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new N(this,b.join,this.params,this.timeout),this.rejoinTimer=new E(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=_.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel","close ".concat(this.topic," ").concat(this._joinRef())),this.state=_.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel","error ".concat(this.topic),e),this.state=_.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel","timeout ".concat(this.topic),this.joinPush.timeout),this.state=_.errored,this.rejoinTimer.scheduleTimeout())}),this._on(b.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new W(this),this.broadcastEndpointURL=D(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.timeout;var s,r;if(this.socket.isConnected()||this.socket.connect(),this.state==_.closed){const{config:{broadcast:n,presence:i,private:o}}=this.params;this._onError(t=>null===e||void 0===e?void 0:e(F.CHANNEL_ERROR,t)),this._onClose(()=>null===e||void 0===e?void 0:e(F.CLOSED));const a={},c={broadcast:n,presence:i,postgres_changes:null!==(r=null===(s=this.bindings.postgres_changes)||void 0===s?void 0:s.map(e=>e.filter))&&void 0!==r?r:[],private:o};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},a)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async t=>{let{postgres_changes:s}=t;var r;if(this.socket.setAuth(),void 0!==s){const t=this.bindings.postgres_changes,n=null!==(r=null===t||void 0===t?void 0:t.length)&&void 0!==r?r:0,i=[];for(let r=0;r<n;r++){const n=t[r],{filter:{event:o,schema:a,table:c,filter:l}}=n,h=s&&s[r];if(!h||h.event!==o||h.schema!==a||h.table!==c||h.filter!==l)return this.unsubscribe(),this.state=_.errored,void(null===e||void 0===e||e(F.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));i.push(Object.assign(Object.assign({},n),{id:h.id}))}return this.bindings.postgres_changes=i,void(e&&e(F.SUBSCRIBED))}null===e||void 0===e||e(F.SUBSCRIBED)}).receive("error",t=>{this.state=_.errored,null===e||void 0===e||e(F.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null===e||void 0===e||e(F.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var s,r;if(this._canPush()||"broadcast"!==e.type)return new Promise(s=>{var r,n,i;const o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(i=null===(n=null===(r=this.params)||void 0===r?void 0:r.config)||void 0===n?void 0:n.broadcast)||void 0===i?void 0:i.ack)||s("ok"),o.receive("ok",()=>s("ok")),o.receive("error",()=>s("error")),o.receive("timeout",()=>s("timed out"))});{const{event:i,payload:o}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?"Bearer ".concat(this.socket.accessTokenValue):"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(s=t.timeout)&&void 0!==s?s:this.timeout);return await(null===(r=e.body)||void 0===r?void 0:r.cancel()),e.ok?"ok":"error"}catch(n){return"AbortError"===n.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.timeout;this.state=_.leaving;const t=()=>{this.socket.log("channel","leave ".concat(this.topic)),this._trigger(b.close,"leave",this._joinRef())};this.joinPush.destroy();let s=null;return new Promise(r=>{s=new N(this,b.leave,{},e),s.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})}).finally(()=>{null===s||void 0===s||s.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,s){const r=new AbortController,n=setTimeout(()=>r.abort(),s),i=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(n),i}_push(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.timeout;if(!this.joinedOnce)throw"tried to push '".concat(e,"' to '").concat(this.topic,"' before joining. Use channel.subscribe() before pushing events");let r=new N(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,n;const i=e.toLocaleLowerCase(),{close:o,error:a,leave:c,join:l}=b;if(s&&[o,a,c,l].indexOf(i)>=0&&s!==this._joinRef())return;let h=this._onMessage(i,t,s);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?null===(r=this.bindings.postgres_changes)||void 0===r||r.filter(e=>{var t,s,r;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(r=null===(s=e.filter)||void 0===s?void 0:s.event)||void 0===r?void 0:r.toLocaleLowerCase())===i}).map(e=>e.callback(h,s)):null===(n=this.bindings[i])||void 0===n||n.filter(e=>{var s,r,n,o,a,c;if(["broadcast","presence","postgres_changes"].includes(i)){if("id"in e){const i=e.id,o=null===(s=e.filter)||void 0===s?void 0:s.event;return i&&(null===(r=t.ids)||void 0===r?void 0:r.includes(i))&&("*"===o||(null===o||void 0===o?void 0:o.toLocaleLowerCase())===(null===(n=t.data)||void 0===n?void 0:n.type.toLocaleLowerCase()))}{const s=null===(a=null===(o=null===e||void 0===e?void 0:e.filter)||void 0===o?void 0:o.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===s||s===(null===(c=null===t||void 0===t?void 0:t.event)||void 0===c?void 0:c.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===i}).map(e=>{if("object"===typeof h&&"ids"in h){const e=h.data,{schema:t,table:s,commit_timestamp:r,type:n,errors:i}=e,o={schema:t,table:s,commit_timestamp:r,eventType:n,new:{},old:{},errors:i};h=Object.assign(Object.assign({},o),this._getPayloadRecords(e))}e.callback(h,s)})}_isClosed(){return this.state===_.closed}_isJoined(){return this.state===_.joined}_isJoining(){return this.state===_.joining}_isLeaving(){return this.state===_.leaving}_replyEventName(e){return"chan_reply_".concat(e)}_on(e,t,s){const r=e.toLocaleLowerCase(),n={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(n):this.bindings[r]=[n],this}_off(e,t){const s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(e=>{var r;return!((null===(r=e.type)||void 0===r?void 0:r.toLocaleLowerCase())===s&&z.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(b.close,{},e)}_onError(e){this._on(b.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.timeout;this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=_.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=P(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=P(e.columns,e.old_record)),t}}const J=()=>{};class K{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=J,this.ref=0,this.logger=J,this.conn=null,this.sendBuffer=[],this.serializer=new j,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(s.bind(s,3937)).then(e=>{let{default:s}=e;return s(...t)})}:fetch),function(){return t(...arguments)}},this.endPoint="".concat(e,"/").concat(k.websocket),this.httpEndpoint=D(e),(null===t||void 0===t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null===t||void 0===t?void 0:t.params)&&(this.params=t.params),(null===t||void 0===t?void 0:t.timeout)&&(this.timeout=t.timeout),(null===t||void 0===t?void 0:t.logger)&&(this.logger=t.logger),((null===t||void 0===t?void 0:t.logLevel)||(null===t||void 0===t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null===t||void 0===t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const n=null===(r=null===t||void 0===t?void 0:t.params)||void 0===r?void 0:r.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=(null===t||void 0===t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null===t||void 0===t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null===t||void 0===t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new E(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null===t||void 0===t?void 0:t.fetch),null===t||void 0===t?void 0:t.worker){if("undefined"!==typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null===t||void 0===t?void 0:t.worker)||!1,this.workerUrl=null===t||void 0===t?void 0:t.workerUrl}this.accessToken=(null===t||void 0===t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=y),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!==t&&void 0!==t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case m.connecting:return S.Connecting;case m.open:return S.Open;case m.closing:return S.Closing;default:return S.Closed}}isConnected(){return this.connectionState()===S.Open}channel(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}};const s="realtime:".concat(e),r=this.getChannels().find(e=>e.topic===s);if(r)return r;{const s=new z("realtime:".concat(e),t,this);return this.channels.push(s),s}}push(e){const{topic:t,event:s,payload:r,ref:n}=e,i=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push","".concat(t," ").concat(s," (").concat(n,")"),r),this.isConnected()?i():this.sendBuffer.push(i)}async setAuth(){let e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=e&&(this.accessTokenValue=e,this.channels.forEach(t=>{const s={access_token:e,version:w};e&&t.updateJoinPayload(s),t.joinedOnce&&t._isJoined()&&t._push(b.access_token,{access_token:e})}))}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",'leaving duplicate topic "'.concat(e,'"')),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:s,payload:r,ref:n}=e;"phoenix"===t&&"phx_reply"===s&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),n&&n===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive","".concat(r.status||""," ").concat(t," ").concat(s," ").concat(n&&"("+n+")"||""),r),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(s,r,n)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport","connected to ".concat(this.endpointURL())),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker","starting worker for from ".concat(this.workerUrl)):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport","".concat(e)),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(b.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return"".concat(e).concat(s).concat(r)}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class H extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function G(e){return"object"===typeof e&&null!==e&&"__isStorageError"in e}class V extends H{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Y extends H{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var $=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function o(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(o,a)}c((r=r.apply(e,t||[])).next())})};const Q=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(s.bind(s,3937)).then(e=>{let{default:s}=e;return s(...t)})}:fetch),function(){return t(...arguments)}},X=e=>{if(Array.isArray(e))return e.map(e=>X(e));if("function"===typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(e=>{let[s,r]=e;const n=s.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[n]=X(r)}),t};var Z=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function o(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(o,a)}c((r=r.apply(e,t||[])).next())})};const ee=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),te=(e,t,r)=>Z(void 0,void 0,void 0,function*(){const n=yield $(void 0,void 0,void 0,function*(){return"undefined"===typeof Response?(yield Promise.resolve().then(s.bind(s,3937))).Response:Response});e instanceof n&&!(null===r||void 0===r?void 0:r.noResolveJson)?e.json().then(s=>{t(new V(ee(s),e.status||500))}).catch(e=>{t(new Y(ee(e),e))}):t(new Y(ee(e),e))});function se(e,t,s,r,n,i){return Z(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(s,((e,t,s,r)=>{const n={method:e,headers:(null===t||void 0===t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null===t||void 0===t?void 0:t.headers),r&&(n.body=JSON.stringify(r)),Object.assign(Object.assign({},n),s))})(t,r,n,i)).then(e=>{if(!e.ok)throw e;return(null===r||void 0===r?void 0:r.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>te(e,a,r))})})}function re(e,t,s,r){return Z(this,void 0,void 0,function*(){return se(e,"GET",t,s,r)})}function ne(e,t,s,r,n){return Z(this,void 0,void 0,function*(){return se(e,"POST",t,r,n,s)})}function ie(e,t,s,r,n){return Z(this,void 0,void 0,function*(){return se(e,"DELETE",t,r,n,s)})}var oe=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function o(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(o,a)}c((r=r.apply(e,t||[])).next())})};const ae={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ce={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class le{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;this.url=e,this.headers=t,this.bucketId=s,this.fetch=Q(r)}uploadOrUpdate(e,t,s,r){return oe(this,void 0,void 0,function*(){try{let n;const i=Object.assign(Object.assign({},ce),r);let o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(i.upsert)});const a=i.metadata;"undefined"!==typeof Blob&&s instanceof Blob?(n=new FormData,n.append("cacheControl",i.cacheControl),a&&n.append("metadata",this.encodeMetadata(a)),n.append("",s)):"undefined"!==typeof FormData&&s instanceof FormData?(n=s,n.append("cacheControl",i.cacheControl),a&&n.append("metadata",this.encodeMetadata(a))):(n=s,o["cache-control"]="max-age=".concat(i.cacheControl),o["content-type"]=i.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null===r||void 0===r?void 0:r.headers)&&(o=Object.assign(Object.assign({},o),r.headers));const c=this._removeEmptyFolders(t),l=this._getFinalPath(c),h=yield this.fetch("".concat(this.url,"/object/").concat(l),Object.assign({method:e,body:n,headers:o},(null===i||void 0===i?void 0:i.duplex)?{duplex:i.duplex}:{})),u=yield h.json();if(h.ok)return{data:{path:c,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(n){if(G(n))return{data:null,error:n};throw n}})}upload(e,t,s){return oe(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return oe(this,void 0,void 0,function*(){const n=this._removeEmptyFolders(e),i=this._getFinalPath(n),o=new URL(this.url+"/object/upload/sign/".concat(i));o.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:ce.upsert},r),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!==typeof Blob&&s instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",s)):"undefined"!==typeof FormData&&s instanceof FormData?(e=s,e.append("cacheControl",t.cacheControl)):(e=s,i["cache-control"]="max-age=".concat(t.cacheControl),i["content-type"]=t.contentType);const a=yield this.fetch(o.toString(),{method:"PUT",body:e,headers:i}),c=yield a.json();if(a.ok)return{data:{path:n,fullPath:c.Key},error:null};return{data:null,error:c}}catch(a){if(G(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(e,t){return oe(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e);const r=Object.assign({},this.headers);(null===t||void 0===t?void 0:t.upsert)&&(r["x-upsert"]="true");const n=yield ne(this.fetch,"".concat(this.url,"/object/upload/sign/").concat(s),{},{headers:r}),i=new URL(this.url+n.url),o=i.searchParams.get("token");if(!o)throw new H("No token returned by API");return{data:{signedUrl:i.toString(),path:e,token:o},error:null}}catch(s){if(G(s))return{data:null,error:s};throw s}})}update(e,t,s){return oe(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return oe(this,void 0,void 0,function*(){try{return{data:yield ne(this.fetch,"".concat(this.url,"/object/move"),{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null===s||void 0===s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(G(r))return{data:null,error:r};throw r}})}copy(e,t,s){return oe(this,void 0,void 0,function*(){try{return{data:{path:(yield ne(this.fetch,"".concat(this.url,"/object/copy"),{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null===s||void 0===s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(G(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,s){return oe(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),n=yield ne(this.fetch,"".concat(this.url,"/object/sign/").concat(r),Object.assign({expiresIn:t},(null===s||void 0===s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers});const i=(null===s||void 0===s?void 0:s.download)?"&download=".concat(!0===s.download?"":s.download):"";return n={signedUrl:encodeURI("".concat(this.url).concat(n.signedURL).concat(i))},{data:n,error:null}}catch(r){if(G(r))return{data:null,error:r};throw r}})}createSignedUrls(e,t,s){return oe(this,void 0,void 0,function*(){try{const r=yield ne(this.fetch,"".concat(this.url,"/object/sign/").concat(this.bucketId),{expiresIn:t,paths:e},{headers:this.headers}),n=(null===s||void 0===s?void 0:s.download)?"&download=".concat(!0===s.download?"":s.download):"";return{data:r.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI("".concat(this.url).concat(e.signedURL).concat(n)):null})),error:null}}catch(r){if(G(r))return{data:null,error:r};throw r}})}download(e,t){return oe(this,void 0,void 0,function*(){const s="undefined"!==typeof(null===t||void 0===t?void 0:t.transform)?"render/image/authenticated":"object",r=this.transformOptsToQueryString((null===t||void 0===t?void 0:t.transform)||{}),n=r?"?".concat(r):"";try{const t=this._getFinalPath(e),r=yield re(this.fetch,"".concat(this.url,"/").concat(s,"/").concat(t).concat(n),{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(i){if(G(i))return{data:null,error:i};throw i}})}info(e){return oe(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield re(this.fetch,"".concat(this.url,"/object/info/").concat(t),{headers:this.headers});return{data:X(e),error:null}}catch(s){if(G(s))return{data:null,error:s};throw s}})}exists(e){return oe(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,s,r){return Z(this,void 0,void 0,function*(){return se(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),r)})}(this.fetch,"".concat(this.url,"/object/").concat(t),{headers:this.headers}),{data:!0,error:null}}catch(s){if(G(s)&&s instanceof Y){const e=s.originalError;if([400,404].includes(null===e||void 0===e?void 0:e.status))return{data:!1,error:s}}throw s}})}getPublicUrl(e,t){const s=this._getFinalPath(e),r=[],n=(null===t||void 0===t?void 0:t.download)?"download=".concat(!0===t.download?"":t.download):"";""!==n&&r.push(n);const i="undefined"!==typeof(null===t||void 0===t?void 0:t.transform)?"render/image":"object",o=this.transformOptsToQueryString((null===t||void 0===t?void 0:t.transform)||{});""!==o&&r.push(o);let a=r.join("&");return""!==a&&(a="?".concat(a)),{data:{publicUrl:encodeURI("".concat(this.url,"/").concat(i,"/public/").concat(s).concat(a))}}}remove(e){return oe(this,void 0,void 0,function*(){try{return{data:yield ie(this.fetch,"".concat(this.url,"/object/").concat(this.bucketId),{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(G(t))return{data:null,error:t};throw t}})}list(e,t,s){return oe(this,void 0,void 0,function*(){try{const r=Object.assign(Object.assign(Object.assign({},ae),t),{prefix:e||""});return{data:yield ne(this.fetch,"".concat(this.url,"/object/list/").concat(this.bucketId),r,{headers:this.headers},s),error:null}}catch(r){if(G(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!==typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return"".concat(this.bucketId,"/").concat(e)}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push("width=".concat(e.width)),e.height&&t.push("height=".concat(e.height)),e.resize&&t.push("resize=".concat(e.resize)),e.format&&t.push("format=".concat(e.format)),e.quality&&t.push("quality=".concat(e.quality)),t.join("&")}}const he={"X-Client-Info":"storage-js/".concat("2.7.1")};var ue=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function o(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(o,a)}c((r=r.apply(e,t||[])).next())})};class de{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;this.url=e,this.headers=Object.assign(Object.assign({},he),t),this.fetch=Q(s)}listBuckets(){return ue(this,void 0,void 0,function*(){try{return{data:yield re(this.fetch,"".concat(this.url,"/bucket"),{headers:this.headers}),error:null}}catch(e){if(G(e))return{data:null,error:e};throw e}})}getBucket(e){return ue(this,void 0,void 0,function*(){try{return{data:yield re(this.fetch,"".concat(this.url,"/bucket/").concat(e),{headers:this.headers}),error:null}}catch(t){if(G(t))return{data:null,error:t};throw t}})}createBucket(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{public:!1};return ue(this,void 0,void 0,function*(){try{return{data:yield ne(this.fetch,"".concat(this.url,"/bucket"),{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(G(s))return{data:null,error:s};throw s}})}updateBucket(e,t){return ue(this,void 0,void 0,function*(){try{const s=yield function(e,t,s,r,n){return Z(this,void 0,void 0,function*(){return se(e,"PUT",t,r,n,s)})}(this.fetch,"".concat(this.url,"/bucket/").concat(e),{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:s,error:null}}catch(s){if(G(s))return{data:null,error:s};throw s}})}emptyBucket(e){return ue(this,void 0,void 0,function*(){try{return{data:yield ne(this.fetch,"".concat(this.url,"/bucket/").concat(e,"/empty"),{},{headers:this.headers}),error:null}}catch(t){if(G(t))return{data:null,error:t};throw t}})}deleteBucket(e){return ue(this,void 0,void 0,function*(){try{return{data:yield ie(this.fetch,"".concat(this.url,"/bucket/").concat(e),{},{headers:this.headers}),error:null}}catch(t){if(G(t))return{data:null,error:t};throw t}})}}class fe extends de{constructor(e){super(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},arguments.length>2?arguments[2]:void 0)}from(e){return new le(this.url,this.headers,e,this.fetch)}}let pe="";pe="undefined"!==typeof Deno?"deno":"undefined"!==typeof document?"web":"undefined"!==typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const ge={headers:{"X-Client-Info":"supabase-js-".concat(pe,"/").concat("2.50.2")}},ve={schema:"public"},ye={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},we={};var me=s(3937),_e=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function o(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(o,a)}c((r=r.apply(e,t||[])).next())})};const be=e=>{let t;return t=e||("undefined"===typeof fetch?me.default:fetch),function(){return t(...arguments)}},ke=(e,t,s)=>{const r=be(s),n="undefined"===typeof Headers?me.Headers:Headers;return(s,i)=>_e(void 0,void 0,void 0,function*(){var o;const a=null!==(o=yield t())&&void 0!==o?o:e;let c=new n(null===i||void 0===i?void 0:i.headers);return c.has("apikey")||c.set("apikey",e),c.has("Authorization")||c.set("Authorization","Bearer ".concat(a)),r(s,Object.assign(Object.assign({},i),{headers:c}))})};var Se=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function o(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(o,a)}c((r=r.apply(e,t||[])).next())})};const Te="2.70.0",je=3e4,Ee=9e4,Pe={"X-Client-Info":"gotrue-js/".concat(Te)},Oe="X-Supabase-Api-Version",Ae={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},Ce=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class Ie extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function Re(e){return"object"===typeof e&&null!==e&&"__isAuthError"in e}class xe extends Ie{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}class Ue extends Ie{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Le extends Ie{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class De extends Le{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class Ne extends Le{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class qe extends Le{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Be extends Le{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Me extends Le{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Fe extends Le{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function We(e){return Re(e)&&"AuthRetryableFetchError"===e.name}class ze extends Le{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class Je extends Le{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Ke="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),He=" \t\n\r=".split(""),Ge=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<He.length;t+=1)e[He[t].charCodeAt(0)]=-2;for(let t=0;t<Ke.length;t+=1)e[Ke[t].charCodeAt(0)]=t;return e})();function Ve(e,t,s){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;s(Ke[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;s(Ke[e]),t.queuedBits-=6}}function Ye(e,t,s){const r=Ge[e];if(!(r>-1)){if(-2===r)return;throw new Error('Invalid Base64-URL character "'.concat(String.fromCharCode(e),'"'))}for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function $e(e){const t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=e=>{!function(e,t,s){if(0===t.utf8seq){if(e<=127)return void s(e);for(let s=1;s<6;s+=1)if(0===(e>>7-s&1)){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}(e,r,s)};for(let o=0;o<e.length;o+=1)Ye(e.charCodeAt(o),n,i);return t.join("")}function Qe(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error("Unrecognized Unicode codepoint: ".concat(e.toString(16)))}t(e)}function Xe(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){const t=1024*(r-55296)&65535;r=65536+(e.charCodeAt(s+1)-56320&65535|t),s+=1}Qe(r,t)}}function Ze(e){const t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let n=0;n<e.length;n+=1)Ye(e.charCodeAt(n),s,r);return new Uint8Array(t)}function et(e){const t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};return e.forEach(e=>Ve(e,s,r)),Ve(null,s,r),t.join("")}const tt=()=>"undefined"!==typeof window&&"undefined"!==typeof document,st={tested:!1,writable:!1},rt=()=>{if(!tt())return!1;try{if("object"!==typeof globalThis.localStorage)return!1}catch(t){return!1}if(st.tested)return st.writable;const e="lswt-".concat(Math.random()).concat(Math.random());try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),st.tested=!0,st.writable=!0}catch(t){st.tested=!0,st.writable=!1}return st.writable};const nt=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(s.bind(s,3937)).then(e=>{let{default:s}=e;return s(...t)})}:fetch),function(){return t(...arguments)}},it=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},ot=async(e,t)=>{const s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch(r){return s}},at=async(e,t)=>{await e.removeItem(t)};class ct{constructor(){this.promise=new ct.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function lt(e){const t=e.split(".");if(3!==t.length)throw new Je("Invalid JWT structure");for(let s=0;s<t.length;s++)if(!Ce.test(t[s]))throw new Je("JWT not in base64url format");return{header:JSON.parse($e(t[0])),payload:JSON.parse($e(t[1])),signature:Ze(t[2]),raw:{header:t[0],payload:t[1]}}}function ht(e){return("0"+e.toString(16)).substr(-2)}async function ut(e){if(!("undefined"!==typeof crypto&&"undefined"!==typeof crypto.subtle&&"undefined"!==typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),s=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(s);return Array.from(r).map(e=>String.fromCharCode(e)).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function dt(e,t){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=function(){const e=new Uint32Array(56);if("undefined"===typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let s="";for(let r=0;r<56;r++)s+=e.charAt(Math.floor(Math.random()*t));return s}return crypto.getRandomValues(e),Array.from(e,ht).join("")}();let n=r;s&&(n+="/PASSWORD_RECOVERY"),await it(e,"".concat(t,"-code-verifier"),n);const i=await ut(r);return[i,r===i?"plain":"s256"]}ct.promiseConstructor=Promise;const ft=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const pt=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function gt(e){if(!pt.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var vt=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]])}return s};const yt=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),wt=[502,503,504];async function mt(e){var t,s;if(!("object"===typeof(s=e)&&null!==s&&"status"in s&&"ok"in s&&"json"in s&&"function"===typeof s.json))throw new Fe(yt(e),0);if(wt.includes(e.status))throw new Fe(yt(e),e.status);let r,n;try{r=await e.json()}catch(o){throw new Ue(yt(o),o)}const i=function(e){const t=e.headers.get(Oe);if(!t)return null;if(!t.match(ft))return null;try{return new Date("".concat(t,"T00:00:00.0Z"))}catch(o){return null}}(e);if(i&&i.getTime()>=Ae.timestamp&&"object"===typeof r&&r&&"string"===typeof r.code?n=r.code:"object"===typeof r&&r&&"string"===typeof r.error_code&&(n=r.error_code),n){if("weak_password"===n)throw new ze(yt(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===n)throw new De}else if("object"===typeof r&&r&&"object"===typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"===typeof t,!0))throw new ze(yt(r),e.status,r.weak_password.reasons);throw new xe(yt(r),e.status||500,n)}async function _t(e,t,s,r){var n;const i=Object.assign({},null===r||void 0===r?void 0:r.headers);i[Oe]||(i[Oe]=Ae.name),(null===r||void 0===r?void 0:r.jwt)&&(i.Authorization="Bearer ".concat(r.jwt));const o=null!==(n=null===r||void 0===r?void 0:r.query)&&void 0!==n?n:{};(null===r||void 0===r?void 0:r.redirectTo)&&(o.redirect_to=r.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",c=await async function(e,t,s,r,n,i){const o=((e,t,s,r)=>{const n={method:e,headers:(null===t||void 0===t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null===t||void 0===t?void 0:t.headers),n.body=JSON.stringify(r),Object.assign(Object.assign({},n),s))})(t,r,n,i);let a;try{a=await e(s,Object.assign({},o))}catch(c){throw console.error(c),new Fe(yt(c),0)}a.ok||await mt(a);if(null===r||void 0===r?void 0:r.noResolveJson)return a;try{return await a.json()}catch(c){await mt(c)}}(e,t,s+a,{headers:i,noResolveJson:null===r||void 0===r?void 0:r.noResolveJson},{},null===r||void 0===r?void 0:r.body);return(null===r||void 0===r?void 0:r.xform)?null===r||void 0===r?void 0:r.xform(c):{data:Object.assign({},c),error:null}}function bt(e){var t;let s=null;var r;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(s=Object.assign({},e),e.expires_at||(s.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:s,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function kt(e){const t=bt(e);return!t.error&&e.weak_password&&"object"===typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"===typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"===typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function St(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Tt(e){return{data:e,error:null}}function jt(e){const{action_link:t,email_otp:s,hashed_token:r,redirect_to:n,verification_type:i}=e,o=vt(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:s,hashed_token:r,redirect_to:n,verification_type:i},user:Object.assign({},o)},error:null}}function Et(e){return e}const Pt=["global","local","others"];var Ot=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]])}return s};class At{constructor(e){let{url:t="",headers:s={},fetch:r}=e;this.url=t,this.headers=s,this.fetch=nt(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Pt[0];if(Pt.indexOf(t)<0)throw new Error("@supabase/auth-js: Parameter scope must be one of ".concat(Pt.join(", ")));try{return await _t(this.fetch,"POST","".concat(this.url,"/logout?scope=").concat(t),{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(s){if(Re(s))return{data:null,error:s};throw s}}async inviteUserByEmail(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return await _t(this.fetch,"POST","".concat(this.url,"/invite"),{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:St})}catch(s){if(Re(s))return{data:{user:null},error:s};throw s}}async generateLink(e){try{const{options:t}=e,s=Ot(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=null===s||void 0===s?void 0:s.newEmail,delete r.newEmail),await _t(this.fetch,"POST","".concat(this.url,"/admin/generate_link"),{body:r,headers:this.headers,xform:jt,redirectTo:null===t||void 0===t?void 0:t.redirectTo})}catch(t){if(Re(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await _t(this.fetch,"POST","".concat(this.url,"/admin/users"),{body:e,headers:this.headers,xform:St})}catch(t){if(Re(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,s,r,n,i,o,a;try{const c={nextPage:null,lastPage:0,total:0},l=await _t(this.fetch,"GET","".concat(this.url,"/admin/users"),{headers:this.headers,noResolveJson:!0,query:{page:null!==(s=null===(t=null===e||void 0===e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==s?s:"",per_page:null!==(n=null===(r=null===e||void 0===e?void 0:e.perPage)||void 0===r?void 0:r.toString())&&void 0!==n?n:""},xform:Et});if(l.error)throw l.error;const h=await l.json(),u=null!==(i=l.headers.get("x-total-count"))&&void 0!==i?i:0,d=null!==(a=null===(o=l.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(e.split(";")[1].split("=")[1]);c["".concat(s,"Page")]=t}),c.total=parseInt(u)),{data:Object.assign(Object.assign({},h),c),error:null}}catch(c){if(Re(c))return{data:{users:[]},error:c};throw c}}async getUserById(e){gt(e);try{return await _t(this.fetch,"GET","".concat(this.url,"/admin/users/").concat(e),{headers:this.headers,xform:St})}catch(t){if(Re(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){gt(e);try{return await _t(this.fetch,"PUT","".concat(this.url,"/admin/users/").concat(e),{body:t,headers:this.headers,xform:St})}catch(s){if(Re(s))return{data:{user:null},error:s};throw s}}async deleteUser(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];gt(e);try{return await _t(this.fetch,"DELETE","".concat(this.url,"/admin/users/").concat(e),{headers:this.headers,body:{should_soft_delete:t},xform:St})}catch(s){if(Re(s))return{data:{user:null},error:s};throw s}}async _listFactors(e){gt(e.userId);try{const{data:t,error:s}=await _t(this.fetch,"GET","".concat(this.url,"/admin/users/").concat(e.userId,"/factors"),{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:s}}catch(t){if(Re(t))return{data:null,error:t};throw t}}async _deleteFactor(e){gt(e.userId),gt(e.id);try{return{data:await _t(this.fetch,"DELETE","".concat(this.url,"/admin/users/").concat(e.userId,"/factors/").concat(e.id),{headers:this.headers}),error:null}}catch(t){if(Re(t))return{data:null,error:t};throw t}}}const Ct={getItem:e=>rt()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{rt()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{rt()&&globalThis.localStorage.removeItem(e)}};function It(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}const Rt=!!(globalThis&&rt()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class xt extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class Ut extends xt{}async function Lt(e,t,s){Rt&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),Rt&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async r=>{if(!r){if(0===t)throw Rt&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Ut('Acquiring an exclusive Navigator LockManager lock "'.concat(e,'" immediately failed'));if(Rt)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(n){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",n)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await s()}Rt&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,r.name);try{return await s()}finally{Rt&&console.log("@supabase/gotrue-js: navigatorLock: released",e,r.name)}}))}!function(){if("object"!==typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!==typeof self&&(self.globalThis=self)}}();const Dt={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Pe,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Nt(e,t,s){return await s()}class qt{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=qt.nextInstanceID,qt.nextInstanceID+=1,this.instanceID>0&&tt()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const r=Object.assign(Object.assign({},Dt),e);if(this.logDebugMessages=!!r.debug,"function"===typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new At({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=nt(r.fetch),this.lock=r.lock||Nt,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:tt()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=Lt:this.lock=Nt,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:rt()?this.storage=Ct:(this.memoryStorage={},this.storage=It(this.memoryStorage)):(this.memoryStorage={},this.storage=It(this.memoryStorage)),tt()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(n){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",n)}null===(s=this.broadcastChannel)||void 0===s||s.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(){if(this.logDebugMessages){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];this.logger("GoTrueClient@".concat(this.instanceID," (").concat(Te,") ").concat((new Date).toISOString()),...t)}return this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},s=new URL(e);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach((e,s)=>{t[s]=e})}catch(r){}return s.searchParams.forEach((e,s)=>{t[s]=e}),t}(window.location.href);let s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),tt()&&this.detectSessionInUrl&&"none"!==s){const{data:r,error:n}=await this._getSessionFromURL(t,s);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),function(e){return Re(e)&&"AuthImplicitGrantRedirectError"===e.name}(n)){const t=null===(e=n.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:n}}return await this._removeSession(),{error:n}}const{session:i,redirectType:o}=r;return this._debug("#_initialize()","detected session in URL",i,"redirect type",o),await this._saveSession(i),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",i):await this._notifyAllSubscribers("SIGNED_IN",i)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return Re(t)?{error:t}:{error:new Ue("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,r;try{const n=await _t(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,body:{data:null!==(s=null===(t=null===e||void 0===e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==s?s:{},gotrue_meta_security:{captcha_token:null===(r=null===e||void 0===e?void 0:e.options)||void 0===r?void 0:r.captchaToken}},xform:bt}),{data:i,error:o}=n;if(o||!i)return{data:{user:null,session:null},error:o};const a=i.session,c=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(n){if(Re(n))return{data:{user:null,session:null},error:n};throw n}}async signUp(e){var t,s,r;try{let n;if("email"in e){const{email:s,password:r,options:i}=e;let o=null,a=null;"pkce"===this.flowType&&([o,a]=await dt(this.storage,this.storageKey)),n=await _t(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,redirectTo:null===i||void 0===i?void 0:i.emailRedirectTo,body:{email:s,password:r,data:null!==(t=null===i||void 0===i?void 0:i.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null===i||void 0===i?void 0:i.captchaToken},code_challenge:o,code_challenge_method:a},xform:bt})}else{if(!("phone"in e))throw new qe("You must provide either an email or phone number and a password");{const{phone:t,password:i,options:o}=e;n=await _t(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,body:{phone:t,password:i,data:null!==(s=null===o||void 0===o?void 0:o.data)&&void 0!==s?s:{},channel:null!==(r=null===o||void 0===o?void 0:o.channel)&&void 0!==r?r:"sms",gotrue_meta_security:{captcha_token:null===o||void 0===o?void 0:o.captchaToken}},xform:bt})}}const{data:i,error:o}=n;if(o||!i)return{data:{user:null,session:null},error:o};const a=i.session,c=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(n){if(Re(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithPassword(e){try{let t;if("email"in e){const{email:s,password:r,options:n}=e;t=await _t(this.fetch,"POST","".concat(this.url,"/token?grant_type=password"),{headers:this.headers,body:{email:s,password:r,gotrue_meta_security:{captcha_token:null===n||void 0===n?void 0:n.captchaToken}},xform:kt})}else{if(!("phone"in e))throw new qe("You must provide either an email or phone number and a password");{const{phone:s,password:r,options:n}=e;t=await _t(this.fetch,"POST","".concat(this.url,"/token?grant_type=password"),{headers:this.headers,body:{phone:s,password:r,gotrue_meta_security:{captcha_token:null===n||void 0===n?void 0:n.captchaToken}},xform:kt})}}const{data:s,error:r}=t;return r?{data:{user:null,session:null},error:r}:s&&s.session&&s.user?(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r}):{data:{user:null,session:null},error:new Ne}}catch(t){if(Re(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,s,r,n;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:null===(n=e.options)||void 0===n?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error('@supabase/auth-js: Unsupported chain "'.concat(t,'"'))}async signInWithSolana(e){var t,s,r,n,i,o,a,c,l,h,u,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{const{chain:u,wallet:d,statement:g,options:v}=e;let y;if(tt())if("object"===typeof d)y=d;else{const e=window;if(!("solana"in e)||"object"!==typeof e.solana||!("signIn"in e.solana&&"function"===typeof e.solana.signIn||"signMessage"in e.solana&&"function"===typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");y=e.solana}else{if("object"!==typeof d||!(null===v||void 0===v?void 0:v.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");y=d}const w=new URL(null!==(t=null===v||void 0===v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in y&&y.signIn){const e=await y.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null===v||void 0===v?void 0:v.signInWithSolana),{version:"1",domain:w.host,uri:w.href}),g?{statement:g}:null));let t;if(Array.isArray(e)&&e[0]&&"object"===typeof e[0])t=e[0];else{if(!(e&&"object"===typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"===typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"===typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in y)||"function"!==typeof y.signMessage||!("publicKey"in y)||"object"!==typeof y||!y.publicKey||!("toBase58"in y.publicKey)||"function"!==typeof y.publicKey.toBase58)throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=["".concat(w.host," wants you to sign in with your Solana account:"),y.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1","URI: ".concat(w.href),"Issued At: ".concat(null!==(r=null===(s=null===v||void 0===v?void 0:v.signInWithSolana)||void 0===s?void 0:s.issuedAt)&&void 0!==r?r:(new Date).toISOString()),...(null===(n=null===v||void 0===v?void 0:v.signInWithSolana)||void 0===n?void 0:n.notBefore)?["Not Before: ".concat(v.signInWithSolana.notBefore)]:[],...(null===(i=null===v||void 0===v?void 0:v.signInWithSolana)||void 0===i?void 0:i.expirationTime)?["Expiration Time: ".concat(v.signInWithSolana.expirationTime)]:[],...(null===(o=null===v||void 0===v?void 0:v.signInWithSolana)||void 0===o?void 0:o.chainId)?["Chain ID: ".concat(v.signInWithSolana.chainId)]:[],...(null===(a=null===v||void 0===v?void 0:v.signInWithSolana)||void 0===a?void 0:a.nonce)?["Nonce: ".concat(v.signInWithSolana.nonce)]:[],...(null===(c=null===v||void 0===v?void 0:v.signInWithSolana)||void 0===c?void 0:c.requestId)?["Request ID: ".concat(v.signInWithSolana.requestId)]:[],...(null===(h=null===(l=null===v||void 0===v?void 0:v.signInWithSolana)||void 0===l?void 0:l.resources)||void 0===h?void 0:h.length)?["Resources",...v.signInWithSolana.resources.map(e=>"- ".concat(e))]:[]].join("\n");const e=await y.signMessage((new TextEncoder).encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:s}=await _t(this.fetch,"POST","".concat(this.url,"/token?grant_type=web3"),{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:et(p)},(null===(u=e.options)||void 0===u?void 0:u.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:bt});if(s)throw s;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:s}):{data:{user:null,session:null},error:new Ne}}catch(g){if(Re(g))return{data:{user:null,session:null},error:g};throw g}}async _exchangeCodeForSession(e){const t=await ot(this.storage,"".concat(this.storageKey,"-code-verifier")),[s,r]=(null!==t&&void 0!==t?t:"").split("/");try{const{data:t,error:n}=await _t(this.fetch,"POST","".concat(this.url,"/token?grant_type=pkce"),{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:bt});if(await at(this.storage,"".concat(this.storageKey,"-code-verifier")),n)throw n;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!==r&&void 0!==r?r:null}),error:n}):{data:{user:null,session:null,redirectType:null},error:new Ne}}catch(n){if(Re(n))return{data:{user:null,session:null,redirectType:null},error:n};throw n}}async signInWithIdToken(e){try{const{options:t,provider:s,token:r,access_token:n,nonce:i}=e,o=await _t(this.fetch,"POST","".concat(this.url,"/token?grant_type=id_token"),{headers:this.headers,body:{provider:s,id_token:r,access_token:n,nonce:i,gotrue_meta_security:{captcha_token:null===t||void 0===t?void 0:t.captchaToken}},xform:bt}),{data:a,error:c}=o;return c?{data:{user:null,session:null},error:c}:a&&a.session&&a.user?(a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:c}):{data:{user:null,session:null},error:new Ne}}catch(t){if(Re(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,s,r,n,i;try{if("email"in e){const{email:r,options:n}=e;let i=null,o=null;"pkce"===this.flowType&&([i,o]=await dt(this.storage,this.storageKey));const{error:a}=await _t(this.fetch,"POST","".concat(this.url,"/otp"),{headers:this.headers,body:{email:r,data:null!==(t=null===n||void 0===n?void 0:n.data)&&void 0!==t?t:{},create_user:null===(s=null===n||void 0===n?void 0:n.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null===n||void 0===n?void 0:n.captchaToken},code_challenge:i,code_challenge_method:o},redirectTo:null===n||void 0===n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){const{phone:t,options:s}=e,{data:o,error:a}=await _t(this.fetch,"POST","".concat(this.url,"/otp"),{headers:this.headers,body:{phone:t,data:null!==(r=null===s||void 0===s?void 0:s.data)&&void 0!==r?r:{},create_user:null===(n=null===s||void 0===s?void 0:s.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null===s||void 0===s?void 0:s.captchaToken},channel:null!==(i=null===s||void 0===s?void 0:s.channel)&&void 0!==i?i:"sms"}});return{data:{user:null,session:null,messageId:null===o||void 0===o?void 0:o.message_id},error:a}}throw new qe("You must provide either an email or phone number.")}catch(o){if(Re(o))return{data:{user:null,session:null},error:o};throw o}}async verifyOtp(e){var t,s;try{let r,n;"options"in e&&(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo,n=null===(s=e.options)||void 0===s?void 0:s.captchaToken);const{data:i,error:o}=await _t(this.fetch,"POST","".concat(this.url,"/verify"),{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:r,xform:bt});if(o)throw o;if(!i)throw new Error("An error occurred on token verification.");const a=i.session,c=i.user;return(null===a||void 0===a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(r){if(Re(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,s,r;try{let n=null,i=null;return"pkce"===this.flowType&&([n,i]=await dt(this.storage,this.storageKey)),await _t(this.fetch,"POST","".concat(this.url,"/sso"),{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==s?s:void 0}),(null===(r=null===e||void 0===e?void 0:e.options)||void 0===r?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:i}),headers:this.headers,xform:Tt})}catch(n){if(Re(n))return{data:null,error:n};throw n}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new De;const{error:r}=await _t(this.fetch,"GET","".concat(this.url,"/reauthenticate"),{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}})}catch(e){if(Re(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t="".concat(this.url,"/resend");if("email"in e){const{email:s,type:r,options:n}=e,{error:i}=await _t(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:null===n||void 0===n?void 0:n.captchaToken}},redirectTo:null===n||void 0===n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in e){const{phone:s,type:r,options:n}=e,{data:i,error:o}=await _t(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:null===n||void 0===n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null===i||void 0===i?void 0:i.message_id},error:o}}throw new qe("You must provide either an email or phone number and a type")}catch(t){if(Re(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch(e){}})()),s}return await this.lock("lock:".concat(this.storageKey),e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await ot(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const s=!!e.expires_at&&1e3*e.expires_at-Date.now()<Ee;if(this._debug("#__loadSession()","session has".concat(s?"":" not"," expired"),"expires_at",e.expires_at),!s){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,s,r)=>(t||"user"!==s||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,s,r))})}return{data:{session:e},error:null}}const{session:r,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{session:null},error:n}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,async()=>await this._getUser())}async _getUser(e){try{return e?await _t(this.fetch,"GET","".concat(this.url,"/user"),{headers:this.headers,jwt:e,xform:St}):await this._useSession(async e=>{var t,s,r;const{data:n,error:i}=e;if(i)throw i;return(null===(t=n.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await _t(this.fetch,"GET","".concat(this.url,"/user"),{headers:this.headers,jwt:null!==(r=null===(s=n.session)||void 0===s?void 0:s.access_token)&&void 0!==r?r:void 0,xform:St}):{data:{user:null},error:new De}})}catch(t){if(Re(t))return function(e){return Re(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await at(this.storage,"".concat(this.storageKey,"-code-verifier"))),{data:{user:null},error:t};throw t}}async updateUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return await this._useSession(async s=>{const{data:r,error:n}=s;if(n)throw n;if(!r.session)throw new De;const i=r.session;let o=null,a=null;"pkce"===this.flowType&&null!=e.email&&([o,a]=await dt(this.storage,this.storageKey));const{data:c,error:l}=await _t(this.fetch,"PUT","".concat(this.url,"/user"),{headers:this.headers,redirectTo:null===t||void 0===t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:a}),jwt:i.access_token,xform:St});if(l)throw l;return i.user=c.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}})}catch(s){if(Re(s))return{data:{user:null},error:s};throw s}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new De;const t=Date.now()/1e3;let s=t,r=!0,n=null;const{payload:i}=lt(e.access_token);if(i.exp&&(s=i.exp,r=s<=t),r){const{session:t,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!t)return{data:{user:null,session:null},error:null};n=t}else{const{data:r,error:i}=await this._getUser(e.access_token);if(i)throw i;n={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(Re(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var s;if(!e){const{data:r,error:n}=t;if(n)throw n;e=null!==(s=r.session)&&void 0!==s?s:void 0}if(!(null===e||void 0===e?void 0:e.refresh_token))throw new De;const{session:r,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(Re(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!tt())throw new Be("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Be(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new Me("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new Be("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Me("No code detected.");const{data:t,error:s}=await this._exchangeCodeForSession(e.code);if(s)throw s;const r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:r,access_token:n,refresh_token:i,expires_in:o,expires_at:a,token_type:c}=e;if(!n||!o||!i||!c)throw new Be("No session defined in URL");const l=Math.round(Date.now()/1e3),h=parseInt(o);let u=l+h;a&&(u=parseInt(a));const d=u-l;1e3*d<=je&&console.warn("@supabase/gotrue-js: Session as retrieved from URL expires in ".concat(d,"s, should have been closer to ").concat(h,"s"));const f=u-h;l-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,u,l):l-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,u,l);const{data:p,error:g}=await this._getUser(n);if(g)throw g;const v={provider_token:s,provider_refresh_token:r,access_token:n,expires_in:h,expires_at:u,refresh_token:i,token_type:c,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:e.type},error:null}}catch(s){if(Re(s))return{data:{session:null,redirectType:null},error:s};throw s}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await ot(this.storage,"".concat(this.storageKey,"-code-verifier"));return!(!e.code||!t)}async signOut(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{scope:"global"};return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut(){let{scope:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{scope:"global"};return await this._useSession(async t=>{var s;const{data:r,error:n}=t;if(n)return{error:n};const i=null===(s=r.session)||void 0===s?void 0:s.access_token;if(i){const{error:t}=await this.admin.signOut(i,e);if(t&&(!function(e){return Re(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await at(this.storage,"".concat(this.storageKey,"-code-verifier"))),{error:null}})}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession(async t=>{var s,r;try{const{data:{session:r},error:n}=t;if(n)throw n;await(null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(n){await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",n),console.error(n)}})}async resetPasswordForEmail(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=null,r=null;"pkce"===this.flowType&&([s,r]=await dt(this.storage,this.storageKey,!0));try{return await _t(this.fetch,"POST","".concat(this.url,"/recover"),{body:{email:e,code_challenge:s,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(n){if(Re(n))return{data:null,error:n};throw n}}async getUserIdentities(){var e;try{const{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(Re(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:s,error:r}=await this._useSession(async t=>{var s,r,n,i,o;const{data:a,error:c}=t;if(c)throw c;const l=await this._getUrlForProvider("".concat(this.url,"/user/identities/authorize"),e.provider,{redirectTo:null===(s=e.options)||void 0===s?void 0:s.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(n=e.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:!0});return await _t(this.fetch,"GET",l,{headers:this.headers,jwt:null!==(o=null===(i=a.session)||void 0===i?void 0:i.access_token)&&void 0!==o?o:void 0})});if(r)throw r;return tt()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null===s||void 0===s?void 0:s.url),{data:{provider:e.provider,url:null===s||void 0===s?void 0:s.url},error:null}}catch(s){if(Re(s))return{data:{provider:e.provider,url:null},error:s};throw s}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var s,r;const{data:n,error:i}=t;if(i)throw i;return await _t(this.fetch,"DELETE","".concat(this.url,"/user/identities/").concat(e.identity_id),{headers:this.headers,jwt:null!==(r=null===(s=n.session)||void 0===s?void 0:s.access_token)&&void 0!==r?r:void 0})})}catch(t){if(Re(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t="#_refreshAccessToken(".concat(e.substring(0,5),"...)");this._debug(t,"begin");try{const n=Date.now();return await(s=async s=>(s>0&&await async function(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await _t(this.fetch,"POST","".concat(this.url,"/token?grant_type=refresh_token"),{body:{refresh_token:e},headers:this.headers,xform:bt})),r=(e,t)=>{const s=200*Math.pow(2,e);return t&&We(t)&&Date.now()+s-n<je},new Promise((e,t)=>{(async()=>{for(let i=0;i<1/0;i++)try{const t=await s(i);if(!r(i,null,t))return void e(t)}catch(n){if(!r(i,n))return void t(n)}})()}))}catch(n){if(this._debug(t,"error",n),Re(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(t,"end")}var s,r}_isValidSession(e){return"object"===typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const s=await this._getUrlForProvider("".concat(this.url,"/authorize"),e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),tt()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const s=await ot(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s))return this._debug(t,"session is not valid"),void(null!==s&&await this._removeSession());const r=1e3*(null!==(e=s.expires_at)&&void 0!==e?e:1/0)-Date.now()<Ee;if(this._debug(t,"session has".concat(r?"":" not"," expired with margin of ").concat(Ee,"s")),r){if(this.autoRefreshToken&&s.refresh_token){const{error:e}=await this._callRefreshToken(s.refresh_token);e&&(console.error(e),We(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){return this._debug(t,"error",s),void console.error(s)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new De;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r="#_callRefreshToken(".concat(e.substring(0,5),"...)");this._debug(r,"begin");try{this.refreshingDeferred=new ct;const{data:t,error:s}=await this._refreshAccessToken(e);if(s)throw s;if(!t.session)throw new De;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(n){if(this._debug(r,"error",n),Re(n)){const e={session:null,error:n};return We(n)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(s=this.refreshingDeferred)||void 0===s||s.reject(n),n}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t){let s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const r="#_notifyAllSubscribers(".concat(e,")");this._debug(r,"begin",t,"broadcast = ".concat(s));try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});const r=[],n=Array.from(this.stateChangeEmitters.values()).map(async s=>{try{await s.callback(e,t)}catch(n){r.push(n)}});if(await Promise.all(n),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await it(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await at(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&tt()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),je);this.autoRefreshTicker=e,e&&"object"===typeof e&&"function"===typeof e.unref?e.unref():"undefined"!==typeof Deno&&"function"===typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async e=>{const{data:{session:s}}=e;if(!s||!s.refresh_token||!s.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const r=Math.floor((1e3*s.expires_at-t)/je);this._debug("#_autoRefreshTokenTick()","access token expires in ".concat(r," ticks, a tick lasts ").concat(je,"ms, refresh threshold is ").concat(3," ticks")),r<=3&&await this._callRefreshToken(s.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(!(e.isAcquireTimeout||e instanceof xt))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!tt()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t="#_onVisibilityChanged(".concat(e,")");this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){const r=["provider=".concat(encodeURIComponent(t))];if((null===s||void 0===s?void 0:s.redirectTo)&&r.push("redirect_to=".concat(encodeURIComponent(s.redirectTo))),(null===s||void 0===s?void 0:s.scopes)&&r.push("scopes=".concat(encodeURIComponent(s.scopes))),"pkce"===this.flowType){const[e,t]=await dt(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:"".concat(encodeURIComponent(e)),code_challenge_method:"".concat(encodeURIComponent(t))});r.push(s.toString())}if(null===s||void 0===s?void 0:s.queryParams){const e=new URLSearchParams(s.queryParams);r.push(e.toString())}return(null===s||void 0===s?void 0:s.skipBrowserRedirect)&&r.push("skip_http_redirect=".concat(s.skipBrowserRedirect)),"".concat(e,"?").concat(r.join("&"))}async _unenroll(e){try{return await this._useSession(async t=>{var s;const{data:r,error:n}=t;return n?{data:null,error:n}:await _t(this.fetch,"DELETE","".concat(this.url,"/factors/").concat(e.factorId),{headers:this.headers,jwt:null===(s=null===r||void 0===r?void 0:r.session)||void 0===s?void 0:s.access_token})})}catch(t){if(Re(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var s,r;const{data:n,error:i}=t;if(i)return{data:null,error:i};const o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:a,error:c}=await _t(this.fetch,"POST","".concat(this.url,"/factors"),{body:o,headers:this.headers,jwt:null===(s=null===n||void 0===n?void 0:n.session)||void 0===s?void 0:s.access_token});return c?{data:null,error:c}:("totp"===e.factorType&&(null===(r=null===a||void 0===a?void 0:a.totp)||void 0===r?void 0:r.qr_code)&&(a.totp.qr_code="data:image/svg+xml;utf-8,".concat(a.totp.qr_code)),{data:a,error:null})})}catch(t){if(Re(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;const{data:r,error:n}=t;if(n)return{data:null,error:n};const{data:i,error:o}=await _t(this.fetch,"POST","".concat(this.url,"/factors/").concat(e.factorId,"/verify"),{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(s=null===r||void 0===r?void 0:r.session)||void 0===s?void 0:s.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:o})})}catch(t){if(Re(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;const{data:r,error:n}=t;return n?{data:null,error:n}:await _t(this.fetch,"POST","".concat(this.url,"/factors/").concat(e.factorId,"/challenge"),{body:{channel:e.channel},headers:this.headers,jwt:null===(s=null===r||void 0===r?void 0:r.session)||void 0===s?void 0:s.access_token})})}catch(t){if(Re(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const s=(null===e||void 0===e?void 0:e.factors)||[],r=s.filter(e=>"totp"===e.factor_type&&"verified"===e.status),n=s.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:s,totp:r,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,s;const{data:{session:r},error:n}=e;if(n)return{data:null,error:n};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:i}=lt(r.access_token);let o=null;i.aal&&(o=i.aal);let a=o;(null!==(s=null===(t=r.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==s?s:[]).length>0&&(a="aal2");return{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:i.amr||[]},error:null}}))}async fetchJwk(e){let t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{keys:[]}).keys.find(t=>t.kid===e);if(t)return t;if(t=this.jwks.keys.find(t=>t.kid===e),t&&this.jwks_cached_at+6e5>Date.now())return t;const{data:s,error:r}=await _t(this.fetch,"GET","".concat(this.url,"/.well-known/jwks.json"),{headers:this.headers});if(r)throw r;if(!s.keys||0===s.keys.length)throw new Je("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),t=s.keys.find(t=>t.kid===e),!t)throw new Je("No matching signing key found in JWKS");return t}async getClaims(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{keys:[]};try{let s=e;if(!s){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};s=e.session.access_token}const{header:r,payload:n,signature:i,raw:{header:o,payload:a}}=lt(s);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(n.exp),!r.kid||"HS256"===r.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(s);if(e)throw e;return{data:{claims:n,header:r,signature:i},error:null}}const c=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(r.alg),l=await this.fetchJwk(r.kid,t),h=await crypto.subtle.importKey("jwk",l,c,!0,["verify"]);if(!await crypto.subtle.verify(c,h,i,function(e){const t=[];return Xe(e,e=>t.push(e)),new Uint8Array(t)}("".concat(o,".").concat(a))))throw new Je("Invalid JWT signature");return{data:{claims:n,header:r,signature:i},error:null}}catch(s){if(Re(s))return{data:null,error:s};throw s}}}qt.nextInstanceID=0;const Bt=qt;class Mt extends Bt{constructor(e){super(e)}}var Ft=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function o(e){try{c(r.next(e))}catch(t){i(t)}}function a(e){try{c(r.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(o,a)}c((r=r.apply(e,t||[])).next())})};class Wt{constructor(e,t,s){var r,n,i;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const o=(a=e).endsWith("/")?a:a+"/";var a;const c=new URL(o);this.realtimeUrl=new URL("realtime/v1",c),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",c),this.storageUrl=new URL("storage/v1",c),this.functionsUrl=new URL("functions/v1",c);const l="sb-".concat(c.hostname.split(".")[0],"-auth-token"),h=function(e,t){var s,r;const{db:n,auth:i,realtime:o,global:a}=e,{db:c,auth:l,realtime:h,global:u}=t,d={db:Object.assign(Object.assign({},c),n),auth:Object.assign(Object.assign({},l),i),realtime:Object.assign(Object.assign({},h),o),global:Object.assign(Object.assign(Object.assign({},u),a),{headers:Object.assign(Object.assign({},null!==(s=null===u||void 0===u?void 0:u.headers)&&void 0!==s?s:{}),null!==(r=null===a||void 0===a?void 0:a.headers)&&void 0!==r?r:{})}),accessToken:()=>Se(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!==s&&void 0!==s?s:{},{db:ve,realtime:we,auth:Object.assign(Object.assign({},ye),{storageKey:l}),global:ge});this.storageKey=null!==(r=h.auth.storageKey)&&void 0!==r?r:"",this.headers=null!==(n=h.global.headers)&&void 0!==n?n:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error("@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.".concat(String(t)," is not possible"))}})):this.auth=this._initSupabaseAuthClient(null!==(i=h.auth)&&void 0!==i?i:{},this.headers,h.global.fetch),this.fetch=ke(t,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new u(new URL("rest/v1",c).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new l(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new fe(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.rest.rpc(e,t,s)}channel(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}};return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return Ft(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return null!==(t=null===(e=s.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null})}_initSupabaseAuthClient(e,t,s){let{autoRefreshToken:r,persistSession:n,detectSessionInUrl:i,storage:o,storageKey:a,flowType:c,lock:l,debug:h}=e;const u={Authorization:"Bearer ".concat(this.supabaseKey),apikey:"".concat(this.supabaseKey)};return new Mt({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),t),storageKey:a,autoRefreshToken:r,persistSession:n,detectSessionInUrl:i,storage:o,flowType:c,lock:l,debug:h,fetch:s,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new K(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null===e||void 0===e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null===t||void 0===t?void 0:t.access_token)})}_handleTokenChanged(e,t,s){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===s?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=s}}const zt=(e,t,s)=>new Wt(e,t,s)},1042:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const n=r(s(8034)),i=r(s(8416)),o=s(9401);class a{constructor(e){let{headers:t={},schema:s,fetch:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=e,this.headers=Object.assign(Object.assign({},o.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=r}from(e){const t=new URL("".concat(this.url,"/").concat(e));return new n.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new a(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{head:r=!1,get:n=!1,count:o}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a=new URL("".concat(this.url,"/rpc/").concat(e));let c;r||n?(t=r?"HEAD":"GET",Object.entries(s).filter(e=>{let[t,s]=e;return void 0!==s}).map(e=>{let[t,s]=e;return[t,Array.isArray(s)?"{".concat(s.join(","),"}"):"".concat(s)]}).forEach(e=>{let[t,s]=e;a.searchParams.append(t,s)})):(t="POST",c=s);const l=Object.assign({},this.headers);return o&&(l.Prefer="count=".concat(o)),new i.default({method:t,url:a,headers:l,schema:this.schemaName,body:c,fetch:this.fetch,allowEmpty:!1})}}t.default=a},1595:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});class s extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=s},1926:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const n=r(s(3374));class i extends n.default{select(e){let t=!1;const s=(null!==e&&void 0!==e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e){let{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:n=r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=n?"".concat(n,".order"):"order",o=this.url.searchParams.get(i);return this.url.searchParams.set(i,"".concat(o?"".concat(o,","):"").concat(e,".").concat(t?"asc":"desc").concat(void 0===s?"":s?".nullsfirst":".nullslast")),this}limit(e){let{foreignTable:t,referencedTable:s=t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r="undefined"===typeof s?"limit":"".concat(s,".limit");return this.url.searchParams.set(r,"".concat(e)),this}range(e,t){let{foreignTable:s,referencedTable:r=s}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const n="undefined"===typeof r?"offset":"".concat(r,".offset"),i="undefined"===typeof r?"limit":"".concat(r,".limit");return this.url.searchParams.set(n,"".concat(e)),this.url.searchParams.set(i,"".concat(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain(){let{analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:n=!1,format:i="text"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var o;const a=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,n?"wal":null].filter(Boolean).join("|"),c=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept="application/vnd.pgrst.plan+".concat(i,'; for="').concat(c,'"; options=').concat(a,";"),this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=i},2072:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},3374:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const n=r(s(3937)),i=r(s(1595));t.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"===typeof fetch?this.fetch=n.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,s,r;let n=null,o=null,a=null,c=e.status,l=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(o="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const r=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),i=null===(s=e.headers.get("content-range"))||void 0===s?void 0:s.split("/");r&&i&&i.length>1&&(a=parseInt(i[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(n={code:"PGRST116",details:"Results contain ".concat(o.length," rows, application/vnd.pgrst.object+json requires 1 row"),hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,a=null,c=406,l="Not Acceptable"):o=1===o.length?o[0]:null)}else{const t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(o=[],n=null,c=200,l="OK")}catch(h){404===e.status&&""===t?(c=204,l="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null===(r=null===n||void 0===n?void 0:n.details)||void 0===r?void 0:r.includes("0 rows"))&&(n=null,c=200,l="OK"),n&&this.shouldThrowOnError)throw new i.default(n)}return{error:n,data:o,count:a,status:c,statusText:l}});return this.shouldThrowOnError||(s=s.catch(e=>{var t,s,r;return{error:{message:"".concat(null!==(t=null===e||void 0===e?void 0:e.name)&&void 0!==t?t:"FetchError",": ").concat(null===e||void 0===e?void 0:e.message),details:"".concat(null!==(s=null===e||void 0===e?void 0:e.stack)&&void 0!==s?s:""),hint:"",code:"".concat(null!==(r=null===e||void 0===e?void 0:e.code)&&void 0!==r?r:"")},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}returns(){return this}overrideTypes(){return this}}},3937:(e,t,s)=>{s.r(t),s.d(t,{Headers:()=>o,Request:()=>a,Response:()=>c,default:()=>i,fetch:()=>n});var r=function(){if("undefined"!==typeof self)return self;if("undefined"!==typeof window)return window;if("undefined"!==typeof s.g)return s.g;throw new Error("unable to locate global object")}();const n=r.fetch,i=r.fetch.bind(r),o=r.Headers,a=r.Request,c=r.Response},8034:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const n=r(s(8416));t.default=class{constructor(e,t){let{headers:s={},schema:r,fetch:n}=t;this.url=e,this.headers=s,this.schema=r,this.fetch=n}select(e){let{head:t=!1,count:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=t?"HEAD":"GET";let i=!1;const o=(null!==e&&void 0!==e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",o),s&&(this.headers.Prefer="count=".concat(s)),new n.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e){let{count:t,defaultToNull:s=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push("count=".concat(t)),s||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>'"'.concat(e,'"'));this.url.searchParams.set("columns",e.join(","))}}return new n.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e){let{onConflict:t,ignoreDuplicates:s=!1,count:r,defaultToNull:i=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o=["resolution=".concat(s?"ignore":"merge","-duplicates")];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),r&&o.push("count=".concat(r)),i||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>'"'.concat(e,'"'));this.url.searchParams.set("columns",e.join(","))}}return new n.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e){let{count:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push("count=".concat(t)),this.headers.Prefer=s.join(","),new n.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete(){let{count:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=[];return e&&t.push("count=".concat(e)),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new n.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}},8416:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const n=r(s(1926));class i extends n.default{eq(e,t){return this.url.searchParams.append(e,"eq.".concat(t)),this}neq(e,t){return this.url.searchParams.append(e,"neq.".concat(t)),this}gt(e,t){return this.url.searchParams.append(e,"gt.".concat(t)),this}gte(e,t){return this.url.searchParams.append(e,"gte.".concat(t)),this}lt(e,t){return this.url.searchParams.append(e,"lt.".concat(t)),this}lte(e,t){return this.url.searchParams.append(e,"lte.".concat(t)),this}like(e,t){return this.url.searchParams.append(e,"like.".concat(t)),this}likeAllOf(e,t){return this.url.searchParams.append(e,"like(all).{".concat(t.join(","),"}")),this}likeAnyOf(e,t){return this.url.searchParams.append(e,"like(any).{".concat(t.join(","),"}")),this}ilike(e,t){return this.url.searchParams.append(e,"ilike.".concat(t)),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,"ilike(all).{".concat(t.join(","),"}")),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,"ilike(any).{".concat(t.join(","),"}")),this}is(e,t){return this.url.searchParams.append(e,"is.".concat(t)),this}in(e,t){const s=Array.from(new Set(t)).map(e=>"string"===typeof e&&new RegExp("[,()]").test(e)?'"'.concat(e,'"'):"".concat(e)).join(",");return this.url.searchParams.append(e,"in.(".concat(s,")")),this}contains(e,t){return"string"===typeof t?this.url.searchParams.append(e,"cs.".concat(t)):Array.isArray(t)?this.url.searchParams.append(e,"cs.{".concat(t.join(","),"}")):this.url.searchParams.append(e,"cs.".concat(JSON.stringify(t))),this}containedBy(e,t){return"string"===typeof t?this.url.searchParams.append(e,"cd.".concat(t)):Array.isArray(t)?this.url.searchParams.append(e,"cd.{".concat(t.join(","),"}")):this.url.searchParams.append(e,"cd.".concat(JSON.stringify(t))),this}rangeGt(e,t){return this.url.searchParams.append(e,"sr.".concat(t)),this}rangeGte(e,t){return this.url.searchParams.append(e,"nxl.".concat(t)),this}rangeLt(e,t){return this.url.searchParams.append(e,"sl.".concat(t)),this}rangeLte(e,t){return this.url.searchParams.append(e,"nxr.".concat(t)),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,"adj.".concat(t)),this}overlaps(e,t){return"string"===typeof t?this.url.searchParams.append(e,"ov.".concat(t)):this.url.searchParams.append(e,"ov.{".concat(t.join(","),"}")),this}textSearch(e,t){let{config:s,type:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n="";"plain"===r?n="pl":"phrase"===r?n="ph":"websearch"===r&&(n="w");const i=void 0===s?"":"(".concat(s,")");return this.url.searchParams.append(e,"".concat(n,"fts").concat(i,".").concat(t)),this}match(e){return Object.entries(e).forEach(e=>{let[t,s]=e;this.url.searchParams.append(t,"eq.".concat(s))}),this}not(e,t,s){return this.url.searchParams.append(e,"not.".concat(t,".").concat(s)),this}or(e){let{foreignTable:t,referencedTable:s=t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=s?"".concat(s,".or"):"or";return this.url.searchParams.append(r,"(".concat(e,")")),this}filter(e,t,s){return this.url.searchParams.append(e,"".concat(t,".").concat(s)),this}}t.default=i},9401:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;const r=s(2072);t.DEFAULT_HEADERS={"X-Client-Info":"postgrest-js/".concat(r.version)}}}]);
//# sourceMappingURL=383.037f6b30.chunk.js.map