// ----------------------------------------
// Pricing & Subscription Constants
// ----------------------------------------

import { PricingTier, SubscriptionTier } from "./types";

export const PRICING_TIERS: PricingTier[] = [
  {
    id: "Free",
    name: "Free",
    price: 0,
    credits: 25,
    features: [
      "25 credits per month",
      "Basic AI generation",
      "Document upload (up to 5MB)",
      "Community support",
      "Basic analytics",
    ],
    stripePriceId: undefined,
  },
  {
    id: "Study Starter",
    name: "Study Starter",
    price: 29,
    credits: 500,
    features: [
      "500 credits per month",
      "Advanced AI generation",
      "Document upload (up to 10MB)",
      "Email support",
      "Detailed analytics",
      "Export functionality",
    ],
    stripePriceId: "price_starter_placeholder",
  },
  {
    id: "Study Pro",
    name: "Study Pro",
    price: 59,
    credits: 1200,
    features: [
      "1,200 credits per month",
      "Premium AI generation",
      "Document upload (up to 25MB)",
      "Priority support",
      "Advanced analytics",
      "Custom study sets",
      "Collaboration features",
    ],
    isPopular: true,
    stripePriceId: "price_pro_placeholder",
  },
  {
    id: "Study Master",
    name: "Study Master",
    price: 119,
    credits: 2500,
    features: [
      "2,500 credits per month",
      "Premium AI generation",
      "Document upload (up to 50MB)",
      "Priority support",
      "Advanced analytics",
      "Custom study sets",
      "Team collaboration",
      "API access",
    ],
    stripePriceId: "price_master_placeholder",
  },
  {
    id: "Study Elite",
    name: "Study Elite",
    price: 239,
    credits: 5500,
    features: [
      "5,500 credits per month",
      "Premium AI generation",
      "Unlimited document upload",
      "Dedicated support",
      "Advanced analytics",
      "Custom study sets",
      "Team collaboration",
      "API access",
      "White-label options",
    ],
    stripePriceId: "price_elite_placeholder",
  },
];

// ----------------------------------------
// Credit System Constants
// ----------------------------------------

export const CREDIT_COSTS = {
  FLASHCARD_GENERATION: 1, // 1 credit per 5 flashcards
  QUIZ_GENERATION: 1, // 1 credit per 5 quiz questions
  FLEX_GENERATION: 1, // 1 credit per 5 flex items
  DOCUMENT_PROCESSING: 0, // Free document processing
} as const;

export const ITEMS_PER_CREDIT = {
  FLASHCARDS: 5,
  QUIZ_QUESTIONS: 5,
  FLEX_ITEMS: 5,
} as const;

// ----------------------------------------
// Subscription Limits
// ----------------------------------------

export const SUBSCRIPTION_LIMITS = {
  Free: {
    maxDocumentSize: 5 * 1024 * 1024, // 5MB
    maxDocuments: 10,
    maxStudySets: 5,
    creditsPerMonth: 25,
  },
  "Study Starter": {
    maxDocumentSize: 10 * 1024 * 1024, // 10MB
    maxDocuments: 50,
    maxStudySets: 25,
    creditsPerMonth: 500,
  },
  "Study Pro": {
    maxDocumentSize: 25 * 1024 * 1024, // 25MB
    maxDocuments: 200,
    maxStudySets: 100,
    creditsPerMonth: 1200,
  },
  "Study Master": {
    maxDocumentSize: 50 * 1024 * 1024, // 50MB
    maxDocuments: 500,
    maxStudySets: 250,
    creditsPerMonth: 2500,
  },
  "Study Elite": {
    maxDocumentSize: 100 * 1024 * 1024, // 100MB (effectively unlimited)
    maxDocuments: 1000,
    maxStudySets: 500,
    creditsPerMonth: 5500,
  },
} as const;

// ----------------------------------------
// Feature Flags
// ----------------------------------------

export const FEATURE_FLAGS = {
  Free: {
    advancedAnalytics: false,
    exportFunctionality: false,
    prioritySupport: false,
    customStudySets: false,
    collaboration: false,
    apiAccess: false,
    whiteLabelOptions: false,
  },
  "Study Starter": {
    advancedAnalytics: true,
    exportFunctionality: true,
    prioritySupport: false,
    customStudySets: false,
    collaboration: false,
    apiAccess: false,
    whiteLabelOptions: false,
  },
  "Study Pro": {
    advancedAnalytics: true,
    exportFunctionality: true,
    prioritySupport: true,
    customStudySets: true,
    collaboration: true,
    apiAccess: false,
    whiteLabelOptions: false,
  },
  "Study Master": {
    advancedAnalytics: true,
    exportFunctionality: true,
    prioritySupport: true,
    customStudySets: true,
    collaboration: true,
    apiAccess: true,
    whiteLabelOptions: false,
  },
  "Study Elite": {
    advancedAnalytics: true,
    exportFunctionality: true,
    prioritySupport: true,
    customStudySets: true,
    collaboration: true,
    apiAccess: true,
    whiteLabelOptions: true,
  },
} as const;

// ----------------------------------------
// Helper Functions
// ----------------------------------------

export const getPricingTier = (
  tier: SubscriptionTier
): PricingTier | undefined => {
  return PRICING_TIERS.find((t) => t.id === tier);
};

export const getSubscriptionLimits = (tier: SubscriptionTier) => {
  return SUBSCRIPTION_LIMITS[tier];
};

export const getFeatureFlags = (tier: SubscriptionTier) => {
  return FEATURE_FLAGS[tier];
};

export const hasFeature = (
  tier: SubscriptionTier,
  feature: keyof typeof FEATURE_FLAGS.Free
): boolean => {
  return FEATURE_FLAGS[tier][feature];
};

export const canUploadDocument = (
  tier: SubscriptionTier,
  fileSize: number
): boolean => {
  const limits = getSubscriptionLimits(tier);
  return fileSize <= limits.maxDocumentSize;
};

export const getRemainingDocuments = (
  tier: SubscriptionTier,
  currentCount: number
): number => {
  const limits = getSubscriptionLimits(tier);
  return Math.max(0, limits.maxDocuments - currentCount);
};

export const getRemainingStudySets = (
  tier: SubscriptionTier,
  currentCount: number
): number => {
  const limits = getSubscriptionLimits(tier);
  return Math.max(0, limits.maxStudySets - currentCount);
};

// ----------------------------------------
// Stripe Configuration
// ----------------------------------------

export const STRIPE_CONFIG = {
  publishableKey: "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY",
  webhookSecret: "STRIPE_WEBHOOK_SECRET",
  successUrl: "FRONTEND_URL" + "/subscription/success",
  cancelUrl: "FRONTEND_URL" + "/subscription/cancel",
} as const;

// ----------------------------------------
// API Endpoints
// ----------------------------------------

export const API_ENDPOINTS = {
  // Authentication
  LOGIN: "/api/auth/login",
  REGISTER: "/api/auth/register",
  LOGOUT: "/api/auth/logout",
  REFRESH: "/api/auth/refresh",

  // User Management
  PROFILE: "/api/user/profile",
  UPDATE_PROFILE: "/api/user/update",

  // Subscription Management
  CREATE_SUBSCRIPTION: "/api/subscription/create",
  CANCEL_SUBSCRIPTION: "/api/subscription/cancel",
  UPDATE_SUBSCRIPTION: "/api/subscription/update",
  GET_SUBSCRIPTION: "/api/subscription/status",

  // Payment Processing
  CREATE_PAYMENT_INTENT: "/api/payment/create-intent",
  CONFIRM_PAYMENT: "/api/payment/confirm",
  WEBHOOK: "/api/payment/webhook",

  // Credit Management
  GET_CREDITS: "/api/credits/balance",
  PURCHASE_CREDITS: "/api/credits/purchase",
  CREDIT_HISTORY: "/api/credits/history",

  // Document Management
  UPLOAD_DOCUMENT: "/api/documents/upload",
  GET_DOCUMENTS: "/api/documents",
  DELETE_DOCUMENT: "/api/documents/delete",

  // Study Set Management
  CREATE_STUDY_SET: "/api/study-sets/create",
  GET_STUDY_SETS: "/api/study-sets",
  DELETE_STUDY_SET: "/api/study-sets/delete",

  // AI Generation
  GENERATE_FLASHCARDS: "/api/ai/generate-flashcards",
  GENERATE_QUIZ: "/api/ai/generate-quiz",
  GENERATE_FLEX: "/api/ai/generate-flex",
} as const;
