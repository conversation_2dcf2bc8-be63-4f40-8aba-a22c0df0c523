import React from 'react';

interface SkipNavigationProps {
  links: Array<{
    href: string;
    label: string;
  }>;
}

export const SkipNavigation: React.FC<SkipNavigationProps> = ({ links }) => {
  return (
    <nav 
      className="sr-only focus-within:not-sr-only"
      aria-label="Skip navigation"
    >
      <div className="fixed top-0 left-0 z-50 bg-primary-600 text-white p-4 rounded-br-lg shadow-lg">
        <ul className="flex flex-col space-y-2">
          {links.map((link, index) => (
            <li key={index}>
              <a
                href={link.href}
                className="block px-3 py-2 text-sm font-medium rounded-md hover:bg-primary-700 focus:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600"
                onClick={(e) => {
                  e.preventDefault();
                  const target = document.querySelector(link.href);
                  if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                    // Focus the target element if it's focusable
                    if (target instanceof HTMLElement) {
                      target.focus();
                    }
                  }
                }}
              >
                {link.label}
              </a>
            </li>
          ))}
        </ul>
      </div>
    </nav>
  );
};
