import React from 'react';
import { motion } from 'framer-motion';
import { BaseDialog } from './BaseDialog';
import { Button } from '../../common/Button';

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'info' | 'warning' | 'danger';
  isLoading?: boolean;
  // Enhanced layout options
  buttonLayout?: 'default' | 'corners'; // 'corners' puts cancel left, confirm right
  additionalContent?: React.ReactNode; // Content to show between message and buttons
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'info',
  isLoading = false,
  buttonLayout = 'default',
  additionalContent
}) => {
  const handleConfirm = () => {
    onConfirm();
  };

  const getIcon = () => {
    switch (variant) {
      case 'warning':
        return (
          <div className="flex-shrink-0 w-10 h-10 mx-auto bg-yellow-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      case 'danger':
        return (
          <div className="flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="flex-shrink-0 w-10 h-10 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const getConfirmButtonVariant = () => {
    switch (variant) {
      case 'danger':
        return 'danger';
      case 'warning':
        return 'primary';
      default:
        return 'primary';
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };

  return (
    <BaseDialog
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
      closeOnOverlayClick={false}
    >
      <motion.div
        className="text-center"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          {getIcon()}
        </motion.div>

        <motion.div className="mt-4" variants={itemVariants}>
          <p className="text-white leading-relaxed whitespace-pre-line">
            {message}
          </p>
        </motion.div>

        {additionalContent && (
          <motion.div className="mt-4" variants={itemVariants}>
            {additionalContent}
          </motion.div>
        )}

        <motion.div
          className={`mt-6 ${
            buttonLayout === 'corners'
              ? 'flex justify-between items-center'
              : 'flex flex-col-reverse sm:flex-row sm:gap-3'
          }`}
          variants={itemVariants}
        >
          <Button
            onClick={onClose}
            variant="secondary"
            className={
              buttonLayout === 'corners'
                ? 'px-6'
                : 'w-full sm:w-auto mt-3 sm:mt-0'
            }
            disabled={isLoading}
          >
            {cancelText}
          </Button>

          <Button
            onClick={handleConfirm}
            variant={getConfirmButtonVariant()}
            className={
              buttonLayout === 'corners'
                ? 'px-6'
                : 'w-full sm:w-auto'
            }
            isLoading={isLoading}
            autoFocus
          >
            {confirmText}
          </Button>
        </motion.div>
      </motion.div>
    </BaseDialog>
  );
};
