import { Request, Response } from 'express';
import { quizService } from '../services/quizService';
import { QuestionType } from '../../../shared/types';

export const getQuizQuestions = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { studySetId } = req.params;

    const questions = await quizService.getQuestionsByStudySet(studySetId, userId);

    res.json({
      success: true,
      data: questions
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get quiz questions'
    });
  }
};

export const createQuizQuestion = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { studySetId } = req.params;
    const {
      question_text,
      question_type,
      options,
      correct_answers,
      explanation,
      difficulty_level
    } = req.body;

    // Validate required fields
    if (!question_text || !question_type || !correct_answers) {
      return res.status(400).json({
        success: false,
        error: 'Question text, type, and correct answers are required'
      });
    }

    // Validate question type
    const validTypes: QuestionType[] = ['multiple_choice', 'select_all', 'true_false', 'short_answer'];
    if (!validTypes.includes(question_type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid question type'
      });
    }

    // Validate options for multiple choice and select all questions
    if ((question_type === 'multiple_choice' || question_type === 'select_all') && (!options || !Array.isArray(options) || options.length < 2)) {
      return res.status(400).json({
        success: false,
        error: 'Multiple choice and select all questions require at least 2 options'
      });
    }

    // Validate correct answers
    if (!Array.isArray(correct_answers) || correct_answers.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'At least one correct answer is required'
      });
    }

    const questionData = {
      question_text: question_text.trim(),
      question_type,
      options: options || null,
      correct_answers,
      explanation: explanation?.trim() || null,
      difficulty_level: difficulty_level || 3,
      is_ai_generated: false
    };

    const question = await quizService.createQuizQuestion(studySetId, userId, questionData);

    res.status(201).json({
      success: true,
      data: question,
      message: 'Quiz question created successfully'
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create quiz question'
    });
  }
};

export const updateQuizQuestion = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates.id;
    delete updates.study_set_id;
    delete updates.times_attempted;
    delete updates.times_correct;
    delete updates.created_at;

    const question = await quizService.updateQuizQuestion(id, userId, updates);

    res.json({
      success: true,
      data: question,
      message: 'Quiz question updated successfully'
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update quiz question'
    });
  }
};

export const deleteQuizQuestion = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { id } = req.params;

    await quizService.deleteQuizQuestion(id, userId);

    res.json({
      success: true,
      message: 'Quiz question deleted successfully'
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to delete quiz question'
    });
  }
};
