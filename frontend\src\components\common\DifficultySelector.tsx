import React from "react";
import { DifficultyLevel } from "../../../../shared/types";

interface DifficultySelectorProps {
  value: DifficultyLevel;
  onChange: (difficulty: DifficultyLevel) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
}

const difficultyOptions = [
  {
    value: DifficultyLevel.EASY,
    label: "Easy",
    description: "Basic facts and definitions",
  },
  {
    value: DifficultyLevel.MEDIUM,
    label: "Medium",
    description: "Moderate understanding required",
  },
  {
    value: DifficultyLevel.HARD,
    label: "Hard",
    description: "Deep analysis and critical thinking",
  },
  {
    value: DifficultyLevel.COLLEGE,
    label: "College",
    description: "Undergraduate level complexity",
  },
  {
    value: DifficultyLevel.GRADUATE,
    label: "Graduate",
    description: "Advanced graduate study",
  },
  {
    value: DifficultyLevel.PHD,
    label: "PhD",
    description: "Research-level expertise",
  },
];

const getDifficultyColor = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case DifficultyLevel.EASY:
      return "bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50";
    case DifficultyLevel.MEDIUM:
      return "bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";
    case DifficultyLevel.HARD:
      return "bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50";
    case DifficultyLevel.COLLEGE:
      return "bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50";
    case DifficultyLevel.GRADUATE:
      return "bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50";
    case DifficultyLevel.PHD:
      return "bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50";
    default:
      return "bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";
  }
};

const getSelectedDifficultyColor = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case DifficultyLevel.EASY:
      return "bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20";
    case DifficultyLevel.MEDIUM:
      return "bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";
    case DifficultyLevel.HARD:
      return "bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20";
    case DifficultyLevel.COLLEGE:
      return "bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20";
    case DifficultyLevel.GRADUATE:
      return "bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20";
    case DifficultyLevel.PHD:
      return "bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20";
    default:
      return "bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";
  }
};

export const DifficultySelector: React.FC<DifficultySelectorProps> = ({
  value,
  onChange,
  className = "",
  disabled = false,
  label = "Difficulty Level",
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-text-primary">
        {label}
      </label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {difficultyOptions.map((option) => {
          const isSelected = value === option.value;
          const colorClasses = isSelected
            ? getSelectedDifficultyColor(option.value)
            : getDifficultyColor(option.value);

          return (
            <button
              key={option.value}
              type="button"
              onClick={() => !disabled && onChange(option.value)}
              disabled={disabled}
              className={`
                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${colorClasses}
                ${
                  disabled
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer hover:scale-105"
                }
                ${
                  isSelected
                    ? "ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary"
                    : ""
                }
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `}
              title={option.description}
              aria-pressed={isSelected}
            >
              <div className="text-center">
                <div className="font-semibold">{option.label}</div>
                <div
                  className={`text-xs mt-1 ${
                    isSelected ? "text-white/90" : "text-text-secondary"
                  }`}
                >
                  {option.description}
                </div>
              </div>
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>
      <p className="text-xs text-text-muted">
        Select the appropriate difficulty level for your flashcards. This
        affects the complexity of questions and answers generated.
      </p>
    </div>
  );
};

export default DifficultySelector;
