{"version": 3, "file": "static/js/843.0acb241d.chunk.js", "mappings": "oJAqBO,MAAMA,GAAmBC,E,QAAAA,IAAuBC,IAAG,CACxDC,UAAW,GACXC,kBAAmB,IAAIC,IACvBC,WAAW,EACXC,eAAgB,CAAC,EAEjBC,eAAgBC,UACdP,EAAI,CAAEI,WAAW,IAEjB,IACE,MAAMI,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,iBAAkB,CAC7CC,QAAS,CACPC,cAAc,UAADC,OAAYP,MAI7B,IAAKG,EAASK,GACZ,MAAM,IAAIC,MAAM,6BAGlB,MAAMC,QAAeP,EAASQ,OAE9B,IAAID,EAAOE,QAGT,MAAM,IAAIH,MAAMC,EAAOG,OAFvBrB,EAAI,CAAEC,UAAWiB,EAAOI,KAAMlB,WAAW,GAI7C,CAAE,MAAOiB,GAGP,MAFAE,QAAQF,MAAM,yBAA0BA,GACxCrB,EAAI,CAAEI,WAAW,IACXiB,CACR,GAGFG,eAAgBjB,UACd,MAAMkB,EAAW,IAAIC,SACrBD,EAASE,OAAO,WAAYC,GAE5B,IACE,MAAMpB,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,wBAAyB,CACpDiB,OAAQ,OACRhB,QAAS,CACPC,cAAc,UAADC,OAAYP,IAE3BsB,KAAML,IAGR,IAAKd,EAASK,GAAI,CAChB,MAAMe,QAAoBpB,EAASQ,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,gBACvC,CAEA,MAAMH,QAAeP,EAASQ,OAE9B,GAAID,EAAOE,QAOT,OALApB,EAAKgC,IAAK,CACR/B,UAAW,CAACiB,EAAOI,QAASU,EAAM/B,WAClCI,gBAAc4B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAM3B,gBAAc,IAAE,CAACuB,EAAKM,MAAO,SAGnDhB,EAAOI,KAEd,MAAM,IAAIL,MAAMC,EAAOG,MAE3B,CAAE,MAAOA,GAEP,MADAE,QAAQF,MAAM,yBAA0BA,GAClCA,CACR,GAGFc,eAAgB5B,UACd,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAADG,OAAmBqB,GAAM,CACnDP,OAAQ,SACRhB,QAAS,CACPC,cAAc,UAADC,OAAYP,MAI7B,IAAKG,EAASK,GAAI,CAChB,MAAMe,QAAoBpB,EAASQ,OACnC,MAAM,IAAIF,MAAMc,EAAYV,OAAS,gBACvC,CAGArB,EAAKgC,IAAK,CACR/B,UAAW+B,EAAM/B,UAAUoC,OAAQC,GAAQA,EAAIF,KAAOA,GACtDlC,kBAAmB,IAAIC,IACrB,IAAI6B,EAAM9B,mBAAmBmC,OAAQE,GAAUA,IAAUH,MAG/D,CAAE,MAAOf,GAEP,MADAE,QAAQF,MAAM,yBAA0BA,GAClCA,CACR,GAGFmB,gBAAiBjC,UACf,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,2BAADG,OACC0B,mBAAmBC,IAC9C,CACE7B,QAAS,CACPC,cAAc,UAADC,OAAYP,MAK/B,IAAKG,EAASK,GACZ,MAAM,IAAIC,MAAM,iBAGlB,MAAMC,QAAeP,EAASQ,OAC9B,OAAOD,EAAOE,QAAUF,EAAOI,KAAO,EACxC,CAAE,MAAOD,GAEP,OADAE,QAAQF,MAAM,0BAA2BA,GAClC,EACT,GAGFsB,YAAapC,UACX,IACE,MAAMC,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAADG,OAAmBqB,GAAM,CACnDvB,QAAS,CACPC,cAAc,UAADC,OAAYP,MAI7B,IAAKG,EAASK,GACZ,OAAO,KAGT,MAAME,QAAeP,EAASQ,OAC9B,OAAOD,EAAOE,QAAUF,EAAOI,KAAO,IACxC,CAAE,MAAOD,GAEP,OADAE,QAAQF,MAAM,sBAAuBA,GAC9B,IACT,GAGFuB,wBAA0BR,IACxBpC,EAAKgC,IACH,MAAMa,EAAe,IAAI1C,IAAI6B,EAAM9B,mBAMnC,OALI2C,EAAaC,IAAIV,GACnBS,EAAaE,OAAOX,GAEpBS,EAAaG,IAAIZ,GAEZ,CAAElC,kBAAmB2C,MAIhCI,eAAgBA,KACdjD,EAAI,CAAEE,kBAAmB,IAAIC,OAG/B+C,UAAWA,KACTlD,EAAKgC,IAAK,CACR9B,kBAAmB,IAAIC,IAAI6B,EAAM/B,UAAUkD,IAAKb,GAAQA,EAAIF,SAIhEgB,kBAAmBA,CAACC,EAAkBC,KACpCtD,EAAKgC,IAAK,CACR3B,gBAAc4B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAM3B,gBAAc,IAAE,CAACgD,GAAWC,U,2GC3LtD,MAAMC,EAA2BA,KACtC,MAAOC,EAAaC,IAAkBC,EAAAA,EAAAA,WAAS,IACxCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAmB,KACrD,eAAElC,EAAc,kBAAE4B,IAAsBtD,EAAAA,EAAAA,KAExC+D,GAASC,EAAAA,EAAAA,aAAYvD,UACzBkD,GAAe,GACfG,EAAgB,IAEhB,MAAMG,EAAmB,GAEzB,IAAK,MAAMnC,KAAQoC,EACjB,IAEE,GAAIpC,EAAKqC,KAAO,SAAkB,CAChCF,EAAOG,KAAK,GAADnD,OAAIa,EAAKM,KAAI,mCACxB,QACF,CAUA,IAPqB,CACnB,kBACA,0EACA,aACA,6EAGgBiC,SAASvC,EAAKwC,MAAO,CACrCL,EAAOG,KAAK,GAADnD,OAAIa,EAAKM,KAAI,0EACxB,QACF,CAGAkB,EAAkBxB,EAAKM,KAAM,SAGvBV,EAAeI,GAGrBwB,EAAkBxB,EAAKM,KAAM,IAC/B,CAAE,MAAOb,GACP0C,EAAOG,KAAK,GAADnD,OAAIa,EAAKM,KAAI,MAAAnB,OAAKM,aAAiBJ,MAAQI,EAAMgD,QAAU,iBACxE,CAGFT,EAAgBG,GAChBN,GAAe,IACd,CAACjC,EAAgB4B,KAEd,aAAEkB,EAAY,cAAEC,EAAa,aAAEC,IAAiBC,EAAAA,EAAAA,IAAY,CAChEZ,SACAa,OAAQ,CACN,kBAAmB,CAAC,QACpB,0EAA2E,CAAC,SAC5E,aAAc,CAAC,QACf,4EAA6E,CAAC,UAEhFC,UAAU,EACVC,SAAU,GACVC,SAAUrB,IAGZ,OACEsB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAA7C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACMqC,KAAc,IAClBS,UAAS,6GAAAhE,OAELyD,EACE,uCACA,kEAAiE,gBAAAzD,OAEnEyC,EAAc,gCAAkC,GAAE,cACpDwB,SAAA,EAEFC,EAAAA,EAAAA,KAAA,SAAAhD,EAAAA,EAAAA,GAAA,GAAWsC,OAEXO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OACEF,UAAU,kCACVG,OAAO,eACPC,KAAK,OACLC,QAAQ,YAAWJ,UAEnBC,EAAAA,EAAAA,KAAA,QACEI,EAAE,yLACFC,YAAa,EACbC,cAAc,QACdC,eAAe,YAIlBhB,GACCS,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mBAAkBC,SAAC,4BAEhCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,gBAAeC,SAAA,CAAC,6BACA,KAC3BC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+BAA8BC,SAAC,eAEjDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAC,4DAQjDxB,IACCsB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAC,wBAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAMlBpB,EAAa8B,OAAS,IACrBX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gCAA+BC,SAAC,oBAC9CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iCAAgCC,SAC3CrB,EAAaR,IAAI,CAAC9B,EAAOqE,KACxBZ,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAgB,UAAG3D,IAAVqE,a,2CCpHhB,MAAMC,EAA4CC,IAAmB,IAAlB,SAAEC,GAAUD,EACpE,MAAM,kBAAE1F,EAAiB,wBAAE0C,EAAuB,eAAET,IAAmBrC,EAAAA,EAAAA,MACjE,QAAEgG,EAAO,MAAEC,IAAUC,EAAAA,EAAAA,MACpBC,EAAYC,IAAiBxC,EAAAA,EAAAA,WAAS,GAEvCyC,EAAajG,EAAkB4C,IAAI+C,EAASzD,IAwDlD,OACE0C,EAAAA,EAAAA,MAAA,OACEC,UAAS,oGAAAhE,OAELoF,EACE,uCACA,wCAAuC,YAG7CC,QAASA,IAAMxD,EAAwBiD,EAASzD,IAAI4C,SAAA,EAGpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,WAAUC,UAxBbqB,EAwB2BR,EAASS,UAvBjB,CACpCC,IAAK,eACLC,KAAM,eACNC,IAAK,eACLC,KAAM,gBAEKL,IAAa,mBAkBpBvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kCAAkC4B,MAAOd,EAASe,SAAS5B,SACtEa,EAASe,YAEZ9B,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CACjCa,EAASS,UAAUO,cAAc,WAhDtBC,KACtB,GAAc,IAAVA,EAAa,MAAO,UACxB,MAEMC,EAAIC,KAAKC,MAAMD,KAAKE,IAAIJ,GAASE,KAAKE,IAFlC,OAGV,OAAOC,YAAYL,EAAQE,KAAKI,IAHtB,KAG6BL,IAAIM,QAAQ,IAAM,IAF3C,CAAC,QAAS,KAAM,KAAM,MAEiCN,IA2CrBO,CAAezB,EAAS0B,qBAMpEtC,EAAAA,EAAAA,KAAA,OACEF,UAAS,wFAAAhE,OAELoF,EACE,oCACA,kBAAiB,gBAErBnB,SAEDmB,IACClB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qBAAqBI,KAAK,eAAeC,QAAQ,YAAWJ,UACzEC,EAAAA,EAAAA,KAAA,QAAMuC,SAAS,UAAUnC,EAAE,qHAAqHoC,SAAS,oBAO/J5B,EAAS6B,eACTzC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAC,uBAK/Ba,EAAS8B,mBACR1C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,CAAC,mCACba,EAAS8B,uBAMtC7C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,CAAC,aAlF5B4C,EAmFO/B,EAASgC,YAlF3B,IAAIC,KAAKF,GAAYG,mBAAmB,QAAS,CACtDC,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,iBAiFRnD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA6BqB,QAAUiC,GAAMA,EAAEC,kBAAkBtD,UAC9EC,EAAAA,EAAAA,KAACsD,EAAAA,EAAM,CACLnC,QA3Ha7F,UASnB,SAR4BuF,EAAQ,CAClCa,MAAO,kBACPtC,QAAQ,oCAADtD,OAAsC8E,EAASe,SAAQ,oCAC9D4B,YAAa,SACbC,WAAY,SACZC,QAAS,WAGX,CAEAxC,GAAc,GACd,UACQ/D,EAAe0D,EAASzD,GAChC,CAAE,MAAOf,GACPE,QAAQF,MAAM,gBAAiBA,SACzB0E,EAAM,CACVY,MAAO,eACPtC,QAAS,+CACTqE,QAAS,SAEb,CAAC,QACCxC,GAAc,EAChB,CAd0B,GAmHpBwC,QAAQ,SACRzE,KAAK,KACL7D,UAAW6F,EAAWjB,SACvB,gBA7Fa4C,MAUCvB,GC1CjBsC,GAA4CC,EAAAA,EAAAA,MAAKhD,IAA6B,IAA5B,MAAEF,EAAK,MAAEmD,EAAK,KAAEvH,GAAMsE,EAC5E,MAAM,UAAE3F,GAAcqB,EAChBuE,EAAW5F,EAAUyF,GAE3B,OACET,EAAAA,EAAAA,KAAA,OAAK4D,MAAOA,EAAM7D,UAChBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,KAACU,EAAY,CACXE,SAAUA,UAOpB8C,EAAaG,YAAc,eAEpB,MAAMC,GAAkEH,EAAAA,EAAAA,MAAKI,IAI7E,IAJ8E,UACnF/I,EAAS,OACTgJ,EAAS,IAAG,WACZC,EAAa,KACdF,EACC,MAAMG,GAAWC,EAAAA,EAAAA,SAAQ,MACvBnJ,cACE,CAACA,IAEL,OAAyB,IAArBA,EAAUwF,QAEVX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qBAAoBC,SAAC,+BACpCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAC,kFAQvC/E,EAAUwF,QAAU,IAEpBR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB/E,EAAUkD,IAAK0C,IACdZ,EAAAA,EAAAA,KAACU,EAAY,CAEXE,SAAUA,GADLA,EAASzD,QAStB6C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,UAChEC,EAAAA,EAAAA,KAACoE,EAAAA,GAAI,CACHJ,OAAQA,EACRK,MAAM,OACNC,UAAWtJ,EAAUwF,OACrB+D,SAAUN,EACVC,SAAUA,EACVM,cAAe,EAAEzE,SAEhB2D,QAMTI,EAAwBD,YAAc,0BC9E/B,MAAMY,GAAyBd,EAAAA,EAAAA,MAAK,KACzC,MAAM,UACJ3I,EAAS,kBACTC,EAAiB,UACjBE,EAAS,eACTE,EAAc,gBACdkC,EAAe,eACfS,EAAc,UACdC,EAAS,eACTf,IACErC,EAAAA,EAAAA,MAEE,QAAEgG,EAAO,MAAEC,IAAUC,EAAAA,EAAAA,MACpB2D,EAAaC,IAAkBlG,EAAAA,EAAAA,UAAS,KACxCmG,EAAeC,IAAoBpG,EAAAA,EAAAA,UAAoC,OACvEqG,EAAaC,IAAkBtG,EAAAA,EAAAA,WAAS,IAE/CuG,EAAAA,EAAAA,WAAU,KACR3J,KACC,CAACA,IAEJ,MAiDM4J,EAAmBL,GAAiB5J,EACpCkK,EAAejK,EAAkB+D,KAAO,EAE9C,OAAI7D,GAAkC,IAArBH,EAAUwF,QAEvBR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAC,4BAMnCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,SAAQC,UACrBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAACmF,EAAAA,EAAK,CACJC,YAAY,sBACZC,MAAOX,EACPY,SAAUX,KAEZ3E,EAAAA,EAAAA,KAACsD,EAAAA,EAAM,CACLnC,QAxES7F,UACnB,GAAIoJ,EAAYa,OAAO/E,OAAS,EAC9BqE,EAAiB,UADnB,CAKAE,GAAe,GACf,IACE,MAAMS,QAAgBjI,EAAgBmH,EAAYa,QAClDV,EAAiBW,EACnB,CAAE,MAAOpJ,GACPE,QAAQF,MAAM,gBAAiBA,EACjC,CAAC,QACC2I,GAAe,EACjB,CAVA,GAqEU5J,UAAW2J,EACXlF,SAAU8E,EAAYa,OAAO/E,OAAS,EAAET,SACzC,WAGA6E,IACC5E,EAAAA,EAAAA,KAACsD,EAAAA,EAAM,CACLnC,QA/DMsE,KAClBd,EAAe,IACfE,EAAiB,OA8DLpB,QAAQ,YAAW1D,SACpB,eAQNmF,IACCrF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAACsD,EAAAA,EAAM,CACLnC,QAASlD,EACTwF,QAAQ,YACRzE,KAAK,KAAIe,SACV,gBAGDF,EAAAA,EAAAA,MAACyD,EAAAA,EAAM,CACLnC,QAASnD,EACTyF,QAAQ,YACRzE,KAAK,KAAIe,SAAA,CACV,UACS9E,EAAkB+D,KAAK,QAEjCgB,EAAAA,EAAAA,KAACsD,EAAAA,EAAM,CACLnC,QArFa7F,UACvB,GAA+B,IAA3BL,EAAkB+D,KAAY,OAUlC,SAR4B6B,EAAQ,CAClCa,MAAO,mBACPtC,QAAQ,mCAADtD,OAAqCb,EAAkB+D,KAAI,+CAClEuE,YAAa,SACbC,WAAY,SACZC,QAAS,WAKX,IACE,MAAMiC,EAAiBC,MAAMC,KAAK3K,GAAmBiD,IAAIf,GAAMD,EAAeC,UACxE0I,QAAQC,IAAIJ,GAClB1H,GACF,CAAE,MAAO5B,GACPE,QAAQF,MAAM,qBAAsBA,SAC9B0E,EAAM,CACVY,MAAO,eACPtC,QAAS,yDACTqE,QAAS,SAEb,GA8DUA,QAAQ,SACRzE,KAAK,KAAIe,SACV,0BAQN6E,IACC/E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,CAAC,SAC9B6E,EAAcpE,OAAO,0BAAwBkE,EAAY,QAKpE1E,EAAAA,EAAAA,KAAC8D,EAAuB,CACtB9I,UAAWiK,EACXjB,OAAQ,WCxJH+B,EAA0BA,KAEnC/F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8CAA6CC,UAC1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gCAA+BC,SAAC,eAC9CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAAC,wFAMpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wCAAuCC,SAAC,sBACtDC,EAAAA,EAAAA,KAAC1B,EAAc,QAIjBuB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wCAAuCC,SAAC,oBACtDC,EAAAA,EAAAA,KAACyE,EAAY,W", "sources": ["stores/documentStore.ts", "components/documents/DocumentUpload.tsx", "components/documents/DocumentCard.tsx", "components/documents/VirtualizedDocumentList.tsx", "components/documents/DocumentList.tsx", "pages/DocumentsPage.tsx"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { DocumentMetadata, DocumentWithContent } from \"../shared/types\";\r\n\r\ninterface DocumentState {\r\n  documents: DocumentMetadata[];\r\n  selectedDocuments: Set<string>;\r\n  isLoading: boolean;\r\n  uploadProgress: { [key: string]: number };\r\n\r\n  // Actions\r\n  fetchDocuments: () => Promise<void>;\r\n  uploadDocument: (file: File) => Promise<DocumentMetadata>;\r\n  deleteDocument: (id: string) => Promise<void>;\r\n  searchDocuments: (query: string) => Promise<DocumentMetadata[]>;\r\n  getDocument: (id: string) => Promise<DocumentWithContent | null>;\r\n  toggleDocumentSelection: (id: string) => void;\r\n  clearSelection: () => void;\r\n  selectAll: () => void;\r\n  setUploadProgress: (fileName: string, progress: number) => void;\r\n}\r\n\r\nexport const useDocumentStore = create<DocumentState>((set) => ({\r\n  documents: [],\r\n  selectedDocuments: new Set(),\r\n  isLoading: false,\r\n  uploadProgress: {},\r\n\r\n  fetchDocuments: async () => {\r\n    set({ isLoading: true });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(\"/api/documents\", {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch documents\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        set({ documents: result.data, isLoading: false });\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Fetch documents error:\", error);\r\n      set({ isLoading: false });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  uploadDocument: async (file: File) => {\r\n    const formData = new FormData();\r\n    formData.append(\"document\", file);\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(\"/api/documents/upload\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Upload failed\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        // Add new document to the list\r\n        set((state) => ({\r\n          documents: [result.data, ...state.documents],\r\n          uploadProgress: { ...state.uploadProgress, [file.name]: 100 },\r\n        }));\r\n\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Upload document error:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  deleteDocument: async (id: string) => {\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(`/api/documents/${id}`, {\r\n        method: \"DELETE\",\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Delete failed\");\r\n      }\r\n\r\n      // Remove document from the list\r\n      set((state) => ({\r\n        documents: state.documents.filter((doc) => doc.id !== id),\r\n        selectedDocuments: new Set(\r\n          [...state.selectedDocuments].filter((docId) => docId !== id)\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Delete document error:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  searchDocuments: async (query: string) => {\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(\r\n        `/api/documents/search?q=${encodeURIComponent(query)}`,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Search failed\");\r\n      }\r\n\r\n      const result = await response.json();\r\n      return result.success ? result.data : [];\r\n    } catch (error) {\r\n      console.error(\"Search documents error:\", error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  getDocument: async (id: string) => {\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(`/api/documents/${id}`, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        return null;\r\n      }\r\n\r\n      const result = await response.json();\r\n      return result.success ? result.data : null;\r\n    } catch (error) {\r\n      console.error(\"Get document error:\", error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  toggleDocumentSelection: (id: string) => {\r\n    set((state) => {\r\n      const newSelection = new Set(state.selectedDocuments);\r\n      if (newSelection.has(id)) {\r\n        newSelection.delete(id);\r\n      } else {\r\n        newSelection.add(id);\r\n      }\r\n      return { selectedDocuments: newSelection };\r\n    });\r\n  },\r\n\r\n  clearSelection: () => {\r\n    set({ selectedDocuments: new Set() });\r\n  },\r\n\r\n  selectAll: () => {\r\n    set((state) => ({\r\n      selectedDocuments: new Set(state.documents.map((doc) => doc.id)),\r\n    }));\r\n  },\r\n\r\n  setUploadProgress: (fileName: string, progress: number) => {\r\n    set((state) => ({\r\n      uploadProgress: { ...state.uploadProgress, [fileName]: progress },\r\n    }));\r\n  },\r\n}));\r\n", "import React, { useState, useCallback } from 'react';\r\nimport { useDropzone } from 'react-dropzone';\r\nimport { useDocumentStore } from '../../stores/documentStore';\r\n\r\nexport const DocumentUpload: React.FC = () => {\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [uploadErrors, setUploadErrors] = useState<string[]>([]);\r\n  const { uploadDocument, setUploadProgress } = useDocumentStore();\r\n\r\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\r\n    setIsUploading(true);\r\n    setUploadErrors([]);\r\n\r\n    const errors: string[] = [];\r\n\r\n    for (const file of acceptedFiles) {\r\n      try {\r\n        // Validate file size (50MB limit)\r\n        if (file.size > 50 * 1024 * 1024) {\r\n          errors.push(`${file.name}: File size exceeds 50MB limit`);\r\n          continue;\r\n        }\r\n\r\n        // Validate file type\r\n        const allowedTypes = [\r\n          'application/pdf',\r\n          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n          'text/plain',\r\n          'application/vnd.openxmlformats-officedocument.presentationml.presentation'\r\n        ];\r\n\r\n        if (!allowedTypes.includes(file.type)) {\r\n          errors.push(`${file.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);\r\n          continue;\r\n        }\r\n\r\n        // Set initial progress\r\n        setUploadProgress(file.name, 0);\r\n\r\n        // Upload file\r\n        await uploadDocument(file);\r\n        \r\n        // Set completion progress\r\n        setUploadProgress(file.name, 100);\r\n      } catch (error) {\r\n        errors.push(`${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n      }\r\n    }\r\n\r\n    setUploadErrors(errors);\r\n    setIsUploading(false);\r\n  }, [uploadDocument, setUploadProgress]);\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      'application/pdf': ['.pdf'],\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\r\n      'text/plain': ['.txt'],\r\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx']\r\n    },\r\n    multiple: true,\r\n    maxFiles: 10,\r\n    disabled: isUploading\r\n  });\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div\r\n        {...getRootProps()}\r\n        className={`\r\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\r\n          ${isDragActive \r\n            ? 'border-primary-500 bg-primary-500/10' \r\n            : 'border-gray-600 hover:border-primary-500 hover:bg-primary-500/5'\r\n          }\r\n          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}\r\n        `}\r\n      >\r\n        <input {...getInputProps()} />\r\n        \r\n        <div className=\"space-y-2\">\r\n          <svg\r\n            className=\"mx-auto h-12 w-12 text-gray-400\"\r\n            stroke=\"currentColor\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 48 48\"\r\n          >\r\n            <path\r\n              d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\r\n              strokeWidth={2}\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n            />\r\n          </svg>\r\n          \r\n          {isDragActive ? (\r\n            <p className=\"text-primary-400\">Drop the files here...</p>\r\n          ) : (\r\n            <div>\r\n              <p className=\"text-gray-300\">\r\n                Drag & drop files here, or{' '}\r\n                <span className=\"text-primary-500 font-medium\">browse</span>\r\n              </p>\r\n              <p className=\"text-sm text-gray-500 mt-1\">\r\n                Supports PDF, DOCX, TXT, PPTX (max 50MB each)\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {isUploading && (\r\n        <div className=\"bg-background-secondary rounded-lg p-4\">\r\n          <p className=\"text-sm text-gray-300 mb-2\">Uploading files...</p>\r\n          <div className=\"space-y-2\">\r\n            {/* Progress bars would be shown here */}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {uploadErrors.length > 0 && (\r\n        <div className=\"bg-red-900/20 border border-red-700 rounded-lg p-4\">\r\n          <h4 className=\"text-red-400 font-medium mb-2\">Upload Errors:</h4>\r\n          <ul className=\"text-sm text-red-300 space-y-1\">\r\n            {uploadErrors.map((error, index) => (\r\n              <li key={index}>• {error}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from 'react';\r\nimport { DocumentMetadata } from '../../../../shared/types';\r\nimport { useDocumentStore } from '../../stores/documentStore';\r\nimport { Button } from '../common/Button';\r\nimport { useDialog } from '../../contexts/DialogContext';\r\n\r\ninterface DocumentCardProps {\r\n  document: DocumentMetadata;\r\n}\r\n\r\nexport const DocumentCard: React.FC<DocumentCardProps> = ({ document }) => {\r\n  const { selectedDocuments, toggleDocumentSelection, deleteDocument } = useDocumentStore();\r\n  const { confirm, alert } = useDialog();\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n\r\n  const isSelected = selectedDocuments.has(document.id);\r\n\r\n  const handleDelete = async () => {\r\n    const confirmDelete = await confirm({\r\n      title: 'Delete Document',\r\n      message: `Are you sure you want to delete \"${document.filename}\"? This action cannot be undone.`,\r\n      confirmText: 'Delete',\r\n      cancelText: 'Cancel',\r\n      variant: 'danger'\r\n    });\r\n\r\n    if (!confirmDelete) return;\r\n\r\n    setIsDeleting(true);\r\n    try {\r\n      await deleteDocument(document.id);\r\n    } catch (error) {\r\n      console.error('Delete error:', error);\r\n      await alert({\r\n        title: 'Delete Error',\r\n        message: 'Failed to delete document. Please try again.',\r\n        variant: 'error'\r\n      });\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  const getFileIcon = (fileType: string) => {\r\n    const icons: Record<string, string> = {\r\n      pdf: '📄',\r\n      docx: '📝',\r\n      txt: '📃',\r\n      pptx: '📊'\r\n    };\r\n    return icons[fileType] || '📄';\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`\r\n        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer\r\n        ${isSelected \r\n          ? 'border-primary-500 bg-primary-500/10' \r\n          : 'border-gray-700 hover:border-gray-600'\r\n        }\r\n      `}\r\n      onClick={() => toggleDocumentSelection(document.id)}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"flex items-start justify-between mb-3\">\r\n        <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\r\n          <span className=\"text-2xl\">{getFileIcon(document.file_type)}</span>\r\n          <div className=\"min-w-0 flex-1\">\r\n            <h3 className=\"text-white font-medium truncate\" title={document.filename}>\r\n              {document.filename}\r\n            </h3>\r\n            <p className=\"text-sm text-gray-400\">\r\n              {document.file_type.toUpperCase()} • {formatFileSize(document.file_size)}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Selection Indicator */}\r\n        <div\r\n          className={`\r\n            w-5 h-5 rounded border-2 flex items-center justify-center\r\n            ${isSelected \r\n              ? 'bg-primary-500 border-primary-500' \r\n              : 'border-gray-500'\r\n            }\r\n          `}\r\n        >\r\n          {isSelected && (\r\n            <svg className=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Processing Status */}\r\n      {!document.is_processed && (\r\n        <div className=\"mb-3\">\r\n          <div className=\"flex items-center space-x-2 text-yellow-400\">\r\n            <div className=\"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"></div>\r\n            <span className=\"text-sm\">Processing...</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {document.processing_error && (\r\n        <div className=\"mb-3\">\r\n          <div className=\"text-red-400 text-sm\">\r\n            ⚠️ Processing failed: {document.processing_error}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Upload Date */}\r\n      <div className=\"text-xs text-gray-500 mb-3\">\r\n        Uploaded {formatDate(document.uploaded_at)}\r\n      </div>\r\n\r\n      {/* Actions */}\r\n      <div className=\"flex justify-end space-x-2\" onClick={(e) => e.stopPropagation()}>\r\n        <Button\r\n          onClick={handleDelete}\r\n          variant=\"danger\"\r\n          size=\"sm\"\r\n          isLoading={isDeleting}\r\n        >\r\n          Delete\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { memo, useMemo } from 'react';\r\nimport { FixedSizeList as List } from 'react-window';\r\nimport { DocumentMetadata } from '../../../../shared/types';\r\nimport { DocumentCard } from './DocumentCard';\r\n\r\ninterface VirtualizedDocumentListProps {\r\n  documents: DocumentMetadata[];\r\n  height?: number;\r\n  itemHeight?: number;\r\n}\r\n\r\ninterface DocumentItemProps {\r\n  index: number;\r\n  style: React.CSSProperties;\r\n  data: {\r\n    documents: DocumentMetadata[];\r\n  };\r\n}\r\n\r\nconst DocumentItem: React.FC<DocumentItemProps> = memo(({ index, style, data }) => {\r\n  const { documents } = data;\r\n  const document = documents[index];\r\n\r\n  return (\r\n    <div style={style}>\r\n      <div className=\"px-2 py-1\">\r\n        <DocumentCard\r\n          document={document}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nDocumentItem.displayName = 'DocumentItem';\r\n\r\nexport const VirtualizedDocumentList: React.FC<VirtualizedDocumentListProps> = memo(({\r\n  documents,\r\n  height = 400,\r\n  itemHeight = 120\r\n}) => {\r\n  const itemData = useMemo(() => ({\r\n    documents\r\n  }), [documents]);\r\n\r\n  if (documents.length === 0) {\r\n    return (\r\n      <div className=\"text-center py-12\">\r\n        <div className=\"text-gray-400 mb-4\">No documents uploaded yet</div>\r\n        <p className=\"text-sm text-gray-500\">\r\n          Upload your first document to get started with AI-powered study materials.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // For small lists, use regular rendering to avoid virtualization overhead\r\n  if (documents.length <= 10) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        {documents.map((document) => (\r\n          <DocumentCard\r\n            key={document.id}\r\n            document={document}\r\n          />\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"border border-gray-600 rounded-lg overflow-hidden\">\r\n      <List\r\n        height={height}\r\n        width=\"100%\"\r\n        itemCount={documents.length}\r\n        itemSize={itemHeight}\r\n        itemData={itemData}\r\n        overscanCount={5}\r\n      >\r\n        {DocumentItem}\r\n      </List>\r\n    </div>\r\n  );\r\n});\r\n\r\nVirtualizedDocumentList.displayName = 'VirtualizedDocumentList';\r\n", "import React, { useEffect, useState, memo } from 'react';\r\nimport { DocumentMetadata } from '../../../../shared/types';\r\nimport { useDocumentStore } from '../../stores/documentStore';\r\nimport { Input } from '../common/Input';\r\nimport { Button } from '../common/Button';\r\nimport { VirtualizedDocumentList } from './VirtualizedDocumentList';\r\nimport { useDialog } from '../../contexts/DialogContext';\r\n\r\nexport const DocumentList: React.FC = memo(() => {\r\n  const {\r\n    documents,\r\n    selectedDocuments,\r\n    isLoading,\r\n    fetchDocuments,\r\n    searchDocuments,\r\n    clearSelection,\r\n    selectAll,\r\n    deleteDocument\r\n  } = useDocumentStore();\r\n\r\n  const { confirm, alert } = useDialog();\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [searchResults, setSearchResults] = useState<DocumentMetadata[] | null>(null);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchDocuments();\r\n  }, [fetchDocuments]);\r\n\r\n  const handleSearch = async () => {\r\n    if (searchQuery.trim().length < 2) {\r\n      setSearchResults(null);\r\n      return;\r\n    }\r\n\r\n    setIsSearching(true);\r\n    try {\r\n      const results = await searchDocuments(searchQuery.trim());\r\n      setSearchResults(results);\r\n    } catch (error) {\r\n      console.error('Search error:', error);\r\n    } finally {\r\n      setIsSearching(false);\r\n    }\r\n  };\r\n\r\n  const clearSearch = () => {\r\n    setSearchQuery('');\r\n    setSearchResults(null);\r\n  };\r\n\r\n  const handleBulkDelete = async () => {\r\n    if (selectedDocuments.size === 0) return;\r\n\r\n    const confirmDelete = await confirm({\r\n      title: 'Delete Documents',\r\n      message: `Are you sure you want to delete ${selectedDocuments.size} document(s)? This action cannot be undone.`,\r\n      confirmText: 'Delete',\r\n      cancelText: 'Cancel',\r\n      variant: 'danger'\r\n    });\r\n\r\n    if (!confirmDelete) return;\r\n\r\n    try {\r\n      const deletePromises = Array.from(selectedDocuments).map(id => deleteDocument(id));\r\n      await Promise.all(deletePromises);\r\n      clearSelection();\r\n    } catch (error) {\r\n      console.error('Bulk delete error:', error);\r\n      await alert({\r\n        title: 'Delete Error',\r\n        message: 'Some documents could not be deleted. Please try again.',\r\n        variant: 'error'\r\n      });\r\n    }\r\n  };\r\n\r\n  const displayDocuments = searchResults || documents;\r\n  const hasSelection = selectedDocuments.size > 0;\r\n\r\n  if (isLoading && documents.length === 0) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-12\">\r\n        <div className=\"text-gray-400\">Loading documents...</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Search and Actions */}\r\n      <div className=\"flex flex-col sm:flex-row gap-4\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex gap-2\">\r\n            <Input\r\n              placeholder=\"Search documents...\"\r\n              value={searchQuery}\r\n              onChange={setSearchQuery}\r\n            />\r\n            <Button\r\n              onClick={handleSearch}\r\n              isLoading={isSearching}\r\n              disabled={searchQuery.trim().length < 2}\r\n            >\r\n              Search\r\n            </Button>\r\n            {searchResults && (\r\n              <Button\r\n                onClick={clearSearch}\r\n                variant=\"secondary\"\r\n              >\r\n                Clear\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bulk Actions */}\r\n        {hasSelection && (\r\n          <div className=\"flex gap-2\">\r\n            <Button\r\n              onClick={selectAll}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n            >\r\n              Select All\r\n            </Button>\r\n            <Button\r\n              onClick={clearSelection}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n            >\r\n              Clear ({selectedDocuments.size})\r\n            </Button>\r\n            <Button\r\n              onClick={handleBulkDelete}\r\n              variant=\"danger\"\r\n              size=\"sm\"\r\n            >\r\n              Delete Selected\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Search Results Info */}\r\n      {searchResults && (\r\n        <div className=\"text-sm text-gray-400\">\r\n          Found {searchResults.length} document(s) matching \"{searchQuery}\"\r\n        </div>\r\n      )}\r\n\r\n      {/* Document Grid */}\r\n      <VirtualizedDocumentList\r\n        documents={displayDocuments}\r\n        height={600}\r\n      />\r\n    </div>\r\n  );\r\n});\r\n", "import React from 'react';\r\nimport { DocumentUpload } from '../components/documents/DocumentUpload';\r\nimport { DocumentList } from '../components/documents/DocumentList';\r\n\r\nexport const DocumentsPage: React.FC = () => {\r\n  return (\r\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n      <div className=\"space-y-8\">\r\n        {/* Header */}\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-white\">Documents</h1>\r\n          <p className=\"mt-2 text-gray-400\">\r\n            Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX\r\n          </p>\r\n        </div>\r\n\r\n        {/* Upload Section */}\r\n        <div className=\"bg-background-secondary rounded-lg p-6\">\r\n          <h2 className=\"text-xl font-semibold text-white mb-4\">Upload Documents</h2>\r\n          <DocumentUpload />\r\n        </div>\r\n\r\n        {/* Document List */}\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold text-white mb-4\">Your Documents</h2>\r\n          <DocumentList />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": ["useDocumentStore", "create", "set", "documents", "selectedDocuments", "Set", "isLoading", "uploadProgress", "fetchDocuments", "async", "token", "localStorage", "getItem", "response", "fetch", "headers", "Authorization", "concat", "ok", "Error", "result", "json", "success", "error", "data", "console", "uploadDocument", "formData", "FormData", "append", "file", "method", "body", "errorResult", "state", "_objectSpread", "name", "deleteDocument", "id", "filter", "doc", "docId", "searchDocuments", "encodeURIComponent", "query", "getDocument", "toggleDocumentSelection", "newSelection", "has", "delete", "add", "clearSelection", "selectAll", "map", "setUploadProgress", "fileName", "progress", "DocumentUpload", "isUploading", "setIsUploading", "useState", "uploadErrors", "setUploadErrors", "onDrop", "useCallback", "errors", "acceptedFiles", "size", "push", "includes", "type", "message", "getRootProps", "getInputProps", "isDragActive", "useDropzone", "accept", "multiple", "maxFiles", "disabled", "_jsxs", "className", "children", "_jsx", "stroke", "fill", "viewBox", "d", "strokeWidth", "strokeLinecap", "strokeLinejoin", "length", "index", "DocumentCard", "_ref", "document", "confirm", "alert", "useDialog", "isDeleting", "setIsDeleting", "isSelected", "onClick", "fileType", "file_type", "pdf", "docx", "txt", "pptx", "title", "filename", "toUpperCase", "bytes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatFileSize", "file_size", "fillRule", "clipRule", "is_processed", "processing_error", "dateString", "uploaded_at", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "e", "stopPropagation", "<PERSON><PERSON>", "confirmText", "cancelText", "variant", "DocumentItem", "memo", "style", "displayName", "VirtualizedDocumentList", "_ref2", "height", "itemHeight", "itemData", "useMemo", "List", "width", "itemCount", "itemSize", "overscanCount", "DocumentList", "searchQuery", "setSearch<PERSON>uery", "searchResults", "setSearchResults", "isSearching", "setIsSearching", "useEffect", "displayDocuments", "hasSelection", "Input", "placeholder", "value", "onChange", "trim", "results", "clearSearch", "deletePromises", "Array", "from", "Promise", "all", "DocumentsPage"], "sourceRoot": ""}