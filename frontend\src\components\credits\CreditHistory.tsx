import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Hi<PERSON>lock,
  HiPlus,
  HiMinus,
  HiChevronDown,
  HiDownload
} from 'react-icons/hi';
import { CreditTransaction } from '../../../../shared/types';
import { Button } from '../common/Button';

interface CreditHistoryProps {
  transactions: CreditTransaction[];
  isLoading: boolean;
  onLoadMore: () => void;
}

export const CreditHistory: React.FC<CreditHistoryProps> = ({
  transactions,
  isLoading,
  onLoadMore
}) => {
  const [filter, setFilter] = useState<'all' | 'used' | 'purchased'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'amount'>('date');

  const getTransactionIcon = (creditsUsed: number) => {
    return creditsUsed > 0 ? (
      <HiMinus className="w-4 h-4 text-red-400" />
    ) : (
      <HiPlus className="w-4 h-4 text-green-400" />
    );
  };

  const getTransactionColor = (creditsUsed: number) => {
    return creditsUsed > 0 ? 'text-red-400' : 'text-green-400';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const filteredTransactions = transactions.filter(transaction => {
    if (filter === 'used') return transaction.credits_used > 0;
    if (filter === 'purchased') return transaction.credits_used < 0;
    return true;
  });

  const sortedTransactions = [...filteredTransactions].sort((a, b) => {
    if (sortBy === 'date') {
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    } else {
      return Math.abs(b.credits_used) - Math.abs(a.credits_used);
    }
  });

  const exportTransactions = () => {
    const csvContent = [
      ['Date', 'Type', 'Credits', 'Operation', 'Description'].join(','),
      ...sortedTransactions.map(transaction => [
        new Date(transaction.created_at).toLocaleDateString(),
        transaction.credits_used > 0 ? 'Used' : 'Purchased',
        Math.abs(transaction.credits_used),
        transaction.operation_type,
        `"${transaction.description}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `credit-history-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <div>
          <h3 className="text-lg font-semibold text-white">Transaction History</h3>
          <p className="text-gray-400 text-sm">
            {filteredTransactions.length} of {transactions.length} transactions
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Filter Dropdown */}
          <div className="relative">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as 'all' | 'used' | 'purchased')}
              className="appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Transactions</option>
              <option value="used">Credits Used</option>
              <option value="purchased">Credits Purchased</option>
            </select>
            <HiChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Sort Dropdown */}
          <div className="relative">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'date' | 'amount')}
              className="appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="date">Sort by Date</option>
              <option value="amount">Sort by Amount</option>
            </select>
            <HiChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Export Button */}
          <Button
            onClick={exportTransactions}
            variant="secondary"
            size="sm"
            disabled={transactions.length === 0}
          >
            <HiDownload className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Transaction List */}
      <div className="space-y-3">
        {isLoading && transactions.length === 0 ? (
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-background-tertiary rounded-lg p-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-600 rounded w-1/3 mb-2"></div>
                      <div className="h-3 bg-gray-600 rounded w-1/2"></div>
                    </div>
                    <div className="h-6 bg-gray-600 rounded w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : sortedTransactions.length === 0 ? (
          <div className="text-center py-12">
            <HiClock className="w-12 h-12 text-gray-500 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-400 mb-2">No Transactions Found</h4>
            <p className="text-gray-500">
              {filter === 'all' 
                ? "You haven't made any credit transactions yet."
                : `No ${filter} transactions found.`
              }
            </p>
          </div>
        ) : (
          sortedTransactions.map((transaction, index) => {
            const { date, time } = formatDate(transaction.created_at);
            const isCredit = transaction.credits_used < 0;
            
            return (
              <motion.div
                key={transaction.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="bg-background-tertiary rounded-lg p-4 border border-border-secondary hover:border-border-primary transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-full ${isCredit ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
                      {getTransactionIcon(transaction.credits_used)}
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-white">
                        {transaction.operation_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </h4>
                      <p className="text-gray-400 text-sm">{transaction.description}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-gray-500 text-xs">{date}</span>
                        <span className="text-gray-600">•</span>
                        <span className="text-gray-500 text-xs">{time}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <span className={`font-semibold ${getTransactionColor(transaction.credits_used)}`}>
                      {isCredit ? '+' : '-'}{Math.abs(transaction.credits_used)} credits
                    </span>
                    {transaction.study_set_id && (
                      <p className="text-gray-500 text-xs mt-1">Study Set</p>
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })
        )}
      </div>

      {/* Load More Button */}
      {transactions.length > 0 && transactions.length % 50 === 0 && (
        <div className="mt-6 text-center">
          <Button
            onClick={onLoadMore}
            variant="secondary"
            isLoading={isLoading}
            disabled={isLoading}
          >
            Load More Transactions
          </Button>
        </div>
      )}
    </div>
  );
};
