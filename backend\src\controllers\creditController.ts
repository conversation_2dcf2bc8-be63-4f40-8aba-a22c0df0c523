import { Request, Response } from "express";
import { creditService } from "../services/creditService";
import { stripeService } from "../services/stripeService";

export const getCreditBalance = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const credits = await creditService.getUserCredits(userId);

    res.json({
      success: true,
      data: { credits },
    });
  } catch (error) {
    console.error("Get credit balance error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get credit balance",
    });
  }
};

export const getCreditHistory = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const result = await creditService.getCreditHistory(userId, limit, offset);

    res.json({
      success: true,
      data: result.transactions,
      total: result.total,
      page: Math.floor(offset / limit) + 1,
      limit,
      hasMore: offset + limit < result.total,
    });
  } catch (error) {
    console.error("Get credit history error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get credit history",
    });
  }
};

export const getCreditStats = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const days = parseInt(req.query.days as string) || 30;

    const stats = await creditService.getCreditStats(userId, days);

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Get credit stats error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get credit statistics",
    });
  }
};

export const getOperationCosts = async (_req: Request, res: Response) => {
  try {
    const costs = await creditService.getAllOperationCosts();

    res.json({
      success: true,
      data: costs,
    });
  } catch (error) {
    console.error("Get operation costs error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get operation costs",
    });
  }
};

export const checkCreditSufficiency = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { operationType } = req.params;

    const result = await creditService.checkSufficientCredits(
      userId,
      operationType
    );

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Check credit sufficiency error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to check credit sufficiency",
    });
  }
};

export const purchaseCredits = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;
    const { amount, credits, price, email, name, discountCode } = req.body;

    // Validate required fields
    if (!amount || !credits || !price || !email) {
      return res.status(400).json({
        success: false,
        error: "Amount, credits, price, and email are required",
      });
    }

    // Validate data types and ranges
    if (typeof amount !== "number" || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: "Amount must be a positive number",
      });
    }

    if (typeof credits !== "number" || credits <= 0) {
      return res.status(400).json({
        success: false,
        error: "Credits must be a positive number",
      });
    }

    if (typeof price !== "number" || price <= 0) {
      return res.status(400).json({
        success: false,
        error: "Price must be a positive number",
      });
    }

    if (typeof email !== "string" || !email.includes("@")) {
      return res.status(400).json({
        success: false,
        error: "Valid email address is required",
      });
    }

    // Create Stripe customer and payment intent
    const customerId = await stripeService.createOrGetCustomer(
      userId,
      email,
      name
    );
    const paymentIntent = await stripeService.createCreditPurchaseIntent(
      customerId,
      price,
      credits,
      discountCode
    );

    res.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      },
    });
  } catch (error) {
    console.error("Purchase credits error:", error);
    res.status(500).json({
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to initiate credit purchase",
    });
  }
};
