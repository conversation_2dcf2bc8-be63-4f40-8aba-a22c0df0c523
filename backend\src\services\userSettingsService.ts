import { supabase } from './supabaseService';
import { DifficultyLevel } from '../../../shared/types';

export interface UserSettings {
  id: string;
  user_id: string;
  skip_delete_confirmations: boolean;
  shuffle_flashcards: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserSettingsUpdate {
  skip_delete_confirmations?: boolean;
  shuffle_flashcards?: boolean;
}

// User Preferences interfaces
export interface UserPreferences {
  id: string;
  user_id: string;
  theme: 'dark' | 'light';
  language: string;
  study_reminders: boolean;
  auto_save: boolean;
  default_study_mode: 'flashcards' | 'quiz';
  session_duration: number;
  difficulty_level: DifficultyLevel;
  created_at: string;
  updated_at: string;
}

export interface UserPreferencesUpdate {
  theme?: 'dark' | 'light';
  language?: string;
  study_reminders?: boolean;
  auto_save?: boolean;
  default_study_mode?: 'flashcards' | 'quiz';
  session_duration?: number;
  difficulty_level?: DifficultyLevel;
}

class UserSettingsService {
  async getUserSettings(userId: string): Promise<UserSettings> {
    // First try to get existing settings
    const { data: existingSettings, error: selectError } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (selectError && selectError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, other errors are actual problems
      throw new Error(`Failed to fetch user settings: ${selectError.message}`);
    }

    if (existingSettings) {
      return existingSettings;
    }

    // If no settings exist, create default settings
    return this.createDefaultSettings(userId);
  }

  async createDefaultSettings(userId: string): Promise<UserSettings> {
    const defaultSettings = {
      user_id: userId,
      skip_delete_confirmations: false,
      shuffle_flashcards: false
    };

    const { data, error } = await supabase
      .from('user_settings')
      .insert(defaultSettings)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create default user settings: ${error.message}`);
    }

    return data;
  }

  async updateUserSettings(userId: string, updates: UserSettingsUpdate): Promise<UserSettings> {
    // Validate updates
    if (Object.keys(updates).length === 0) {
      throw new Error('No updates provided');
    }

    // Validate boolean fields
    if (updates.skip_delete_confirmations !== undefined && typeof updates.skip_delete_confirmations !== 'boolean') {
      throw new Error('skip_delete_confirmations must be a boolean');
    }

    if (updates.shuffle_flashcards !== undefined && typeof updates.shuffle_flashcards !== 'boolean') {
      throw new Error('shuffle_flashcards must be a boolean');
    }

    // First ensure settings exist
    await this.getUserSettings(userId);

    // Update settings
    const { data, error } = await supabase
      .from('user_settings')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update user settings: ${error.message}`);
    }

    return data;
  }

  async deleteUserSettings(userId: string): Promise<void> {
    const { error } = await supabase
      .from('user_settings')
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to delete user settings: ${error.message}`);
    }
  }
}

export const userSettingsService = new UserSettingsService();

// User Preferences Service
class UserPreferencesService {
  async getUserPreferences(userId: string): Promise<UserPreferences> {
    // First try to get existing preferences
    const { data: existingPreferences, error: selectError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (selectError && selectError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, other errors are actual problems
      throw new Error(`Failed to fetch user preferences: ${selectError.message}`);
    }

    if (existingPreferences) {
      return existingPreferences;
    }

    // If no preferences exist, create default preferences
    return this.createDefaultPreferences(userId);
  }

  async createDefaultPreferences(userId: string): Promise<UserPreferences> {
    const defaultPreferences = {
      user_id: userId,
      theme: 'dark' as const,
      language: 'en',
      study_reminders: true,
      auto_save: true,
      default_study_mode: 'flashcards' as const,
      session_duration: 30,
      difficulty_level: DifficultyLevel.MEDIUM
    };

    const { data, error } = await supabase
      .from('user_preferences')
      .insert(defaultPreferences)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create default user preferences: ${error.message}`);
    }

    return data;
  }

  async updateUserPreferences(userId: string, updates: UserPreferencesUpdate): Promise<UserPreferences> {
    // Validate updates
    if (Object.keys(updates).length === 0) {
      throw new Error('No updates provided');
    }

    // Validate theme
    if (updates.theme !== undefined && !['dark', 'light'].includes(updates.theme)) {
      throw new Error('theme must be either "dark" or "light"');
    }

    // Validate default_study_mode
    if (updates.default_study_mode !== undefined && !['flashcards', 'quiz'].includes(updates.default_study_mode)) {
      throw new Error('default_study_mode must be either "flashcards" or "quiz"');
    }

    // Validate session_duration
    if (updates.session_duration !== undefined && (typeof updates.session_duration !== 'number' || updates.session_duration <= 0)) {
      throw new Error('session_duration must be a positive number');
    }

    // Validate difficulty_level
    if (updates.difficulty_level !== undefined && !Object.values(DifficultyLevel).includes(updates.difficulty_level)) {
      throw new Error('Invalid difficulty level');
    }

    // Validate boolean fields
    if (updates.study_reminders !== undefined && typeof updates.study_reminders !== 'boolean') {
      throw new Error('study_reminders must be a boolean');
    }

    if (updates.auto_save !== undefined && typeof updates.auto_save !== 'boolean') {
      throw new Error('auto_save must be a boolean');
    }

    // First ensure preferences exist
    await this.getUserPreferences(userId);

    // Update preferences
    const { data, error } = await supabase
      .from('user_preferences')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update user preferences: ${error.message}`);
    }

    return data;
  }

  async deleteUserPreferences(userId: string): Promise<void> {
    const { error } = await supabase
      .from('user_preferences')
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to delete user preferences: ${error.message}`);
    }
  }
}

export const userPreferencesService = new UserPreferencesService();
