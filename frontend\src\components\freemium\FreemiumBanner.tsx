import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, Hi<PERSON>rrowR<PERSON>, HiGift } from "react-icons/hi";
import { Button } from "../common/Button";
import { SUBSCRIPTION_LIMITS, PRICING_TIERS } from "../../../../shared/constants";

interface FreemiumBannerProps {
  currentCredits: number;
  onUpgradeClick: () => void;
  className?: string;
}

export const FreemiumBanner: React.FC<FreemiumBannerProps> = ({
  currentCredits,
  onUpgradeClick,
  className = "",
}) => {
  const freemiumLimit = SUBSCRIPTION_LIMITS.Free.creditsPerMonth;
  const usedCredits = freemiumLimit - currentCredits;
  const usagePercentage = (usedCredits / freemiumLimit) * 100;
  const starterTier = PRICING_TIERS.find((tier) => tier.id === "Study Starter");

  const getStatusColor = () => {
    if (usagePercentage >= 90) return "text-red-400";
    if (usagePercentage >= 70) return "text-yellow-400";
    return "text-green-400";
  };

  const getProgressColor = () => {
    if (usagePercentage >= 90) return "bg-red-500";
    if (usagePercentage >= 70) return "bg-yellow-500";
    return "bg-green-500";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30 rounded-lg p-4 ${className}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <HiGift className="w-5 h-5 text-purple-400" />
            </div>
          </div>

          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className="text-sm font-semibold text-white">Free Plan</h3>
              <span className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-full">
                {currentCredits} credits left
              </span>
            </div>

            <div className="mt-1">
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
                    style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                  />
                </div>
                <span className={`text-xs font-medium ${getStatusColor()}`}>
                  {usedCredits}/{freemiumLimit}
                </span>
              </div>
            </div>

            <p className="text-xs text-gray-400 mt-1">
              {currentCredits > 0
                ? `${
                    currentCredits * 5
                  } flashcards/quizzes remaining this month`
                : "Monthly limit reached. Upgrade to continue studying!"}
            </p>
          </div>
        </div>

        <div className="flex-shrink-0">
          <Button
            onClick={onUpgradeClick}
            variant="primary"
            size="sm"
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
          >
            <HiSparkles className="w-4 h-4 mr-1" />
            Upgrade
            <HiArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </div>

      {/* Upgrade incentive */}
      {currentCredits <= 5 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          className="mt-3 pt-3 border-t border-purple-500/20"
        >
          <div className="flex items-center justify-between text-sm">
            <div>
              <p className="text-purple-300 font-medium">
                🚀 Ready to supercharge your studies?
              </p>
              <p className="text-gray-400 text-xs">
                Get {starterTier?.credits} credits/month for just $
                {starterTier?.price}/month
              </p>
            </div>
            <div className="text-right">
              <div className="text-green-400 font-semibold text-xs">
                {starterTier &&
                  `${((starterTier.credits / starterTier.price) * 100).toFixed(
                    0
                  )}% more value!`}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};
