# Phase 10: AI Generation Frontend
**Priority**: CRITICAL - Core AI-powered study material creation
**Dependencies**: Phase 1 (Foundation), Phase 4 (Auth Components), Phase 6 (Credit System), Phase 7 (AI Backend), Phase 9 (Document Management)
**Estimated Time**: 4-5 hours

## Overview
Create React components for AI-powered flashcard and quiz generation with document selection, custom prompts, and credit management.

## Tasks

### 10.1 AI Generation Store (Zustand)
**File**: `frontend/src/stores/aiStore.ts`

```typescript
import { create } from 'zustand';
import { StudySet, FlashcardData, QuizQuestionData } from '../../../shared/types';

interface AIGenerationState {
  isGenerating: boolean;
  generationProgress: string;
  lastGenerated: {
    studySet?: StudySet;
    content?: FlashcardData[] | QuizQuestionData[];
    type?: 'flashcards' | 'quiz';
  } | null;
  
  // Actions
  generateFlashcards: (params: {
    documentIds: string[];
    name: string;
    count: number;
    customPrompt?: string;
  }) => Promise<{ studySet: StudySet; flashcards: FlashcardData[]; creditsRemaining: number }>;
  
  generateQuiz: (params: {
    documentIds: string[];
    name: string;
    count: number;
    customPrompt?: string;
  }) => Promise<{ studySet: StudySet; questions: QuizQuestionData[]; creditsRemaining: number }>;
  
  clearLastGenerated: () => void;
}

export const useAIStore = create<AIGenerationState>((set, get) => ({
  isGenerating: false,
  generationProgress: '',
  lastGenerated: null,

  generateFlashcards: async (params) => {
    set({ isGenerating: true, generationProgress: 'Preparing documents...' });
    
    try {
      const token = localStorage.getItem('auth_token');
      
      set({ generationProgress: 'Generating flashcards with AI...' });
      
      const response = await fetch('/api/ai/generate-flashcards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Generation failed');
      }

      const result = await response.json();
      
      if (result.success) {
        set({
          lastGenerated: {
            studySet: result.data.studySet,
            content: result.data.flashcards,
            type: 'flashcards'
          },
          isGenerating: false,
          generationProgress: ''
        });
        
        return {
          studySet: result.data.studySet,
          flashcards: result.data.flashcards,
          creditsRemaining: result.data.creditsRemaining
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: '' });
      throw error;
    }
  },

  generateQuiz: async (params) => {
    set({ isGenerating: true, generationProgress: 'Preparing documents...' });
    
    try {
      const token = localStorage.getItem('auth_token');
      
      set({ generationProgress: 'Generating quiz questions with AI...' });
      
      const response = await fetch('/api/ai/generate-quiz', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Generation failed');
      }

      const result = await response.json();
      
      if (result.success) {
        set({
          lastGenerated: {
            studySet: result.data.studySet,
            content: result.data.questions,
            type: 'quiz'
          },
          isGenerating: false,
          generationProgress: ''
        });
        
        return {
          studySet: result.data.studySet,
          questions: result.data.questions,
          creditsRemaining: result.data.creditsRemaining
        };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      set({ isGenerating: false, generationProgress: '' });
      throw error;
    }
  },

  clearLastGenerated: () => {
    set({ lastGenerated: null });
  }
}));
```

### 10.2 Document Selector Component
**File**: `frontend/src/components/ai/DocumentSelector.tsx`

```typescript
import React, { useEffect, useState } from 'react';
import { useDocumentStore } from '../../stores/documentStore';
import { DocumentMetadata } from '../../../../shared/types';

interface DocumentSelectorProps {
  selectedDocuments: string[];
  onSelectionChange: (documentIds: string[]) => void;
  maxSelection?: number;
}

export const DocumentSelector: React.FC<DocumentSelectorProps> = ({
  selectedDocuments,
  onSelectionChange,
  maxSelection = 5
}) => {
  const { documents, fetchDocuments, isLoading } = useDocumentStore();
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (documents.length === 0) {
      fetchDocuments();
    }
  }, [documents.length, fetchDocuments]);

  const filteredDocuments = documents.filter(doc => 
    doc.is_processed && 
    doc.filename.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDocumentToggle = (documentId: string) => {
    const isSelected = selectedDocuments.includes(documentId);
    
    if (isSelected) {
      onSelectionChange(selectedDocuments.filter(id => id !== documentId));
    } else if (selectedDocuments.length < maxSelection) {
      onSelectionChange([...selectedDocuments, documentId]);
    }
  };

  const getSelectedDocuments = () => {
    return documents.filter(doc => selectedDocuments.includes(doc.id));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-400">Loading documents...</div>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-400 mb-4">No documents found</div>
        <p className="text-sm text-gray-500">
          Upload some documents first to generate study materials.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <div>
        <input
          type="text"
          placeholder="Search documents..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
        />
      </div>

      {/* Selection Summary */}
      {selectedDocuments.length > 0 && (
        <div className="bg-primary-500/10 border border-primary-500/30 rounded-lg p-3">
          <div className="text-sm text-primary-400 mb-2">
            Selected {selectedDocuments.length} of {maxSelection} documents:
          </div>
          <div className="space-y-1">
            {getSelectedDocuments().map(doc => (
              <div key={doc.id} className="text-sm text-gray-300 flex items-center justify-between">
                <span className="truncate">{doc.filename}</span>
                <button
                  onClick={() => handleDocumentToggle(doc.id)}
                  className="text-red-400 hover:text-red-300 ml-2"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Document List */}
      <div className="max-h-64 overflow-y-auto space-y-2">
        {filteredDocuments.map((document) => {
          const isSelected = selectedDocuments.includes(document.id);
          const canSelect = !isSelected && selectedDocuments.length < maxSelection;
          
          return (
            <div
              key={document.id}
              className={`
                p-3 rounded-lg border cursor-pointer transition-all
                ${isSelected 
                  ? 'bg-primary-500/20 border-primary-500' 
                  : canSelect
                    ? 'bg-background-secondary border-gray-600 hover:border-gray-500'
                    : 'bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed'
                }
              `}
              onClick={() => canSelect || isSelected ? handleDocumentToggle(document.id) : null}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">
                      {document.file_type === 'pdf' ? '📄' : 
                       document.file_type === 'docx' ? '📝' : 
                       document.file_type === 'txt' ? '📃' : '📊'}
                    </span>
                    <div className="min-w-0 flex-1">
                      <p className="text-white font-medium truncate">{document.filename}</p>
                      <p className="text-sm text-gray-400">
                        {document.file_type.toUpperCase()} • {Math.round(document.file_size / 1024)} KB
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${isSelected 
                    ? 'bg-primary-500 border-primary-500' 
                    : 'border-gray-500'
                  }
                `}>
                  {isSelected && (
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredDocuments.length === 0 && searchQuery && (
        <div className="text-center py-4 text-gray-400">
          No documents match your search.
        </div>
      )}
    </div>
  );
};
```

### 10.3 AI Generation Form Component
**File**: `frontend/src/components/ai/AIGenerationForm.tsx`

```typescript
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAIStore } from '../../stores/aiStore';
import { useAuthStore } from '../../stores/authStore';
import { DocumentSelector } from './DocumentSelector';
import { Button } from '../common/Button';
import { Input } from '../common/Input';

interface AIGenerationFormProps {
  type: 'flashcards' | 'quiz';
}

export const AIGenerationForm: React.FC<AIGenerationFormProps> = ({ type }) => {
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [studySetName, setStudySetName] = useState('');
  const [itemCount, setItemCount] = useState(10);
  const [customPrompt, setCustomPrompt] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { generateFlashcards, generateQuiz, isGenerating, generationProgress } = useAIStore();
  const { user, updateUser } = useAuthStore();
  const navigate = useNavigate();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (selectedDocuments.length === 0) {
      newErrors.documents = 'Please select at least one document';
    }

    if (!studySetName.trim()) {
      newErrors.name = 'Study set name is required';
    }

    if (itemCount < 1 || itemCount > 50) {
      newErrors.count = 'Item count must be between 1 and 50';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    // Check credits (1 credit = 5 items)
    const requiredCredits = Math.ceil(itemCount / 5);
    if (!user || user.credits_remaining < requiredCredits) {
      alert(`You need ${requiredCredits} credits to generate ${itemCount} items, but you only have ${user.credits_remaining} credits remaining.`);
      return;
    }

    try {
      const params = {
        documentIds: selectedDocuments,
        name: studySetName.trim(),
        count: itemCount,
        customPrompt: customPrompt.trim() || undefined
      };

      let result;
      if (type === 'flashcards') {
        result = await generateFlashcards(params);
      } else {
        result = await generateQuiz(params);
      }

      // Update user credits
      updateUser({ credits_remaining: result.creditsRemaining });

      // Navigate to the created study set
      navigate(`/study-sets/${result.studySet.id}`);
    } catch (error) {
      console.error('Generation error:', error);
      alert(error.message || 'Failed to generate study materials. Please try again.');
    }
  };

  // Calculate credit cost based on new 1:5 ratio (1 credit = 5 items)
  const creditCost = Math.ceil(itemCount / 5);
  const hasEnoughCredits = user && user.credits_remaining >= creditCost;

  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2">
            Generate {type === 'flashcards' ? 'Flashcards' : 'Quiz Questions'}
          </h2>
          <p className="text-gray-400">
            Create AI-powered study materials from your documents
          </p>
        </div>

        {/* Credit Info */}
        <div className="bg-background-secondary rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-300">
                Cost: <span className="font-medium text-primary-400">{creditCost} credit{creditCost !== 1 ? 's' : ''}</span>
                <span className="text-gray-500 ml-1">({itemCount} items)</span>
              </p>
              <p className="text-sm text-gray-400">
                Your balance: {user?.credits_remaining || 0} credits
              </p>
            </div>
            {!hasEnoughCredits && (
              <Button
                onClick={() => navigate('/credits')}
                variant="secondary"
                size="sm"
              >
                Buy Credits
              </Button>
            )}
          </div>
        </div>

        {/* Document Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Select Documents *
          </label>
          <div className="bg-background-secondary rounded-lg p-4">
            <DocumentSelector
              selectedDocuments={selectedDocuments}
              onSelectionChange={setSelectedDocuments}
              maxSelection={5}
            />
          </div>
          {errors.documents && (
            <p className="mt-1 text-sm text-red-500">{errors.documents}</p>
          )}
        </div>

        {/* Study Set Name */}
        <Input
          label="Study Set Name"
          value={studySetName}
          onChange={setStudySetName}
          placeholder={`My ${type === 'flashcards' ? 'Flashcards' : 'Quiz'}`}
          error={errors.name}
          required
        />

        {/* Item Count */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Number of {type === 'flashcards' ? 'Flashcards' : 'Questions'}
          </label>
          <input
            type="number"
            min="1"
            max="50"
            value={itemCount}
            onChange={(e) => setItemCount(parseInt(e.target.value) || 10)}
            className="w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
          {errors.count && (
            <p className="mt-1 text-sm text-red-500">{errors.count}</p>
          )}
        </div>

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Custom Instructions (Optional)
          </label>
          <textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder="Add specific instructions for the AI (e.g., focus on key concepts, include examples, etc.)"
            rows={3}
            className="w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* Generation Progress */}
        {isGenerating && (
          <div className="bg-primary-500/10 border border-primary-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"></div>
              <span className="text-primary-400">{generationProgress}</span>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          isLoading={isGenerating}
          disabled={!hasEnoughCredits}
          className="w-full"
          size="lg"
        >
          {isGenerating 
            ? 'Generating...' 
            : `Generate ${type === 'flashcards' ? 'Flashcards' : 'Quiz'} (${creditCost} credit${creditCost !== 1 ? 's' : ''})`
          }
        </Button>

        {!hasEnoughCredits && (
          <p className="text-center text-sm text-red-400">
            Insufficient credits. Please purchase more credits to continue.
          </p>
        )}
      </form>
    </div>
  );
};
```

### 10.4 AI Generation Pages
**File**: `frontend/src/pages/GenerateFlashcardsPage.tsx`

```typescript
import React from 'react';
import { AIGenerationForm } from '../components/ai/AIGenerationForm';

export const GenerateFlashcardsPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <AIGenerationForm type="flashcards" />
    </div>
  );
};
```

**File**: `frontend/src/pages/GenerateQuizPage.tsx`

```typescript
import React from 'react';
import { AIGenerationForm } from '../components/ai/AIGenerationForm';

export const GenerateQuizPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <AIGenerationForm type="quiz" />
    </div>
  );
};
```

## Acceptance Criteria
- [ ] Document selection with search and filtering
- [ ] Form validation for all required fields
- [ ] Credit balance checking before generation
- [ ] Real-time generation progress feedback
- [ ] Custom prompt support for AI instructions
- [ ] Item count validation (1-50 range)
- [ ] Error handling for generation failures
- [ ] Navigation to created study sets
- [ ] Credit deduction and balance updates
- [ ] Responsive design for all screen sizes

## Next Phase Dependencies
- Phase 11 (Study Set Management Frontend) requires navigation to created study sets
- Phase 12 (Study Interfaces) requires the generated content
- Phase 13 (Credit Management Frontend) requires credit balance integration
