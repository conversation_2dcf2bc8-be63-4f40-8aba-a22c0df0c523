import pdf from "pdf-parse";
import mammoth from "mammoth";
import { createClient } from "@supabase/supabase-js";

// Create a service role client for storage operations (bypasses RLS)
const supabaseServiceRole = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

export interface ProcessingResult {
  text: string;
  pageCount?: number;
}

export interface PageContent {
  pageNumber: number;
  text: string;
}

export class DocumentProcessor {
  async processFile(file: Express.Multer.File): Promise<ProcessingResult> {
    switch (file.mimetype) {
      case "application/pdf":
        return this.processPDF(file.buffer);
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        return this.processDocx(file.buffer);
      case "text/plain":
        return this.processText(file.buffer);
      case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        return this.processPPTX(file.buffer);
      default:
        throw new Error("Unsupported file type");
    }
  }

  async extractPageRangeContent(
    filePath: string,
    startPage: number,
    endPage: number,
    fileType: string
  ): Promise<string> {
    try {
      const fileBuffer = await this.getFileBuffer(filePath);

      if (fileType === "pdf") {
        return this.extractPDFPageRange(fileBuffer, startPage, endPage);
      } else if (fileType === "pptx") {
        return this.extractPPTXSlideRange(fileBuffer, startPage, endPage);
      } else {
        const result = await this.processFile({
          buffer: fileBuffer,
          mimetype: this.getMimeType(fileType),
        } as Express.Multer.File);
        return this.estimateContentRange(
          result.text,
          startPage,
          endPage,
          result.pageCount
        );
      }
    } catch (error: any) {
      throw new Error(`Page range extraction failed: ${error.message}`);
    }
  }

  private async getFileBuffer(filePath: string): Promise<Buffer> {
    const { data, error } = await supabaseServiceRole.storage
      .from("documents")
      .download(filePath);

    if (error || !data) {
      throw new Error(`Failed to download file: ${error?.message}`);
    }

    return Buffer.from(await data.arrayBuffer());
  }

  private getMimeType(fileType: string): string {
    const mimeTypes: { [key: string]: string } = {
      pdf: "application/pdf",
      docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      txt: "text/plain",
      pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    };
    return mimeTypes[fileType] || "text/plain";
  }

  private async extractPDFPageRange(
    buffer: Buffer,
    startPage: number,
    endPage: number
  ): Promise<string> {
    try {
      // Parse the entire PDF first
      const data = await pdf(buffer);

      // For basic page range extraction, estimate content distribution
      // This is a simplified approach - for production, consider using pdf2pic + OCR for precise extraction
      const totalPages = data.numpages;
      const fullText = data.text;

      if (startPage === 1 && endPage >= totalPages) {
        return this.cleanText(fullText);
      }

      // Estimate content per page by splitting text proportionally
      const lines = fullText
        .split("\n")
        .filter((line) => line.trim().length > 0);
      const linesPerPage = Math.ceil(lines.length / totalPages);

      const startLine = Math.max(0, (startPage - 1) * linesPerPage);
      const endLine = Math.min(lines.length, endPage * linesPerPage);

      const extractedLines = lines.slice(startLine, endLine);

      console.log(
        `PDF page range extraction: pages ${startPage}-${endPage} of ${totalPages} (lines ${startLine}-${endLine})`
      );

      return this.cleanText(extractedLines.join("\n"));
    } catch (error: any) {
      throw new Error(`PDF page range extraction failed: ${error.message}`);
    }
  }

  private async extractPPTXSlideRange(
    buffer: Buffer,
    startSlide: number,
    endSlide: number
  ): Promise<string> {
    try {
      const text = buffer.toString("utf-8");
      const slidePattern = /<p:sld[^>]*>/g;
      const slides = text.split(slidePattern);

      if (slides.length <= 1) {
        return this.estimateContentRange(text, startSlide, endSlide, undefined);
      }

      const selectedSlides = slides.slice(startSlide, endSlide + 1);
      const slideTexts: string[] = [];

      selectedSlides.forEach((slide) => {
        const textMatches = slide.match(/<a:t[^>]*>([^<]+)<\/a:t>/g);
        if (textMatches) {
          const slideText = textMatches
            .map((match) => match.replace(/<[^>]+>/g, ""))
            .join(" ");
          if (slideText.trim()) {
            slideTexts.push(slideText.trim());
          }
        }
      });

      return this.cleanText(slideTexts.join("\n\n"));
    } catch (error: any) {
      throw new Error(`PPTX slide range extraction failed: ${error.message}`);
    }
  }

  private estimateContentRange(
    fullText: string,
    startPage: number,
    endPage: number,
    totalPages?: number
  ): string {
    if (!totalPages || totalPages <= 1) {
      return fullText;
    }

    const lines = fullText.split("\n");
    const linesPerPage = Math.ceil(lines.length / totalPages);

    const startLine = (startPage - 1) * linesPerPage;
    const endLine = Math.min(endPage * linesPerPage, lines.length);

    return lines.slice(startLine, endLine).join("\n");
  }

  private async processPDF(buffer: Buffer): Promise<ProcessingResult> {
    try {
      const data = await pdf(buffer);
      return {
        text: this.cleanText(data.text),
        pageCount: data.numpages,
      };
    } catch (error: any) {
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }

  private async processDocx(buffer: Buffer): Promise<ProcessingResult> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return {
        text: this.cleanText(result.value),
        pageCount: undefined, // DOCX doesn't have reliable page count without complex parsing
      };
    } catch (error: any) {
      throw new Error(`DOCX processing failed: ${error.message}`);
    }
  }

  private async processText(buffer: Buffer): Promise<ProcessingResult> {
    try {
      const text = buffer.toString("utf-8");
      return {
        text: this.cleanText(text),
        pageCount: undefined, // Plain text doesn't have pages
      };
    } catch (error: any) {
      throw new Error(`Text processing failed: ${error.message}`);
    }
  }

  private async processPPTX(buffer: Buffer): Promise<ProcessingResult> {
    // For PPTX, we'll use a simple text extraction
    // In production, consider using a library like 'officegen' or 'node-pptx'
    try {
      // Basic PPTX text extraction (simplified)
      const text = buffer.toString("utf-8");
      // Extract readable text from PPTX XML structure
      const textMatches = text.match(/<a:t[^>]*>([^<]+)<\/a:t>/g);

      // Try to count slides by looking for slide markers
      const slideMarkers = text.match(/<p:sld\s/g);
      const slideCount = slideMarkers ? slideMarkers.length : undefined;

      if (textMatches) {
        const extractedText = textMatches
          .map((match) => match.replace(/<[^>]+>/g, ""))
          .join(" ");
        return {
          text: this.cleanText(extractedText),
          pageCount: slideCount,
        };
      }
      return {
        text: "Unable to extract text from PowerPoint file",
        pageCount: slideCount,
      };
    } catch (error: any) {
      throw new Error(`PPTX processing failed: ${error.message}`);
    }
  }

  private cleanText(text: string): string {
    return text
      .replace(/\s+/g, " ") // Replace multiple whitespace with single space
      .replace(/\n\s*\n/g, "\n") // Remove empty lines
      .trim();
  }
}

// File upload service for Supabase Storage
export class FileUploadService {
  async uploadFile(
    fileName: string,
    fileBuffer: Buffer,
    mimeType: string
  ): Promise<string> {
    try {
      // Use service role client to bypass RLS for storage operations
      const { data, error } = await supabaseServiceRole.storage
        .from("documents")
        .upload(fileName, fileBuffer, {
          contentType: mimeType,
          upsert: false,
        });

      if (error) {
        throw new Error(`File upload failed: ${error.message}`);
      }

      return data.path;
    } catch (error: any) {
      throw new Error(`Storage upload failed: ${error.message}`);
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      const { error } = await supabaseServiceRole.storage
        .from("documents")
        .remove([filePath]);

      if (error) {
        throw new Error(`File deletion failed: ${error.message}`);
      }
    } catch (error) {
      console.error("File deletion error:", error);
      // Don't throw here as this is cleanup
    }
  }

  async getFileUrl(filePath: string): Promise<string> {
    try {
      const { data } = await supabaseServiceRole.storage
        .from("documents")
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (!data?.signedUrl) {
        throw new Error("Failed to generate file URL");
      }

      return data.signedUrl;
    } catch (error: any) {
      throw new Error(`URL generation failed: ${error.message}`);
    }
  }
}
