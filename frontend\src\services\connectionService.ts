import { create } from 'zustand';

export interface ConnectionState {
  isOnline: boolean;
  isBackendAvailable: boolean;
  lastChecked: Date | null;
  retryCount: number;
  isChecking: boolean;
}

interface ConnectionStore extends ConnectionState {
  checkConnection: () => Promise<boolean>;
  setOffline: () => void;
  setOnline: () => void;
  reset: () => void;
}

// Request deduplication map
const pendingRequests = new Map<string, Promise<any>>();

// Exponential backoff configuration
const INITIAL_RETRY_DELAY = 1000; // 1 second
const MAX_RETRY_DELAY = 30000; // 30 seconds
const MAX_RETRIES = 5;

export const useConnectionStore = create<ConnectionStore>((set, get) => ({
  isOnline: navigator.onLine,
  isBackendAvailable: false,
  lastChecked: null,
  retryCount: 0,
  isChecking: false,

  checkConnection: async () => {
    const state = get();
    
    // Prevent multiple simultaneous checks
    if (state.isChecking) {
      return state.isBackendAvailable;
    }

    set({ isChecking: true });

    try {
      // Check if browser is online first
      if (!navigator.onLine) {
        set({ 
          isOnline: false, 
          isBackendAvailable: false, 
          lastChecked: new Date(),
          isChecking: false 
        });
        return false;
      }

      // Check backend health endpoint with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch('/api/health', {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      clearTimeout(timeoutId);

      const isAvailable = response.ok;
      
      set({
        isOnline: true,
        isBackendAvailable: isAvailable,
        lastChecked: new Date(),
        retryCount: isAvailable ? 0 : state.retryCount + 1,
        isChecking: false,
      });

      return isAvailable;
    } catch (error) {
      // Handle fetch errors (network issues, timeouts, etc.)
      set({
        isOnline: navigator.onLine,
        isBackendAvailable: false,
        lastChecked: new Date(),
        retryCount: state.retryCount + 1,
        isChecking: false,
      });
      return false;
    }
  },

  setOffline: () => set({ isOnline: false, isBackendAvailable: false }),
  setOnline: () => set({ isOnline: true }),
  reset: () => set({ 
    isOnline: navigator.onLine, 
    isBackendAvailable: false, 
    lastChecked: null, 
    retryCount: 0,
    isChecking: false 
  }),
}));

// Enhanced fetch with connection awareness and deduplication
export const safeFetch = async (
  url: string, 
  options: RequestInit = {},
  retryOnFailure = true
): Promise<Response> => {
  const { checkConnection, isBackendAvailable, retryCount } = useConnectionStore.getState();
  
  // Create a unique key for request deduplication
  const requestKey = `${options.method || 'GET'}-${url}-${JSON.stringify(options.body || {})}`;
  
  // Check if this request is already pending
  if (pendingRequests.has(requestKey)) {
    return pendingRequests.get(requestKey)!;
  }

  const requestPromise = async (): Promise<Response> => {
    try {
      // Check connection status first
      if (!isBackendAvailable) {
        const isConnected = await checkConnection();
        if (!isConnected) {
          throw new Error('Backend server is not available');
        }
      }

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // If request succeeds, ensure connection state is updated
      if (response.ok && !isBackendAvailable) {
        useConnectionStore.getState().checkConnection();
      }

      return response;
    } catch (error) {
      // Update connection state on error
      useConnectionStore.getState().setOffline();
      
      // Implement exponential backoff for retries
      if (retryOnFailure && retryCount < MAX_RETRIES) {
        const delay = Math.min(
          INITIAL_RETRY_DELAY * Math.pow(2, retryCount),
          MAX_RETRY_DELAY
        );
        
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Retry the request
        return safeFetch(url, options, false); // Prevent infinite retry loops
      }
      
      throw error;
    } finally {
      // Clean up pending request
      pendingRequests.delete(requestKey);
    }
  };

  // Store the promise to prevent duplicate requests
  const promise = requestPromise();
  pendingRequests.set(requestKey, promise);
  
  return promise;
};

// Initialize connection monitoring
export const initializeConnectionMonitoring = () => {
  const { checkConnection, setOnline, setOffline } = useConnectionStore.getState();

  // Listen for online/offline events
  window.addEventListener('online', () => {
    setOnline();
    checkConnection();
  });

  window.addEventListener('offline', setOffline);

  // Periodic connection checks (every 30 seconds when offline)
  const startPeriodicChecks = () => {
    setInterval(() => {
      const { isBackendAvailable, isChecking } = useConnectionStore.getState();
      if (!isBackendAvailable && !isChecking) {
        checkConnection();
      }
    }, 30000);
  };

  // Initial connection check
  checkConnection().then(() => {
    startPeriodicChecks();
  });
};
