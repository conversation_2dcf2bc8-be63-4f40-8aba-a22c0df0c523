import { create } from 'zustand';
import { DocumentMetadata, DocumentWithContent } from '../../../shared/types';

interface DocumentState {
  documents: DocumentMetadata[];
  selectedDocuments: Set<string>;
  isLoading: boolean;
  uploadProgress: { [key: string]: number };
  
  // Actions
  fetchDocuments: () => Promise<void>;
  uploadDocument: (file: File) => Promise<DocumentMetadata>;
  deleteDocument: (id: string) => Promise<void>;
  searchDocuments: (query: string) => Promise<DocumentMetadata[]>;
  getDocument: (id: string) => Promise<DocumentWithContent | null>;
  toggleDocumentSelection: (id: string) => void;
  clearSelection: () => void;
  selectAll: () => void;
  setUploadProgress: (fileName: string, progress: number) => void;
}

export const useDocumentStore = create<DocumentState>((set) => ({
  documents: [],
  selectedDocuments: new Set(),
  isLoading: false,
  uploadProgress: {},

  fetchDocuments: async () => {
    set({ isLoading: true });
    
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/documents', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch documents');
      }

      const result = await response.json();
      
      if (result.success) {
        set({ documents: result.data, isLoading: false });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Fetch documents error:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  uploadDocument: async (file: File) => {
    const formData = new FormData();
    formData.append('document', file);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/documents/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Upload failed');
      }

      const result = await response.json();
      
      if (result.success) {
        // Add new document to the list
        set(state => ({
          documents: [result.data, ...state.documents],
          uploadProgress: { ...state.uploadProgress, [file.name]: 100 }
        }));
        
        return result.data;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Upload document error:', error);
      throw error;
    }
  },

  deleteDocument: async (id: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/documents/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Delete failed');
      }

      // Remove document from the list
      set(state => ({
        documents: state.documents.filter(doc => doc.id !== id),
        selectedDocuments: new Set([...state.selectedDocuments].filter(docId => docId !== id))
      }));
    } catch (error) {
      console.error('Delete document error:', error);
      throw error;
    }
  },

  searchDocuments: async (query: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/documents/search?q=${encodeURIComponent(query)}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Search failed');
      }

      const result = await response.json();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Search documents error:', error);
      return [];
    }
  },

  getDocument: async (id: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/documents/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Get document error:', error);
      return null;
    }
  },

  toggleDocumentSelection: (id: string) => {
    set(state => {
      const newSelection = new Set(state.selectedDocuments);
      if (newSelection.has(id)) {
        newSelection.delete(id);
      } else {
        newSelection.add(id);
      }
      return { selectedDocuments: newSelection };
    });
  },

  clearSelection: () => {
    set({ selectedDocuments: new Set() });
  },

  selectAll: () => {
    set(state => ({
      selectedDocuments: new Set(state.documents.map(doc => doc.id))
    }));
  },

  setUploadProgress: (fileName: string, progress: number) => {
    set(state => ({
      uploadProgress: { ...state.uploadProgress, [fileName]: progress }
    }));
  }
}));
