{"version": 3, "file": "static/js/316.6b56635f.chunk.js", "mappings": "oMAgCA,MAAMA,EAAcC,IAClB,MAAMC,EAAQC,KAAKC,MAAMH,EAAU,MAC7BI,EAAUF,KAAKC,MAAOH,EAAU,KAAQ,IACxCK,EAAOL,EAAU,GAEvB,OAAIC,EAAQ,EACJ,GAANK,OAAUL,EAAK,MAAAK,OAAKF,EAAO,KAClBA,EAAU,EACb,GAANE,OAAUF,EAAO,MAAAE,OAAKD,EAAI,KAEpB,GAANC,OAAUD,EAAI,MAIZE,GAKDC,EAAAA,EAAAA,MAAKC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,SAAEC,EAAQ,KAAEC,GAAMJ,EAAA,OACzCK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gEAA+DC,UAC5EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAEN,KACtCI,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCAAwCC,SAAEL,IACtDC,IAAYE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAEJ,OAEzDC,IAAQC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,SAAEH,WAK1CN,EAASW,YAAc,WAEvB,MAAMC,GAEDX,EAAAA,EAAAA,MAAKY,IAAmB,IAAlB,SAAEC,GAAUD,EACrB,MAAME,GAAYC,EAAAA,EAAAA,SAAQ,KACxB,MAAMC,EAAYC,MAAMC,KAAK,CAAEC,OAAQ,GAAK,CAACC,EAAGC,KAC9C,MAAMC,EAAO,IAAIC,KAEjB,OADAD,EAAKE,QAAQF,EAAKG,WAAa,EAAIJ,IAC5B,CACLC,KAAMA,EAAKI,mBAAmB,QAAS,CAAEC,QAAS,UAClDd,SAAU,EACVe,UAAW,KAef,OAXAf,EAASgB,QAAQC,IACf,MAAMC,EAAc,IAAIR,KAAKO,EAAQE,WAC/BC,EAAWvC,KAAKC,OAAO4B,KAAKW,MAAQH,EAAYI,WAAS,OAE/D,GAAIF,GAAY,GAAKA,EAAW,EAAG,CACjC,MAAMG,EAAa,EAAIH,EACvBjB,EAAUoB,GAAYvB,UAAY,EAClCG,EAAUoB,GAAYR,WAAaE,EAAQF,SAC7C,IAGKZ,GACN,CAACH,IAEEwB,EAAU3C,KAAK4C,OAAOxB,EAAUyB,IAAIC,GAAKA,EAAEZ,YAEjD,OACEnB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,kCACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,SAC3DM,EAAUyB,IAAI,CAACE,EAAKC,KACnBjC,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAwCoC,MAAO,CAAEC,OAAQ,SAAUpC,UAChFF,EAAAA,EAAAA,KAAA,OACEC,UAAU,uDACVoC,MAAO,CACLC,OAAQP,EAAU,EAAC,GAAAvC,OAAO2C,EAAIb,UAAYS,EAAW,IAAG,KAAM,KAC9DQ,SAAU,WACVC,OAAQ,EACRC,KAAM,EACNC,MAAO,GAET9C,MAAK,GAAAJ,OAAK2C,EAAI5B,SAAQ,eAAAf,OAAcP,EAAWkD,EAAIb,iBAGvDtB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6BAA4BC,SAAEiC,EAAInB,SAdzCoB,WAsBpB/B,EAAcD,YAAc,gBAErB,MAAMuC,GAAgDjD,EAAAA,EAAAA,MAAKkD,IAG3D,IAH4D,UACjEC,EAAS,SACTtC,GACDqC,EACC,MAAME,GAAYrC,EAAAA,EAAAA,SAAQ,KAAsB,IAADsC,EAAAC,EAC7C,MAAMC,EAAiB1C,EAAS2C,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,GAC5E8B,EAAgB7C,EAASM,OACzBwC,EAAqBD,EAAgB,EAAIH,EAAiBG,EAAgB,EAC1EE,EAAqB/C,EAAS2C,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQ+B,cAAe,GAEpFC,EAAejD,EAASkD,OAAOC,GAAgB,SAAXA,EAAEC,WAAwCC,IAArBF,EAAEG,gBAC3DC,EAAiBN,EAAaN,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQuC,WAAY,GACjFC,EAAeR,EAAaN,OAAO,CAACC,EAAK3B,IAAY2B,GAAO3B,EAAQqC,gBAAkB,GAAI,GAC1FI,EAAkBH,EAAiB,EAAKE,EAAeF,EAAkB,IAAM,EAG/EI,EAAQ,IAAIjD,KAClB,IAAIkD,EAAc,EAClB,IAAK,IAAIpD,EAAI,EAAGA,EAAI,IAAKA,IAAK,CAC5B,MAAMqD,EAAY,IAAInD,KAAKiD,GAC3BE,EAAUlD,QAAQgD,EAAM/C,UAAYJ,GAOpC,GALyBR,EAAS8D,KAAK7C,GACjB,IAAIP,KAAKO,EAAQE,WAClB4C,iBAAmBF,EAAUE,gBAIhDH,SACK,GAAIpD,EAAI,EACb,KAEJ,CAGA,MAAMwD,EAAgBhE,EAAS2C,OAAO,CAACsB,EAAKhD,KAC1CgD,EAAIhD,EAAQiD,aAAeD,EAAIhD,EAAQiD,aAAe,GAAK,EACpDD,GACN,CAAC,GAEEE,EAC2B,QADX3B,EAAG4B,OAAOC,QAAQL,GACrCM,KAAK,CAAAC,EAAAC,KAAA,IAAE,CAACC,GAAEF,GAAG,CAACG,GAAEF,EAAA,OAAKE,EAAID,IAAG,UAAE,IAAAjC,OAAA,EADRA,EACW,GAQpC,MAAO,CACLE,iBACAG,gBACAC,qBACAC,qBACAW,kBACAE,cACAe,gBAbuE,QAAlDlC,EAAAH,EAAUsC,KAAKC,GAAOA,EAAIC,KAAOX,UAAiB,IAAA1B,OAAA,EAAlDA,EAAoDsC,OAAQ,OAcjFC,eAZqBhF,EACpBsE,KAAK,CAACG,EAAGC,IAAM,IAAIhE,KAAKgE,EAAEvD,WAAWG,UAAY,IAAIZ,KAAK+D,EAAEtD,WAAWG,WACvE2D,MAAM,EAAG,KAYX,CAACjF,EAAUsC,IAEd,OACE1C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qCAAoCC,SAAC,qBACnDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,qDAI/BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4DAA2DC,SAAA,EACxEF,EAAAA,EAAAA,KAACP,EAAQ,CACPG,MAAM,mBACNC,MAAOZ,EAAW6D,EAAUG,gBAC5BlD,KAAK,kBAEPC,EAAAA,EAAAA,KAACP,EAAQ,CACPG,MAAM,iBACNC,MAAOiD,EAAUM,cACjBtD,SAAS,WACTC,KAAK,kBAEPC,EAAAA,EAAAA,KAACP,EAAQ,CACPG,MAAM,kBACNC,MAAOZ,EAAWG,KAAKqG,MAAM3C,EAAUO,qBACvCtD,KAAK,YAEPC,EAAAA,EAAAA,KAACP,EAAQ,CACPG,MAAM,eACNC,MAAK,GAAAL,OAAKsD,EAAUqB,YAAW,SAC/BpE,KAAK,qBAITI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,KAACP,EAAQ,CACPG,MAAM,iBACNC,MAAOiD,EAAUQ,mBACjBxD,SAAS,yBACTC,KAAK,YAEPC,EAAAA,EAAAA,KAACP,EAAQ,CACPG,MAAM,gBACNC,MAAK,GAAAL,OAAKJ,KAAKqG,MAAM3C,EAAUmB,iBAAgB,KAC/CnE,SAAS,6BACTC,KAAK,qBAKTC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAACK,EAAa,CAACE,SAAUA,OAI3BJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,oBACjB,IAApC4C,EAAUyC,eAAe1E,QACxBb,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,8BAE7BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB4C,EAAUyC,eAAetD,IAAI,CAACT,EAASY,KACtC,MAAMsD,EAAW7C,EAAUsC,KAAKC,GAAOA,EAAIC,KAAO7D,EAAQiD,YAC1D,OACEtE,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,kFAAiFC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBAAwBC,UAAU,OAARwF,QAAQ,IAARA,OAAQ,EAARA,EAAUJ,OAAQ,iBACzDnF,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBC,SAAA,CAChB,SAAjBsB,EAAQmC,KAAkB,OAAS,aAAa,WAAInC,EAAQ+B,cAAc,IAAE/B,EAAQuC,WAAW,gBAGpG5D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAEjB,EAAWuC,EAAQF,cACzDtB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SACjC,IAAIe,KAAKO,EAAQE,WAAWN,4BAVzBgB,eAuB1BO,EAAevC,YAAc,iBClO7B,MAAMnB,EAAcC,IAClB,MAAMC,EAAQC,KAAKC,MAAMH,EAAU,MAC7BI,EAAUF,KAAKC,MAAOH,EAAU,KAAQ,IAE9C,OAAIC,EAAQ,EACJ,GAANK,OAAUL,EAAK,MAAAK,OAAKF,EAAO,KAClBA,EAAU,EACb,GAANE,OAAUF,EAAO,KAEX,GAANE,OAAUN,EAAO,MAIfyG,EAODhG,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,SAAEC,EAAUC,KAAM6F,EAAI,MAAEC,EAAK,UAAEC,GAAWnG,EAAA,OAC5DQ,EAAAA,EAAAA,MAAC4F,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBrG,UAAU,sEAAqEC,SAAA,EAE/EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UAC/CF,EAAAA,EAAAA,KAAC4F,EAAI,CAAC3F,UAAU,+BAGjB4F,IACC1F,EAAAA,EAAAA,MAAA,OAAKF,UAAS,+BAAAT,OACZqG,EAAMU,WAAa,iBAAmB,gBACrCrG,SAAA,EACDF,EAAAA,EAAAA,KAACwG,EAAAA,IAAY,CAACvG,UAAS,WAAAT,OAAaqG,EAAMU,WAAa,GAAK,iBAC5DpG,EAAAA,EAAAA,MAAA,QAAMF,UAAU,sBAAqBC,SAAA,CAAEd,KAAKqH,IAAIZ,EAAMhG,OAAO,cAKnEM,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yCAAwCC,SAAEN,IACvDkG,GACC9F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAGjBD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gCAA+BC,SAAEL,IAE/CC,IACCE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAEJ,WAMtC4G,EAAwDpG,IAM9D,IAN+D,UACpEuC,EAAS,SACTtC,EAAQ,UACRoG,EAAS,UACTC,EAAS,UACTd,GACDxF,EACC,MAAMuG,GAAUpG,EAAAA,EAAAA,SAAQ,KACtB,MAAMmB,EAAM,IAAIX,KACV6F,EAAc,CAClB,KAAM,OACN,MAAO,OACP,MAAO,OACP,IAAOC,KACPH,GAEII,EAAmBzG,EAASkD,OAAOjC,IACvC,MAAMyF,EAAc,IAAIhG,KAAKO,EAAQE,WAAWG,UAChD,OAAOD,EAAIC,UAAYoF,GAAeH,IAGlC7D,EAAiB+D,EAAiB9D,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,GACpF8B,EAAgB4D,EAAiBnG,OACjCwC,EAAqBD,EAAgB,EAAIH,EAAiBG,EAAgB,EAE1EW,EAAaiD,EAAiB9D,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQuC,WAAY,GACjFR,EAAgByD,EAAiB9D,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQ+B,cAAe,GACvF2D,EAAiBnD,EAAa,EAAKR,EAAgBQ,EAAc,IAAM,EAEvEoD,EAAkBtE,EAAUY,OAAO2B,GAAOA,EAAIgC,iBAAiBvG,OAI/D0E,EAAiBhF,EAASkD,OAAOjC,IACrC,MAAMyF,EAAc,IAAIhG,KAAKO,EAAQE,WAAWG,UAChD,OAAOD,EAAIC,UAAYoF,GAHL,QAIjBpG,OAEH,MAAO,CACLwG,eAAgBxE,EAAUhC,OAC1ByG,eAAgBX,EAAU9F,OAC1BuC,gBACAH,iBACAI,qBACA6D,iBACAC,kBACA5B,mBAED,CAAC1C,EAAWtC,EAAUoG,EAAWC,IAG9BW,GAAS9G,EAAAA,EAAAA,SAAQ,KACrB,MAAMmB,EAAM,IAAIX,KACV6F,EAAc,CAClB,KAAM,OACN,MAAO,OACP,MAAO,OACP,IAAOC,KACPH,GAEF,GAAIE,IAAgBC,IAAU,MAAO,CAAC,EAEtC,MAAMS,EAAqB5F,EAAIC,UAAYiF,EACrCW,EAAsBD,EAAqBV,EAE3CY,EAAkBnH,EAASkD,OAAOjC,GAClB,IAAIP,KAAKO,EAAQE,WAAWG,WAC1B2F,GAGlBG,EAAmBpH,EAASkD,OAAOjC,IACvC,MAAMyF,EAAc,IAAIhG,KAAKO,EAAQE,WAAWG,UAChD,OAAOoF,GAAeQ,GAAuBR,EAAcO,IAGvDI,EAAcF,EAAgBxE,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,GAChFuG,EAAeF,EAAiBzE,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,GAElFwG,EAAYD,EAAe,GAAMD,EAAcC,GAAgBA,EAAgB,IAAM,EACrFE,EAAgBJ,EAAiB9G,OAAS,GAC5C6G,EAAgB7G,OAAS8G,EAAiB9G,QAAU8G,EAAiB9G,OAAU,IAAM,EAEzF,MAAO,CACLmH,UAAW,CAAEnI,MAAOT,KAAKqG,MAAMqC,GAAYvB,WAAYuB,GAAa,GACpEvH,SAAU,CAAEV,MAAOT,KAAKqG,MAAMsC,GAAgBxB,WAAYwB,GAAiB,KAE5E,CAACxH,EAAUqG,IAEd,OACEzG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAAC2F,EAAU,CACT/F,MAAM,aACNC,MAAOgH,EAAQQ,eACfvH,SAAS,gBACTC,KAAMkI,EAAAA,IACNnC,UAAWA,KAGb9F,EAAAA,EAAAA,KAAC2F,EAAU,CACT/F,MAAM,YACNC,MAAOgH,EAAQS,eACfxH,SAAS,iBACTC,KAAMmI,EAAAA,IACNpC,UAAWA,KAGb9F,EAAAA,EAAAA,KAAC2F,EAAU,CACT/F,MAAM,aACNC,MAAOZ,EAAW4H,EAAQ5D,gBAC1BnD,SAAQ,GAAAN,OAAmB,QAAdoH,EAAsB,WAAU,QAAApH,OAAWoH,EAAUuB,QAAQ,IAAK,WAC/EpI,KAAMqI,EAAAA,IACNvC,MAAO0B,EAAOS,UACdlC,UAAWA,KAGb9F,EAAAA,EAAAA,KAAC2F,EAAU,CACT/F,MAAM,WACNC,MAAOgH,EAAQzD,cACftD,SAAQ,GAAAN,OAAmB,QAAdoH,EAAsB,WAAU,QAAApH,OAAWoH,EAAUuB,QAAQ,IAAK,WAC/EpI,KAAMsI,EAAAA,IACNxC,MAAO0B,EAAOhH,SACduF,UAAWA,QAKf3F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAC2F,EAAU,CACT/F,MAAM,cACNC,MAAOZ,EAAWG,KAAKqG,MAAMoB,EAAQxD,qBACrCvD,SAAS,mBACTC,KAAMqI,EAAAA,IACNtC,UAAWA,KAGb9F,EAAAA,EAAAA,KAAC2F,EAAU,CACT/F,MAAM,kBACNC,MAAK,GAAAL,OAAKJ,KAAKqG,MAAMoB,EAAQK,gBAAe,KAC5CpH,SAAS,iBACTC,KAAMuI,EAAAA,IACNxC,UAAWA,KAGb9F,EAAAA,EAAAA,KAAC2F,EAAU,CACT/F,MAAM,eACNC,MAAOgH,EAAQM,gBACfrH,SAAS,2BACTC,KAAMwI,EAAAA,IACNzC,UAAWA,QAKf3F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,iBACtDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sCAAqCC,SAAE2G,EAAQtB,kBAC9DvF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,uBAGzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,SAC/C2C,EAAUY,OAAO2B,GAAoB,eAAbA,EAAIzB,MAAuB9C,UAEtDb,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,uBAGzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,SAC9C2C,EAAUY,OAAO2B,GAAoB,SAAbA,EAAIzB,MAAiB9C,UAEhDb,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,kBAGzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qCAAoCC,SAAA,CAChDd,KAAKqG,MAAOoB,EAAQM,gBAAkB/H,KAAK4C,IAAI6E,EAAQQ,eAAgB,GAAM,KAAK,QAErFrH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,8BCjP7CsI,EAAeC,GACfA,EAAiB,EACb,GAANjJ,OAAUJ,KAAKqG,MAAuB,GAAjBgD,GAAoB,UAErC,GAANjJ,OAAUiJ,EAAeC,QAAQ,GAAE,cAG/BC,EAQDhJ,IAAkF,IAAjF,MAAEC,EAAK,MAAEC,EAAK,SAAEC,EAAUC,KAAM6F,EAAI,MAAEC,EAAK,UAAEC,EAAS,MAAE8C,EAAQ,WAAWjJ,EAQ/E,OACEQ,EAAAA,EAAAA,MAAC4F,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBrG,UAAU,sEAAqEC,SAAA,EAE/EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,kBAAAT,OAfC,CACnBqJ,QAAS,qCACTC,MAAO,iCACPC,IAAK,6BACLC,OAAQ,oCAW2CJ,IAAS1I,UACtDF,EAAAA,EAAAA,KAAC4F,EAAI,CAAC3F,UAAU,mBAGP2D,IAAViC,IACC1F,EAAAA,EAAAA,MAAA,OAAKF,UAAS,+BAAAT,OACZqG,GAAS,EAAI,iBAAmB,gBAC/B3F,SAAA,CACA2F,GAAS,GACR7F,EAAAA,EAAAA,KAACwG,EAAAA,IAAY,CAACvG,UAAU,aAExBD,EAAAA,EAAAA,KAACiJ,EAAAA,IAAc,CAAChJ,UAAU,aAE5BE,EAAAA,EAAAA,MAAA,QAAMF,UAAU,sBAAqBC,SAAA,CAAEd,KAAKqH,IAAIZ,GAAO6C,QAAQ,GAAG,cAKxEvI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yCAAwCC,SAAEN,IACvDkG,GACC9F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAGjBD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gCAA+BC,SAAEL,IAE/CC,IACCE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAEJ,WAO/CoJ,EAID5I,IAAA,IAAC,SAAE6I,EAAQ,MAAEvJ,EAAK,MAAEgJ,GAAOtI,EAAA,OAC9BH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAEN,KACvDI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACH,IAApBiJ,EAAStI,QACRb,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAAgCC,SAAC,sBAE9CiJ,EAASlH,IAAI,CAACmH,EAAShH,KACrBjC,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qCAAoCC,SAAEkJ,EAAQ9D,QAC9DnF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAS,oBAAAT,OAAsBoJ,GAC/BvG,MAAO,CAAEgH,MAAM,GAAD7J,OAAK4J,EAAQE,SAAQ,WAGvCnJ,EAAAA,EAAAA,MAAA,QAAMF,UAAU,yCAAwCC,SAAA,CACrDd,KAAKqG,MAAM2D,EAAQE,UAAU,YAV1BlH,UAoBPmH,EAAwD3G,IAK9D,IAL+D,UACpEC,EAAS,SACTtC,EAAQ,UACRqG,EAAS,UACTd,GACDlD,EACC,MAAM4G,GAAkB/I,EAAAA,EAAAA,SAAQ,KAC9B,MAAMmB,EAAM,IAAIX,KACV6F,EAAc,CAClB,KAAM,OACN,MAAO,OACP,MAAO,OACP,IAAOC,KACPH,GAEII,EAAmBzG,EAASkD,OAAOjC,IACvC,MAAMyF,EAAc,IAAIhG,KAAKO,EAAQE,WAAWG,UAChD,OAAOD,EAAIC,UAAYoF,GAAeH,IAIlCtD,EAAewD,EAAiBvD,OAAOC,GAAgB,SAAXA,EAAEC,WAAwCC,IAArBF,EAAEG,gBACnEC,EAAiBN,EAAaN,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQuC,WAAY,GACjFC,EAAeR,EAAaN,OAAO,CAACC,EAAK3B,IAAY2B,GAAO3B,EAAQqC,gBAAkB,GAAI,GAC1F4F,EAAkB3F,EAAiB,EAAKE,EAAeF,EAAkB,IAAM,EAG/EC,EAAaiD,EAAiB9D,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQ+B,cAAe,GACpFmG,EAAmB1C,EAAiB9D,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,GAAK,GAC3FqI,EAAeD,EAAmB,EAAI3F,EAAa2F,EAAmB,EAGtEE,EAAqB,IAAIC,IAE/BrG,EAAajC,QAAQC,IACnB,MAAMkE,EAAW7C,EAAUsC,KAAKC,GAAOA,EAAIC,KAAO7D,EAAQiD,YAC1D,GAAIiB,EAAU,CACZ,MAAMoE,EAAWF,EAAmBG,IAAIrE,EAASJ,OAAS,CAAE0E,QAAS,EAAGC,MAAO,GAC/EL,EAAmBxE,IAAIM,EAASJ,KAAM,CACpC0E,QAASF,EAASE,SAAWxI,EAAQqC,gBAAkB,GACvDoG,MAAOH,EAASG,MAAQzI,EAAQuC,YAEpC,IAGF,MAAMmG,EAAoBvJ,MAAMC,KAAKgJ,EAAmBhF,WACrD3C,IAAI6C,IAAA,IAAEQ,EAAM6E,GAAKrF,EAAA,MAAM,CACtBQ,OACAgE,SAAUa,EAAKF,MAAQ,EAAKE,EAAKH,QAAUG,EAAKF,MAAS,IAAM,KAEhEpF,KAAK,CAACG,EAAGC,IAAMA,EAAEqE,SAAWtE,EAAEsE,UAE3Bc,EAAoBF,EAAkB1E,MAAM,EAAG,GAC/C6E,EAAkBH,EAAkB1E,OAAO,GAAG8E,UAG9CC,EAAmB5J,MAAMC,KAAK,CAAEC,OAAQ,GAAK,CAACC,EAAGC,KACrD,MAAMC,EAAO,IAAIC,KACjBD,EAAKE,QAAQF,EAAKG,WAAa,EAAIJ,IACnC,MAAMyJ,EAAUxJ,EAAKyJ,cAAcC,MAAM,KAAK,GAExCC,EAAc3D,EAAiBvD,OAAOjC,GACtB,IAAIP,KAAKO,EAAQE,WAAW+I,cAAcC,MAAM,KAAK,KAClDF,GAGnBI,EAAkBD,EAAYlH,OAAOC,GAAgB,SAAXA,EAAEC,WAAwCC,IAArBF,EAAEG,gBACjEgH,EAAgBD,EAAgB1H,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQuC,WAAY,GACnF+G,EAAaF,EAAgB1H,OAAO,CAACC,EAAK3B,IAAY2B,GAAO3B,EAAQqC,gBAAkB,GAAI,GAC3FkH,EAAcF,EAAgB,EAAKC,EAAaD,EAAiB,IAAM,EAEvEG,EAAmBL,EAAYzH,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQ+B,cAAe,GACrF0H,EAAiBN,EAAYzH,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,GAAK,GACpF4J,EAAWD,EAAiB,EAAID,EAAmBC,EAAiB,EAE1E,MAAO,CACLjK,KAAMA,EAAKI,mBAAmB,QAAS,CAAEC,QAAS,UAClDiI,SAAUyB,EACVI,MAAOD,KAKLE,EAAiBb,EAAiB/E,OAAO,GAAGtC,OAAO,CAACC,EAAKhB,IAAQgB,EAAMhB,EAAImH,SAAU,GAAK,EAC1F+B,EAAkBd,EAAiB/E,MAAM,EAAG,GAAGtC,OAAO,CAACC,EAAKhB,IAAQgB,EAAMhB,EAAImH,SAAU,GAAK,EAC7FgC,EAAgBD,EAAkB,GAAMD,EAAiBC,GAAmBA,EAAmB,IAAM,EAErGE,EAAchB,EAAiB/E,OAAO,GAAGtC,OAAO,CAACC,EAAKhB,IAAQgB,EAAMhB,EAAIgJ,MAAO,GAAK,EACpFK,EAAejB,EAAiB/E,MAAM,EAAG,GAAGtC,OAAO,CAACC,EAAKhB,IAAQgB,EAAMhB,EAAIgJ,MAAO,GAAK,EACvFM,EAAaD,EAAe,GAAMD,EAAcC,GAAgBA,EAAgB,IAAM,EAGtFE,EAAmBnB,EAAiBrH,OAAO,CAACC,EAAKhB,KACrD,MAAMwJ,EAAOxJ,EAAImH,SAAWG,EAC5B,OAAOtG,EAAOwI,EAAOA,GACpB,GAAKpB,EAAiB1J,OACnB+K,EAAmBxM,KAAK4C,IAAI,EAAG,IAAM5C,KAAKyM,KAAKH,IAErD,MAAO,CACLjC,kBACA6B,gBACA3B,eACA8B,aACAG,mBACAE,gBAAiBR,EACjBlB,oBACAC,kBACAE,qBAED,CAAChK,EAAUsC,EAAW+D,IAEzB,OACEzG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAAC2I,EAAe,CACd/I,MAAM,mBACNC,MAAK,GAAAL,OAAKJ,KAAKqG,MAAM+D,EAAgBC,iBAAgB,KACrD3J,SAAS,mBACTC,KAAMgM,EAAAA,IACNlG,MAAO2D,EAAgB8B,cACvBxF,UAAWA,EACX8C,MAAM,WAGR5I,EAAAA,EAAAA,KAAC2I,EAAe,CACd/I,MAAM,gBACNC,MAAO2I,EAAYgB,EAAgBG,cACnC7J,SAAS,mBACTC,KAAMiM,EAAAA,IACNnG,MAAO2D,EAAgBiC,WACvB3F,UAAWA,EACX8C,MAAM,YAGR5I,EAAAA,EAAAA,KAAC2I,EAAe,CACd/I,MAAM,cACNC,MAAK,GAAAL,OAAKJ,KAAKqG,MAAM+D,EAAgBoC,kBAAiB,KACtD9L,SAAS,wBACTC,KAAMkM,EAAAA,IACNnG,UAAWA,EACX8C,MAAM,aAGR5I,EAAAA,EAAAA,KAAC2I,EAAe,CACd/I,MAAM,cACNC,MAAK,GAAAL,OAAKgK,EAAgBsC,iBAAmB,EAAI,IAAM,IAAEtM,OAAGgK,EAAgBsC,gBAAgBpD,QAAQ,GAAE,KACtG5I,SAAS,eACTC,KAAMyG,EAAAA,IACNV,UAAWA,EACX8C,MAAOY,EAAgBsC,iBAAmB,EAAI,QAAU,YAK5D3L,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAACkJ,EAAuB,CACtBC,SAAUK,EAAgBY,kBAC1BxK,MAAM,qBACNgJ,MAAM,kBAGR5I,EAAAA,EAAAA,KAACkJ,EAAuB,CACtBC,SAAUK,EAAgBa,gBAC1BzK,MAAM,wBACNgJ,MAAM,mBAKVzI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,qCACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,SAC3DsJ,EAAgBe,iBAAiBtI,IAAI,CAACE,EAAKC,KAC1C,MAAM8J,EAAc9M,KAAK4C,OAAOwH,EAAgBe,iBAAiBtI,IAAIC,GAAKA,EAAEoH,UAAW,GACjFhH,EAAUH,EAAImH,SAAW4C,EAAe,IAE9C,OACE/L,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAwCoC,MAAO,CAAEC,OAAQ,SAAUpC,UAChFF,EAAAA,EAAAA,KAAC+F,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAE3D,OAAQ,GACnB8D,QAAS,CAAE9D,OAAO,GAAD9C,OAAK8C,EAAM,MAC5B+D,WAAY,CAAEC,SAAU,GAAK6F,MAAe,GAAR/J,GACpCnC,UAAU,sHACVL,MAAK,GAAAJ,OAAKJ,KAAKqG,MAAMtD,EAAImH,UAAS,gBAAA9J,OAAegJ,EAAYrG,EAAIgJ,aAGrEnL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6BAA4BC,SAAEiC,EAAInB,SAVzCoB,cCvRlBnD,EAAcC,IAClB,MAAMC,EAAQC,KAAKC,MAAMH,EAAU,MAC7BI,EAAUF,KAAKC,MAAOH,EAAU,KAAQ,IAE9C,OAAIC,EAAQ,EACJ,GAANK,OAAUL,EAAK,MAAAK,OAAKF,EAAO,KAClBA,EAAU,EACb,GAANE,OAAUF,EAAO,KAEX,GAANE,OAAUN,EAAO,MAIfkN,EAODzM,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,SAAEC,EAAUC,KAAM6F,EAAI,UAAEE,EAAS,MAAE8C,EAAQ,oBAAoBjJ,EAAA,OACjFQ,EAAAA,EAAAA,MAAC4F,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBrG,UAAU,sEAAqEC,SAAA,EAE/EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,mCAAqCC,UACjDF,EAAAA,EAAAA,KAAC4F,EAAI,CAAC3F,UAAS,WAAAT,OAAaoJ,QAE9BzI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oCAAmCC,SAAEN,IAClDkG,GACC9F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAGjBD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BAA8BC,SAAEL,UAIlDC,IACCE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAEJ,QAKtCuM,EAKD/L,IAA6B,IAA5B,MAAEV,EAAK,KAAEuK,EAAI,MAAEvB,GAAOtI,EAC1B,MAAMgM,EAAWlN,KAAK4C,OAAOmI,EAAKlI,IAAIC,GAAKA,EAAErC,OAAQ,GAErD,OACEM,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAEN,KACvDI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBiK,EAAKlI,IAAI,CAACsK,EAAMnK,KACfjC,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,6BAA4BC,SAAEqM,EAAKC,SACnDxM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sCAAqCC,UAClDF,EAAAA,EAAAA,KAAC+F,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEoD,MAAO,GAClBjD,QAAS,CAAEiD,MAAM,GAAD7J,OAAM+M,EAAK1M,MAAQyM,EAAY,IAAG,MAClDjG,WAAY,CAAEC,SAAU,GAAK6F,MAAe,GAAR/J,GACpCnC,UAAS,oBAAAT,OAAsBoJ,UAIrCzI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,SAAEqM,EAAK1M,QACtD0M,EAAKE,OACJzM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAEjB,EAAWsN,EAAKE,aAfpDrK,UAyBPsK,EAA0C9J,IAIhD,IAJiD,SACtDrC,EAAQ,UACRqG,EAAS,UACTd,GACDlD,EACC,MAAM+J,GAAYlM,EAAAA,EAAAA,SAAQ,KACxB,MAAMmB,EAAM,IAAIX,KAGhB,IAAIkD,EAAc,EACdyI,EAAgB,EAChBC,EAAgB,EAEpB,IAAK,IAAI9L,EAAI,EAAGA,EAAI,IAAKA,IAAK,CAC5B,MAAMqD,EAAY,IAAInD,KAAKW,GAC3BwC,EAAUlD,QAAQU,EAAIT,UAAYJ,GAOlC,GALyBR,EAAS8D,KAAK7C,GACjB,IAAIP,KAAKO,EAAQE,WAClB4C,iBAAmBF,EAAUE,gBAItC,IAANvD,GAAWoD,IAAgBpD,GAC7BoD,IAEF0I,IACAD,EAAgBxN,KAAK4C,IAAI4K,EAAeC,OACnC,CACL,GAAU,IAAN9L,EAAS,CAEX,MAAM+L,EAAY,IAAI7L,KAAKW,GAC3BkL,EAAU5L,QAAQU,EAAIT,UAAY,GACNZ,EAAS8D,KAAK7C,GACpB,IAAIP,KAAKO,EAAQE,WAClB4C,iBAAmBwI,EAAUxI,kBAGhDH,EAAc,EAElB,CACA0I,EAAgB,CAClB,CACF,CAGA,MAAM/F,EAAc,CAClB,KAAM,OACN,MAAO,OACP,MAAO,OACP,IAAOC,KACPH,GAEII,EAAmBzG,EAASkD,OAAOjC,IACvC,MAAMyF,EAAc,IAAIhG,KAAKO,EAAQE,WAAWG,UAChD,OAAOD,EAAIC,UAAYoF,GAAeH,IAGlCiG,EAAOjG,IAAgBC,IAC3B3H,KAAK4C,IAAI,EAAG5C,KAAK4N,MAAMpL,EAAIC,UAAYzC,KAAK6N,OAAO1M,EAAS0B,IAAIyB,GAAK,IAAIzC,KAAKyC,EAAEhC,WAAWG,aAAW,QACtGzC,KAAK4N,KAAKlG,EAAW,OAEjBoG,EAAwBlG,EAAiBnG,OAASkM,EAGlDI,EAAgBxM,MAAMC,KAAK,CAAEC,OAAQ,IAAM,CAACC,EAAGsM,KACnD,MAAMC,EAAerG,EAAiBvD,OAAOjC,GACvB,IAAIP,KAAKO,EAAQE,WAAW4L,aACzBF,GAGzB,MAAO,CACLA,OACA7M,SAAU8M,EAAaxM,OACvB4L,KAAMY,EAAanK,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,MAInEiM,EAAqBJ,EAAcjK,OAAO,CAAClB,EAAKwL,IACpDA,EAAQjN,SAAWyB,EAAIzB,SAAWiN,EAAUxL,GAC5CoL,KAGIK,EAAgB,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAAOxL,IAAI,CAACE,EAAKC,KAChF,MAAMuI,EAAc3D,EAAiBvD,OAAOjC,GACvB,IAAIP,KAAKO,EAAQE,WAAWgM,WACzBtL,GAGxB,MAAO,CACLD,MACA5B,SAAUoK,EAAY9J,OACtB4L,KAAM9B,EAAYzH,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,MAIlEqM,EAAoBF,EAAcvK,OAAO,CAAClB,EAAKwL,IACnDA,EAAQjN,SAAWyB,EAAIzB,SAAWiN,EAAUxL,GAC5CG,IAGIyL,EAAejN,MAAMC,KAAK,CAAEC,OAAQ,GAAK,CAACC,EAAGC,KACjD,MAAMC,EAAO,IAAIC,KAAKW,GACtBZ,EAAK6M,SAASjM,EAAIkM,YAAc,EAAI/M,IACpC,MAAMgN,EAAY/M,EAAKI,mBAAmB,QAAS,CAAE4M,MAAO,UAEtDC,EAAgB1N,EAASkD,OAAOjC,IACpC,MAAMC,EAAc,IAAIR,KAAKO,EAAQE,WACrC,OAAOD,EAAYqM,aAAe9M,EAAK8M,YAChCrM,EAAYyM,gBAAkBlN,EAAKkN,gBAG5C,MAAO,CACLF,MAAOD,EACPxN,SAAU0N,EAAcpN,OACxB4L,KAAMwB,EAAc/K,OAAO,CAACC,EAAK3B,IAAY2B,EAAM3B,EAAQF,UAAW,MAI1E,MAAO,CACL6C,cACAyI,gBACAM,wBACAK,qBACAI,oBACAF,gBACAN,cAAeA,EAAc1J,OAAO0K,GAAKA,EAAE5N,SAAW,GACtDqN,iBAED,CAACrN,EAAUqG,IAERwH,EAAchB,GACL,IAATA,EAAmB,QACnBA,EAAO,GAAU,GAAN5N,OAAU4N,EAAI,OAChB,KAATA,EAAoB,QAClB,GAAN5N,OAAU4N,EAAO,GAAE,OAGrB,OACEjN,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAACoM,EAAS,CACRxM,MAAM,iBACNC,MAAK,GAAAL,OAAKmN,EAAUxI,YAAW,SAC/BrE,SAAS,yBACTC,KAAMsO,EAAAA,IACNvI,UAAWA,EACX8C,MAAM,qBAGR5I,EAAAA,EAAAA,KAACoM,EAAS,CACRxM,MAAM,iBACNC,MAAK,GAAAL,OAAKmN,EAAUC,cAAa,SACjC9M,SAAS,gBACTC,KAAMyG,EAAAA,IACNV,UAAWA,EACX8C,MAAM,oBAGR5I,EAAAA,EAAAA,KAACoM,EAAS,CACRxM,MAAM,gBACNC,MAAO8M,EAAUO,sBAAsBxE,QAAQ,GAC/C5I,SAAS,mBACTC,KAAMuO,EAAAA,IACNxI,UAAWA,EACX8C,MAAM,mBAGR5I,EAAAA,EAAAA,KAACoM,EAAS,CACRxM,MAAM,YACNC,MAAOuO,EAAWzB,EAAUY,oBAC5BzN,SAAQ,mBACRC,KAAM4M,EAAUY,mBAAqB,GAAKgB,EAAAA,IAAQC,EAAAA,IAClD1I,UAAWA,EACX8C,MAAM,wBAKVzI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAACqM,EAAY,CACXzM,MAAM,uBACNuK,KAAMwC,EAAUc,cAAcxL,IAAIE,IAAG,CACnCqK,MAAOrK,EAAIA,IACXtC,MAAOsC,EAAI5B,SACXkM,KAAMtK,EAAIsK,QAEZ9I,KAAK,MACLiF,MAAM,oBAGR5I,EAAAA,EAAAA,KAACqM,EAAY,CACXzM,MAAM,qBACNuK,KAAMwC,EAAUQ,cAAc3H,MAAM,EAAG,GAAGvD,IAAImL,IAAI,CAChDZ,MAAO4B,EAAWhB,EAAKA,MACvBvN,MAAOuN,EAAK7M,SACZkM,KAAMW,EAAKX,QAEb9I,KAAK,MACLiF,MAAM,sBAKVzI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,yBACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,SAC3DyM,EAAUiB,aAAa3L,IAAI,CAAC+L,EAAO5L,KAClC,MAAMqM,EAAcrP,KAAK4C,OAAO2K,EAAUiB,aAAa3L,IAAIyM,GAAKA,EAAEnO,UAAW,GACvE+B,EAAU0L,EAAMzN,SAAWkO,EAAe,IAEhD,OACEtO,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAwCoC,MAAO,CAAEC,OAAQ,SAAUpC,UAChFF,EAAAA,EAAAA,KAAC+F,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAE3D,OAAQ,GACnB8D,QAAS,CAAE9D,OAAO,GAAD9C,OAAK8C,EAAM,MAC5B+D,WAAY,CAAEC,SAAU,GAAK6F,MAAe,GAAR/J,GACpCnC,UAAU,wHACVL,MAAK,GAAAJ,OAAKwO,EAAMzN,SAAQ,eAAAf,OAAcP,EAAW+O,EAAMvB,YAG3DzM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6BAA4BC,SAAE8N,EAAMA,SACnDhO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iCAAgCC,SAAE8N,EAAMzN,aAX/C6B,WAmBlBjC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,oBACtDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8BAA6BC,SAAC,oBAC5CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAEyM,EAAUgB,qBACxC3N,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,wDAK5CC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8BAA6BC,SAAC,wBAC5CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAEkO,EAAWzB,EAAUY,uBACnDvN,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,mE,kCC1VtD,MAAMyO,EAA8B,CAClC,CACEtJ,GAAI,WACJmH,MAAO,WACPzM,KAAMkM,EAAAA,IACN2C,YAAa,wCAEf,CACEvJ,GAAI,cACJmH,MAAO,cACPzM,KAAMyG,EAAAA,IACNoI,YAAa,6CAEf,CACEvJ,GAAI,SACJmH,MAAO,SACPzM,KAAMqI,EAAAA,IACNwG,YAAa,qCAIJC,EAA0BA,KACrC,MAAOC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,aACpCpI,EAAWqI,IAAgBD,EAAAA,EAAAA,UAAuC,QAClElJ,EAAWoJ,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAwB,OAE5C,UAAEnM,EAAS,SAAEtC,EAAQ,eAAE8O,EAAc,mBAAEC,IAAuBC,EAAAA,EAAAA,kBAC9D,UAAE5I,IAAc6I,EAAAA,EAAAA,MAEtBC,EAAAA,EAAAA,WAAU,KACkBC,WACxBR,GAAa,GACbE,EAAS,MAET,UACQO,QAAQC,IAAI,CAChBP,IACAC,EAAmB1I,IAEvB,CAAE,MAAOiJ,GACPT,EAASS,aAAeC,MAAQD,EAAIE,QAAU,gCAChD,CAAC,QACCb,GAAa,EACf,GAGFc,IACC,CAACpJ,EAAWyI,EAAgBC,IAE/B,MA0CMW,EAAoBA,KACxB9P,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAC0G,EAAkB,CACjB7D,UAAWA,EACXtC,SAAUA,EACVoG,UAAWA,EACXC,UAAWA,EACXd,UAAWA,KAEb9F,EAAAA,EAAAA,KAAC2C,EAAc,CACbE,UAAWA,EACXtC,SAAUA,OAmChB,OACEP,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qCAAoCC,SAAC,eACnDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,8DAG/BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAE1CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,UACEN,MAAO+G,EACPsJ,SAAWC,GAAMlB,EAAakB,EAAEC,OAAOvQ,OACvCI,UAAU,2KAA0KC,SAAA,EAEpLF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,KAAIK,SAAC,iBACnBF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,MAAKK,SAAC,kBACpBF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,MAAKK,SAAC,kBACpBF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,MAAKK,SAAC,iBAEtBF,EAAAA,EAAAA,KAACqQ,EAAAA,IAAQ,CAACpQ,UAAU,sGAItBE,EAAAA,EAAAA,MAACmQ,EAAAA,EAAM,CACLC,QApGYC,KAEtB,MAAMC,EAAU,CACd,CAAC,OAAQ,YAAa,OAAQ,iBAAkB,uBAAwB,YAAYC,KAAK,QACtFnQ,EAAS0B,IAAIT,IACd,MAAMkE,EAAW7C,EAAUsC,KAAKC,GAAOA,EAAIC,KAAO7D,EAAQiD,YAC1D,MAAO,CACL,IAAIxD,KAAKO,EAAQE,WAAWN,sBACpB,OAARsE,QAAQ,IAARA,OAAQ,EAARA,EAAUJ,OAAQ,UAClB9D,EAAQmC,KACRnC,EAAQ+B,cACRnE,KAAKqG,MAAMjE,EAAQF,UAAY,IAC/BE,EAAQqC,eAAc,GAAArE,OAAMJ,KAAKqG,MAAOjE,EAAQqC,eAAiBrC,EAAQuC,WAAc,KAAI,KAAM,OACjG2M,KAAK,QAETA,KAAK,MAEDC,EAAO,IAAIC,KAAK,CAACH,GAAU,CAAE9M,KAAM,aACnCkN,EAAMC,OAAOC,IAAIC,gBAAgBL,GACjC3L,EAAIiM,SAASC,cAAc,KACjClM,EAAEmM,KAAON,EACT7L,EAAEoM,SAAQ,mBAAA5R,QAAsB,IAAIyB,MAAOwJ,cAAcC,MAAM,KAAK,GAAE,QACtE1F,EAAEqM,QACFP,OAAOC,IAAIO,gBAAgBT,IA8EjBU,QAAQ,YACRC,KAAK,KACLC,SAA8B,IAApBlR,EAASM,OAAaX,SAAA,EAEhCF,EAAAA,EAAAA,KAAC0R,EAAAA,IAAU,CAACzR,UAAU,iBAAiB,aAKzCE,EAAAA,EAAAA,MAACmQ,EAAAA,EAAM,CACLC,QA/HUb,UACpBN,EAAS,MACTF,GAAa,GAEb,UACQS,QAAQC,IAAI,CAChBP,IACAC,EAAmB1I,IAEvB,CAAE,MAAOiJ,GACPT,EAASS,aAAeC,MAAQD,EAAIE,QAAU,yBAChD,CAAC,QACCb,GAAa,EACf,GAmHUqC,QAAQ,YACRC,KAAK,KACLC,SAAU3L,EAAU5F,SAAA,EAEpBF,EAAAA,EAAAA,KAAC2R,EAAAA,IAAS,CAAC1R,UAAS,gBAAAT,OAAkBsG,EAAY,eAAiB,MAAQ,mBAOhFqJ,IACChP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAC4R,EAAAA,IAAmB,CAAC3R,UAAU,0BAC/BD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2BAA0BC,SAAC,cAE7CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAAmBC,SAAEiP,KAClCnP,EAAAA,EAAAA,KAACsQ,EAAAA,EAAM,CACLC,QAASA,IAAMnB,EAAS,MACxBmC,QAAQ,YACRC,KAAK,KACLvR,UAAU,OAAMC,SACjB,gBAMLC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,UAClFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvByO,EAAc1M,IAAK4P,IAClB,MAAMjM,EAAOiM,EAAI9R,KACX+R,EAAWhD,IAAc+C,EAAIxM,GAEnC,OACElF,EAAAA,EAAAA,MAAA,UAEEoQ,QAASA,IAAMxB,EAAa8C,EAAIxM,IAChCpF,UAAS,6KAAAT,OAGLsS,EACE,kEACA,8DAA6D,4BAEjE5R,SAAA,EAEFF,EAAAA,EAAAA,KAAC4F,EAAI,CAAC3F,UAAU,aAChBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oBAAmBC,SAAE2R,EAAIrF,SACzCxM,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uCAAsCC,SACnD2R,EAAIjD,mBAfJiD,EAAIxM,aA0BrBrF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAC+F,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAG6L,EAAG,IAC1B3L,QAAS,CAAEF,QAAS,EAAG6L,EAAG,GAC1B1L,WAAY,CAAEC,SAAU,IAAMpG,SA5HpB8R,MACpB,OAAQlD,GACN,IAAK,WAML,QACE,OAAOmB,IALT,IAAK,cACH,OArBJjQ,EAAAA,EAAAA,KAACuJ,EAAkB,CACjB1G,UAAWA,EACXtC,SAAUA,EACVqG,UAAWA,EACXd,UAAWA,IAkBX,IAAK,SACH,OAdJ9F,EAAAA,EAAAA,KAAC0M,EAAW,CACVnM,SAAUA,EACVqG,UAAWA,EACXd,UAAWA,MAkIFkM,IALIlD,a,gDC3PZ,MAAMU,GAAmByC,E,QAAAA,IAAuB7M,IAAG,CACxDuB,UAAW,GACXuL,kBAAmB,IAAIC,IACvBrM,WAAW,EACXsM,eAAgB,CAAC,EAEjBC,eAAgB3C,UACdtK,EAAI,CAAEU,WAAW,IAEjB,IACE,MAAMwM,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,iBAAkB,CAC7CC,QAAS,CACPC,cAAc,UAADpT,OAAY8S,MAI7B,IAAKG,EAASI,GACZ,MAAM,IAAI/C,MAAM,6BAGlB,MAAMgD,QAAeL,EAASM,OAE9B,IAAID,EAAOE,QAGT,MAAM,IAAIlD,MAAMgD,EAAO3D,OAFvB/J,EAAI,CAAEuB,UAAWmM,EAAO3I,KAAMrE,WAAW,GAI7C,CAAE,MAAOqJ,GAGP,MAFA8D,QAAQ9D,MAAM,yBAA0BA,GACxC/J,EAAI,CAAEU,WAAW,IACXqJ,CACR,GAGF+D,eAAgBxD,UACd,MAAMyD,EAAW,IAAIC,SACrBD,EAASE,OAAO,WAAYC,GAE5B,IACE,MAAMhB,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,wBAAyB,CACpDa,OAAQ,OACRZ,QAAS,CACPC,cAAc,UAADpT,OAAY8S,IAE3BkB,KAAML,IAGR,IAAKV,EAASI,GAAI,CAChB,MAAMY,QAAoBhB,EAASM,OACnC,MAAM,IAAIjD,MAAM2D,EAAYtE,OAAS,gBACvC,CAEA,MAAM2D,QAAeL,EAASM,OAE9B,GAAID,EAAOE,QAOT,OALA5N,EAAKsO,IAAK,CACR/M,UAAW,CAACmM,EAAO3I,QAASuJ,EAAM/M,WAClCyL,gBAAcuB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAMtB,gBAAc,IAAE,CAACkB,EAAKhO,MAAO,SAGnDwN,EAAO3I,KAEd,MAAM,IAAI2F,MAAMgD,EAAO3D,MAE3B,CAAE,MAAOA,GAEP,MADA8D,QAAQ9D,MAAM,yBAA0BA,GAClCA,CACR,GAGFyE,eAAgBlE,UACd,IACE,MAAM4C,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAADlT,OAAmB6F,GAAM,CACnDkO,OAAQ,SACRZ,QAAS,CACPC,cAAc,UAADpT,OAAY8S,MAI7B,IAAKG,EAASI,GAAI,CAChB,MAAMY,QAAoBhB,EAASM,OACnC,MAAM,IAAIjD,MAAM2D,EAAYtE,OAAS,gBACvC,CAGA/J,EAAKsO,IAAK,CACR/M,UAAW+M,EAAM/M,UAAUlD,OAAQoQ,GAAQA,EAAIxO,KAAOA,GACtD6M,kBAAmB,IAAIC,IACrB,IAAIuB,EAAMxB,mBAAmBzO,OAAQqQ,GAAUA,IAAUzO,MAG/D,CAAE,MAAO8J,GAEP,MADA8D,QAAQ9D,MAAM,yBAA0BA,GAClCA,CACR,GAGF4E,gBAAiBrE,UACf,IACE,MAAM4C,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,2BAADlT,OACCwU,mBAAmBC,IAC9C,CACEtB,QAAS,CACPC,cAAc,UAADpT,OAAY8S,MAK/B,IAAKG,EAASI,GACZ,MAAM,IAAI/C,MAAM,iBAGlB,MAAMgD,QAAeL,EAASM,OAC9B,OAAOD,EAAOE,QAAUF,EAAO3I,KAAO,EACxC,CAAE,MAAOgF,GAEP,OADA8D,QAAQ9D,MAAM,0BAA2BA,GAClC,EACT,GAGF+E,YAAaxE,UACX,IACE,MAAM4C,EAAQC,aAAaC,QAAQ,cAC7BC,QAAiBC,MAAM,kBAADlT,OAAmB6F,GAAM,CACnDsN,QAAS,CACPC,cAAc,UAADpT,OAAY8S,MAI7B,IAAKG,EAASI,GACZ,OAAO,KAGT,MAAMC,QAAeL,EAASM,OAC9B,OAAOD,EAAOE,QAAUF,EAAO3I,KAAO,IACxC,CAAE,MAAOgF,GAEP,OADA8D,QAAQ9D,MAAM,sBAAuBA,GAC9B,IACT,GAGFgF,wBAA0B9O,IACxBD,EAAKsO,IACH,MAAMU,EAAe,IAAIjC,IAAIuB,EAAMxB,mBAMnC,OALIkC,EAAaC,IAAIhP,GACnB+O,EAAaE,OAAOjP,GAEpB+O,EAAaG,IAAIlP,GAEZ,CAAE6M,kBAAmBkC,MAIhCI,eAAgBA,KACdpP,EAAI,CAAE8M,kBAAmB,IAAIC,OAG/BsC,UAAWA,KACTrP,EAAKsO,IAAK,CACRxB,kBAAmB,IAAIC,IAAIuB,EAAM/M,UAAU1E,IAAK4R,GAAQA,EAAIxO,SAIhEqP,kBAAmBA,CAACC,EAAkBC,KACpCxP,EAAKsO,IAAK,CACRtB,gBAAcuB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAMtB,gBAAc,IAAE,CAACuC,GAAWC,U,sEC3L7D,MAAMC,EAAmBC,IACvB,MAAMC,EAAW,IAAID,GACrB,IAAK,IAAI/T,EAAIgU,EAASlU,OAAS,EAAGE,EAAI,EAAGA,IAAK,CAC5C,MAAMiU,EAAI5V,KAAKC,MAAMD,KAAK6V,UAAYlU,EAAI,KACzCgU,EAAShU,GAAIgU,EAASC,IAAM,CAACD,EAASC,GAAID,EAAShU,GACtD,CACA,OAAOgU,GAmGIxF,GAAgB0C,EAAAA,EAAAA,IAAmB,CAAC7M,EAAK2E,KAAG,CACvDmL,eAAgB,KAChBC,gBAAiB,KACjBtS,UAAW,GACXtC,SAAU,GACVuF,WAAW,EACXqJ,MAAO,KAGPiG,cAAe,GACfC,oBAAqB,EACrBC,SAAS,EACTC,SAAS,EAETC,qBAAsB9F,eAAOjL,GAA8C,IAADgR,EAAA,IAAzBC,EAAYC,UAAA9U,OAAA,QAAA+C,IAAA+R,UAAA,IAAAA,UAAA,GAE3D,MAAM,gBAAER,GAAoBpL,IAC5B,GACE2L,IACCP,IACuB,QAAxBM,EAAAN,EAAgBzP,gBAAQ,IAAA+P,OAAA,EAAxBA,EAA0BpQ,MAAOZ,EACjC,CACAW,EAAI,CAAEU,WAAW,EAAMqJ,MAAO,OAE9B,IACE,MAAMmD,EAAQC,aAAaC,QAAQ,cAE7BC,QAAiBC,MAAM,mBAADlT,OAAoBiF,EAAU,YAAY,CACpEkO,QAAS,CACPC,cAAc,UAADpT,OAAY8S,MAI7B,IAAKG,EAASI,GAAI,CAChB,MAAMY,QAAoBhB,EAASM,OACnC,MAAM,IAAIjD,MACR2D,EAAYtE,OAAS,oCAEzB,CAEA,MAAM2D,QAAeL,EAASM,OAE9B,IAAID,EAAOE,QAUT,MAAM,IAAIlD,MAAMgD,EAAO3D,OATvB/J,EAAI,CACF+P,gBAAiB,CACfzP,SAAUoN,EAAO3I,KAAKzE,SACtBkQ,WAAY9C,EAAO3I,KAAKyL,YAAc,GACtCC,UAAW/C,EAAO3I,KAAK0L,WAAa,IAEtC/P,WAAW,GAKjB,CAAE,MAAOqJ,GAKP,MAJA/J,EAAI,CACF+J,MAAOA,EAAMY,SAAW,oCACxBjK,WAAW,IAEPqJ,CACR,CACF,CACF,EAEA2G,kBAAmBpG,eACjBjL,EACAd,GAEI,IAADoS,EAAAC,EAAAC,EAAA,IADHC,EAAOP,UAAA9U,OAAA,QAAA+C,IAAA+R,UAAA,IAAAA,UAAA,GAEP,MAAM,gBAAER,EAAe,qBAAEK,GAAyBzL,IAG7CoL,IAA2C,QAAxBY,EAAAZ,EAAgBzP,gBAAQ,IAAAqQ,OAAA,EAAxBA,EAA0B1Q,MAAOZ,SACjD+Q,EAAqB/Q,GAG7B,MAAM0R,EAAUpM,IAAMoL,gBACtB,IAAKgB,EACH,MAAM,IAAIrG,MAAM,oCAGlB,MAAM/L,EACK,eAATJ,GACsB,QAAlBqS,EAAAG,EAAQP,kBAAU,IAAAI,OAAA,EAAlBA,EAAoBnV,SAAU,GACb,QAAjBoV,EAAAE,EAAQN,iBAAS,IAAAI,OAAA,EAAjBA,EAAmBpV,SAAU,EAEnC,GAAmB,IAAfkD,EACF,MAAM,IAAI+L,MAAM,wCAIlB,IAAIsG,EACJ,GAAIF,EAKF,GAHAE,EAAgBzV,MAAMC,KAAK,CAAEC,OAAQkD,GAAc,CAACjD,EAAGC,IAAMA,GAGhD,eAAT4C,GAAyBwS,EAAQP,WAAY,CAC/C,MAAMS,EAAqBxB,EAAasB,EAAQP,YAChDxQ,EAAKsO,IAAK,CACRyB,iBAAexB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACVD,EAAMyB,iBAAe,IACxBS,WAAYS,MAGlB,MAAO,GAAa,SAAT1S,GAAmBwS,EAAQN,UAAW,CAC/C,MAAMS,EAAoBzB,EAAasB,EAAQN,WAC/CzQ,EAAKsO,IAAK,CACRyB,iBAAexB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACVD,EAAMyB,iBAAe,IACxBU,UAAWS,MAGjB,CAGFlR,EAAI,CACF8P,eAAgB,CACdzQ,aACAd,OACAjC,UAAW,IAAIT,KACfsV,aAAc,EACdxS,aACAR,cAAe,GACfiT,aAAc,GACd3S,eAAyB,SAATF,EAAkB,OAAIC,EACtCtC,UAAW,EACXmV,WAAYP,EACZE,kBAGN,EAEAM,gBAAiBA,KACftR,EAAI,CAAE8P,eAAgB,QAGxByB,SAAUA,KACR,MAAM,eAAEzB,EAAc,aAAE0B,GAAiB7M,IACzC,IAAKmL,EAAgB,OAGrB,MAAM2B,EACJ3B,EAAeqB,eAAiBrB,EAAenR,WAAa,EACxD,EACAmR,EAAeqB,aAAe,EAGpCK,EAAa,CACXjT,KAAM,YACNmT,QAAS,CAAEC,UAAW7B,EAAeqB,aAAcS,QAASH,GAC5DI,cAAe,CAAEV,aAAcrB,EAAeqB,cAC9CW,UAAWjW,KAAKW,QAGlBwD,EAAI,CACF8P,gBAAcvB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTuB,GAAc,IACjBqB,aAAcM,OAKpBM,aAAcA,KACZ,MAAM,eAAEjC,EAAc,aAAE0B,GAAiB7M,IACzC,IAAKmL,EAAgB,OAGrB,MAAMkC,EAC4B,IAAhClC,EAAeqB,aACXrB,EAAenR,WAAa,EAC5BmR,EAAeqB,aAAe,EAGpCK,EAAa,CACXjT,KAAM,gBACNmT,QAAS,CAAEC,UAAW7B,EAAeqB,aAAcS,QAASI,GAC5DH,cAAe,CAAEV,aAAcrB,EAAeqB,cAC9CW,UAAWjW,KAAKW,QAGlBwD,EAAI,CACF8P,gBAAcvB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTuB,GAAc,IACjBqB,aAAca,OAKpBC,SAAWjV,IACT,MAAM,eAAE8S,GAAmBnL,IAC3B,IAAKmL,EAAgB,OAErB,MAAMoC,EAAelY,KAAK4C,IACxB,EACA5C,KAAK6N,IAAI7K,EAAO8S,EAAenR,WAAa,IAE9CqB,EAAI,CACF8P,gBAAcvB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTuB,GAAc,IACjBqB,aAAce,OAKpBC,WAAaC,IACX,MAAM,eAAEtC,EAAc,aAAE0B,GAAiB7M,IACzC,IAAKmL,EAAgB,OAErB,MAAMuC,EAAavC,EAAesB,aAAakB,SAASF,GAClDhB,EAAeiB,EACjBvC,EAAesB,aAAa/S,OAAQ4B,GAAOA,IAAOmS,GAClD,IAAItC,EAAesB,aAAcgB,GAGrCZ,EAAa,CACXjT,KAAM,cACNmT,QAAS,CAAEU,SAAQC,cACnBR,cAAe,CAAET,aAActB,EAAesB,cAC9CU,UAAWjW,KAAKW,QAGlBwD,EAAI,CACF8P,gBAAcvB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTuB,GAAc,IACjBsB,oBAKNmB,aAAeC,IACb,MAAM,eAAE1C,GAAmBnL,IACtBmL,IAEAA,EAAe3R,cAAcmU,SAASxC,EAAeqB,eACxDnR,EAAI,CACF8P,gBAAcvB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTuB,GAAc,IACjB3R,cAAe,IACV2R,EAAe3R,cAClB2R,EAAeqB,oBAOzBsB,iBAAkBA,CAChBC,EACAC,EACAC,KAEA,MAAM,eAAE9C,EAAc,aAAEyC,GAAiB5N,IACpCmL,GAA0C,SAAxBA,EAAevR,OAEtCgU,EAAaG,GAETE,GACF5S,EAAI,CACF8P,gBAAcvB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTuB,GAAc,IACjBrR,gBAAiBqR,EAAerR,gBAAkB,GAAK,QAM/DoU,gBAAkB/Y,IAChB,MAAM,eAAEgW,GAAmBnL,IACtBmL,GAEL9P,EAAI,CACF8P,gBAAcvB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTuB,GAAc,IACjB5T,UAAW4T,EAAe5T,UAAYpC,OAM5C0X,aAAesB,IACb,MAAM,cAAE9C,EAAa,mBAAEC,GAAuBtL,IAGxCoO,EAAa/C,EAAc5P,MAAM,EAAG6P,EAAqB,GAC/D8C,EAAWC,KAAKF,GAGhB,MAAMG,EAAiBF,EAAW3S,OAAO,IAEzCJ,EAAI,CACFgQ,cAAeiD,EACfhD,mBAAoBgD,EAAexX,OAAS,EAC5CyU,QAAS+C,EAAexX,OAAS,EACjC0U,SAAS,KAIb+C,KAAMA,KACJ,MAAM,cAAElD,EAAa,mBAAEC,EAAkB,eAAEH,GAAmBnL,IAE9D,GAAIsL,EAAqB,IAAMH,EAAgB,OAE/C,MAAMgD,EAAS9C,EAAcC,GAG7BjQ,EAAI,CACF8P,gBAAcvB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTuB,GACAgD,EAAOjB,eAEZ5B,mBAAoBA,EAAqB,EACzCC,QAASD,EAAqB,EAC9BE,SAAS,KAIbgD,KAAMA,KACJ,MAAM,cAAEnD,EAAa,mBAAEC,EAAkB,eAAEH,GAAmBnL,IAE9D,GAAIsL,GAAsBD,EAAcvU,OAAS,IAAMqU,EACrD,OAEF,MAAMsD,EAAkBnD,EAAqB,EACvC6C,EAAS9C,EAAcoD,GAG7B,OAAQN,EAAOvU,MACb,IAAK,YACHoG,IAAM4M,WACN,MACF,IAAK,gBACH5M,IAAMoN,eACN,MACF,IAAK,cACHpN,IAAMwN,WAAWW,EAAOpB,QAAQU,QAChC,MACF,IAAK,gBACHzN,IAAM4N,aAAaO,EAAOpB,QAAQU,QAItCpS,EAAI,CACFiQ,mBAAoBmD,EACpBlD,SAAS,EACTC,QAASiD,EAAkBpD,EAAcvU,OAAS,KAItD4X,aAAcA,KACZrT,EAAI,CACFgQ,cAAe,GACfC,oBAAqB,EACrBC,SAAS,EACTC,SAAS,KAIblG,eAAgBK,UACdtK,EAAI,CAAEU,WAAW,EAAMqJ,MAAO,OAE9B,IACE,MAAMmD,EAAQC,aAAaC,QAAQ,cAE7BC,QAAiBC,MAAM,kBAAmB,CAC9CC,QAAS,CACPC,cAAc,UAADpT,OAAY8S,MAI7B,IAAKG,EAASI,GAAI,CAChB,MAAMY,QAAoBhB,EAASM,OACnC,MAAM,IAAIjD,MAAM2D,EAAYtE,OAAS,6BACvC,CAEA,MAAM2D,QAAeL,EAASM,OAE9B,IAAID,EAAOE,QAMT,MAAM,IAAIlD,MAAMgD,EAAO3D,OALvB/J,EAAI,CACFvC,UAAWiQ,EAAO3I,KAClBrE,WAAW,GAKjB,CAAE,MAAOqJ,GAKP,MAJA/J,EAAI,CACF+J,MAAOA,EAAMY,SAAW,6BACxBjK,WAAW,IAEPqJ,CACR,GAGFG,mBAAoBI,iBAA8B,IAAvB9I,EAAS+O,UAAA9U,OAAA,QAAA+C,IAAA+R,UAAA,GAAAA,UAAA,GAAG,MACrCvQ,EAAI,CAAEU,WAAW,EAAMqJ,MAAO,OAE9B,IACE,MAAMmD,EAAQC,aAAaC,QAAQ,cAE7BC,QAAiBC,MAAM,iCAADlT,OACOoH,GACjC,CACE+L,QAAS,CACPC,cAAc,UAADpT,OAAY8S,MAK/B,IAAKG,EAASI,GAAI,CAChB,MAAMY,QAAoBhB,EAASM,OACnC,MAAM,IAAIjD,MAAM2D,EAAYtE,OAAS,iCACvC,CAEA,MAAM2D,QAAeL,EAASM,OAE9B,IAAID,EAAOE,QAaT,MAAM,IAAIlD,MAAMgD,EAAO3D,OAbL,CAElB,MAAM5O,EAAWuS,EAAO3I,KAAKlI,IAAKT,IAAYmS,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACzCnS,GAAO,IACVE,UAAW,IAAIT,KAAKO,EAAQE,WAC5BgX,QAASlX,EAAQkX,QAAU,IAAIzX,KAAKO,EAAQkX,cAAW9U,KAGzDwB,EAAI,CACF7E,WACAuF,WAAW,GAEf,CAGF,CAAE,MAAOqJ,GAKP,MAJA/J,EAAI,CACF+J,MAAOA,EAAMY,SAAW,iCACxBjK,WAAW,IAEPqJ,CACR,CACF,EAGAwJ,0BAA4BlU,IAC1B,MAAM,gBAAE0Q,GAAoBpL,IAEX,IAAD6O,EAAZnU,GAEiB,OAAf0Q,QAAe,IAAfA,GAAyB,QAAVyD,EAAfzD,EAAiBzP,gBAAQ,IAAAkT,OAAV,EAAfA,EAA2BvT,MAAOZ,GACpCW,EAAI,CAAE+P,gBAAiB,OAIzB/P,EAAI,CAAE+P,gBAAiB,QAI3B0D,uBAAwBnJ,gBAEhB3F,IAAMyL,qBAAqB/Q,GAAY,IAG/CqU,oBAAqBA,KAEnB1T,EAAI,CAAEvC,UAAW,Q", "sources": ["components/analytics/StudyAnalytics.tsx", "components/analytics/AnalyticsDashboard.tsx", "components/analytics/PerformanceMetrics.tsx", "components/analytics/StudyTrends.tsx", "pages/AnalyticsPage.tsx", "stores/documentStore.ts", "stores/studyStore.ts"], "sourcesContent": ["import React, { memo, useMemo } from 'react';\r\nimport { StudySet } from '../../../../shared/types';\r\n\r\ninterface StudySession {\r\n  id: string;\r\n  studySetId: string;\r\n  type: 'flashcards' | 'quiz';\r\n  startTime: Date;\r\n  endTime?: Date;\r\n  totalItems: number;\r\n  reviewedItems: number;\r\n  flaggedItems: number;\r\n  correctAnswers?: number;\r\n  timeSpent: number; // in seconds\r\n}\r\n\r\ninterface StudyAnalyticsProps {\r\n  studySets: StudySet[];\r\n  sessions: StudySession[];\r\n}\r\n\r\ninterface AnalyticsData {\r\n  totalStudyTime: number;\r\n  totalSessions: number;\r\n  averageSessionTime: number;\r\n  totalItemsReviewed: number;\r\n  averageAccuracy: number;\r\n  studyStreak: number;\r\n  mostStudiedSet: string;\r\n  recentActivity: StudySession[];\r\n}\r\n\r\nconst formatTime = (seconds: number): string => {\r\n  const hours = Math.floor(seconds / 3600);\r\n  const minutes = Math.floor((seconds % 3600) / 60);\r\n  const secs = seconds % 60;\r\n  \r\n  if (hours > 0) {\r\n    return `${hours}h ${minutes}m`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes}m ${secs}s`;\r\n  } else {\r\n    return `${secs}s`;\r\n  }\r\n};\r\n\r\nconst StatCard: React.FC<{\r\n  title: string;\r\n  value: string | number;\r\n  subtitle?: string;\r\n  icon?: string;\r\n}> = memo(({ title, value, subtitle, icon }) => (\r\n  <div className=\"bg-background-secondary border border-gray-600 rounded-lg p-6\">\r\n    <div className=\"flex items-center justify-between\">\r\n      <div>\r\n        <p className=\"text-sm text-gray-400\">{title}</p>\r\n        <p className=\"text-2xl font-semibold text-white mt-1\">{value}</p>\r\n        {subtitle && <p className=\"text-xs text-gray-500 mt-1\">{subtitle}</p>}\r\n      </div>\r\n      {icon && <div className=\"text-2xl\">{icon}</div>}\r\n    </div>\r\n  </div>\r\n));\r\n\r\nStatCard.displayName = 'StatCard';\r\n\r\nconst ProgressChart: React.FC<{\r\n  sessions: StudySession[];\r\n}> = memo(({ sessions }) => {\r\n  const chartData = useMemo(() => {\r\n    const last7Days = Array.from({ length: 7 }, (_, i) => {\r\n      const date = new Date();\r\n      date.setDate(date.getDate() - (6 - i));\r\n      return {\r\n        date: date.toLocaleDateString('en-US', { weekday: 'short' }),\r\n        sessions: 0,\r\n        timeSpent: 0\r\n      };\r\n    });\r\n\r\n    sessions.forEach(session => {\r\n      const sessionDate = new Date(session.startTime);\r\n      const dayIndex = Math.floor((Date.now() - sessionDate.getTime()) / (1000 * 60 * 60 * 24));\r\n\r\n      if (dayIndex >= 0 && dayIndex < 7) {\r\n        const chartIndex = 6 - dayIndex;\r\n        last7Days[chartIndex].sessions += 1;\r\n        last7Days[chartIndex].timeSpent += session.timeSpent;\r\n      }\r\n    });\r\n\r\n    return last7Days;\r\n  }, [sessions]);\r\n\r\n  const maxTime = Math.max(...chartData.map(d => d.timeSpent));\r\n\r\n  return (\r\n    <div className=\"bg-background-secondary border border-gray-600 rounded-lg p-6\">\r\n      <h3 className=\"text-lg font-semibold text-white mb-4\">Study Activity (Last 7 Days)</h3>\r\n      <div className=\"flex items-end justify-between h-32 space-x-2\">\r\n        {chartData.map((day, index) => (\r\n          <div key={index} className=\"flex-1 flex flex-col items-center\">\r\n            <div className=\"w-full bg-gray-700 rounded-t relative\" style={{ height: '100px' }}>\r\n              <div\r\n                className=\"bg-primary-500 rounded-t transition-all duration-300\"\r\n                style={{\r\n                  height: maxTime > 0 ? `${(day.timeSpent / maxTime) * 100}%` : '0%',\r\n                  position: 'absolute',\r\n                  bottom: 0,\r\n                  left: 0,\r\n                  right: 0\r\n                }}\r\n                title={`${day.sessions} sessions, ${formatTime(day.timeSpent)}`}\r\n              />\r\n            </div>\r\n            <div className=\"text-xs text-gray-400 mt-2\">{day.date}</div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nProgressChart.displayName = 'ProgressChart';\r\n\r\nexport const StudyAnalytics: React.FC<StudyAnalyticsProps> = memo(({\r\n  studySets,\r\n  sessions\r\n}) => {\r\n  const analytics = useMemo((): AnalyticsData => {\r\n    const totalStudyTime = sessions.reduce((sum, session) => sum + session.timeSpent, 0);\r\n    const totalSessions = sessions.length;\r\n    const averageSessionTime = totalSessions > 0 ? totalStudyTime / totalSessions : 0;\r\n    const totalItemsReviewed = sessions.reduce((sum, session) => sum + session.reviewedItems, 0);\r\n    \r\n    const quizSessions = sessions.filter(s => s.type === 'quiz' && s.correctAnswers !== undefined);\r\n    const totalQuizItems = quizSessions.reduce((sum, session) => sum + session.totalItems, 0);\r\n    const totalCorrect = quizSessions.reduce((sum, session) => sum + (session.correctAnswers || 0), 0);\r\n    const averageAccuracy = totalQuizItems > 0 ? (totalCorrect / totalQuizItems) * 100 : 0;\r\n\r\n    // Calculate study streak (consecutive days with sessions)\r\n    const today = new Date();\r\n    let studyStreak = 0;\r\n    for (let i = 0; i < 365; i++) {\r\n      const checkDate = new Date(today);\r\n      checkDate.setDate(today.getDate() - i);\r\n      \r\n      const hasSessionOnDate = sessions.some(session => {\r\n        const sessionDate = new Date(session.startTime);\r\n        return sessionDate.toDateString() === checkDate.toDateString();\r\n      });\r\n      \r\n      if (hasSessionOnDate) {\r\n        studyStreak++;\r\n      } else if (i > 0) { // Don't break on first day (today) if no session\r\n        break;\r\n      }\r\n    }\r\n\r\n    // Find most studied set\r\n    const setStudyCount = sessions.reduce((acc, session) => {\r\n      acc[session.studySetId] = (acc[session.studySetId] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>);\r\n    \r\n    const mostStudiedSetId = Object.entries(setStudyCount)\r\n      .sort(([,a], [,b]) => b - a)[0]?.[0];\r\n    \r\n    const mostStudiedSet = studySets.find(set => set.id === mostStudiedSetId)?.name || 'None';\r\n\r\n    const recentActivity = sessions\r\n      .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())\r\n      .slice(0, 5);\r\n\r\n    return {\r\n      totalStudyTime,\r\n      totalSessions,\r\n      averageSessionTime,\r\n      totalItemsReviewed,\r\n      averageAccuracy,\r\n      studyStreak,\r\n      mostStudiedSet,\r\n      recentActivity\r\n    };\r\n  }, [sessions, studySets]);\r\n\r\n  return (\r\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n      <div className=\"mb-8\">\r\n        <h1 className=\"text-3xl font-bold text-white mb-2\">Study Analytics</h1>\r\n        <p className=\"text-gray-400\">Track your learning progress and performance</p>\r\n      </div>\r\n\r\n      {/* Stats Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n        <StatCard\r\n          title=\"Total Study Time\"\r\n          value={formatTime(analytics.totalStudyTime)}\r\n          icon=\"⏱️\"\r\n        />\r\n        <StatCard\r\n          title=\"Study Sessions\"\r\n          value={analytics.totalSessions}\r\n          subtitle=\"All time\"\r\n          icon=\"📚\"\r\n        />\r\n        <StatCard\r\n          title=\"Average Session\"\r\n          value={formatTime(Math.round(analytics.averageSessionTime))}\r\n          icon=\"⏰\"\r\n        />\r\n        <StatCard\r\n          title=\"Study Streak\"\r\n          value={`${analytics.studyStreak} days`}\r\n          icon=\"🔥\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\r\n        <StatCard\r\n          title=\"Items Reviewed\"\r\n          value={analytics.totalItemsReviewed}\r\n          subtitle=\"Flashcards & questions\"\r\n          icon=\"✅\"\r\n        />\r\n        <StatCard\r\n          title=\"Quiz Accuracy\"\r\n          value={`${Math.round(analytics.averageAccuracy)}%`}\r\n          subtitle=\"Average across all quizzes\"\r\n          icon=\"🎯\"\r\n        />\r\n      </div>\r\n\r\n      {/* Progress Chart */}\r\n      <div className=\"mb-8\">\r\n        <ProgressChart sessions={sessions} />\r\n      </div>\r\n\r\n      {/* Recent Activity */}\r\n      <div className=\"bg-background-secondary border border-gray-600 rounded-lg p-6\">\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Recent Activity</h3>\r\n        {analytics.recentActivity.length === 0 ? (\r\n          <p className=\"text-gray-400\">No recent study sessions</p>\r\n        ) : (\r\n          <div className=\"space-y-3\">\r\n            {analytics.recentActivity.map((session, index) => {\r\n              const studySet = studySets.find(set => set.id === session.studySetId);\r\n              return (\r\n                <div key={index} className=\"flex items-center justify-between py-2 border-b border-gray-700 last:border-b-0\">\r\n                  <div>\r\n                    <p className=\"text-white font-medium\">{studySet?.name || 'Unknown Set'}</p>\r\n                    <p className=\"text-sm text-gray-400\">\r\n                      {session.type === 'quiz' ? 'Quiz' : 'Flashcards'} • {session.reviewedItems}/{session.totalItems} items\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"text-right\">\r\n                    <p className=\"text-sm text-gray-300\">{formatTime(session.timeSpent)}</p>\r\n                    <p className=\"text-xs text-gray-500\">\r\n                      {new Date(session.startTime).toLocaleDateString()}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nStudyAnalytics.displayName = 'StudyAnalytics';\r\n", "import React, { useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { \r\n  HiAcademicCap, \r\n  HiClock, \r\n  HiT<PERSON>dingUp, \r\n  <PERSON><PERSON>oll<PERSON><PERSON>,\r\n  HiDocumentT<PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  HiChart<PERSON>ie\r\n} from 'react-icons/hi';\r\nimport { StudySet, DocumentMetadata } from '../../../../shared/types';\r\n\r\ninterface StudySession {\r\n  id: string;\r\n  studySetId: string;\r\n  type: 'flashcards' | 'quiz';\r\n  startTime: Date;\r\n  endTime?: Date;\r\n  totalItems: number;\r\n  reviewedItems: number;\r\n  flaggedItems: number;\r\n  correctAnswers?: number;\r\n  timeSpent: number;\r\n}\r\n\r\ninterface AnalyticsDashboardProps {\r\n  studySets: StudySet[];\r\n  sessions: StudySession[];\r\n  documents: DocumentMetadata[];\r\n  timeRange: '7d' | '30d' | '90d' | 'all';\r\n  isLoading: boolean;\r\n}\r\n\r\ninterface DashboardMetrics {\r\n  totalStudySets: number;\r\n  totalDocuments: number;\r\n  totalSessions: number;\r\n  totalStudyTime: number;\r\n  averageSessionTime: number;\r\n  completionRate: number;\r\n  aiGeneratedSets: number;\r\n  recentActivity: number;\r\n}\r\n\r\nconst formatTime = (seconds: number): string => {\r\n  const hours = Math.floor(seconds / 3600);\r\n  const minutes = Math.floor((seconds % 3600) / 60);\r\n  \r\n  if (hours > 0) {\r\n    return `${hours}h ${minutes}m`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes}m`;\r\n  } else {\r\n    return `${seconds}s`;\r\n  }\r\n};\r\n\r\nconst MetricCard: React.FC<{\r\n  title: string;\r\n  value: string | number;\r\n  subtitle?: string;\r\n  icon: React.ComponentType<{ className?: string }>;\r\n  trend?: { value: number; isPositive: boolean };\r\n  isLoading?: boolean;\r\n}> = ({ title, value, subtitle, icon: Icon, trend, isLoading }) => (\r\n  <motion.div\r\n    initial={{ opacity: 0, y: 20 }}\r\n    animate={{ opacity: 1, y: 0 }}\r\n    transition={{ duration: 0.3 }}\r\n    className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\"\r\n  >\r\n    <div className=\"flex items-center justify-between mb-4\">\r\n      <div className=\"p-3 bg-primary-500/20 rounded-lg\">\r\n        <Icon className=\"w-6 h-6 text-primary-400\" />\r\n      </div>\r\n      \r\n      {trend && (\r\n        <div className={`flex items-center space-x-1 ${\r\n          trend.isPositive ? 'text-green-400' : 'text-red-400'\r\n        }`}>\r\n          <HiTrendingUp className={`w-4 h-4 ${trend.isPositive ? '' : 'rotate-180'}`} />\r\n          <span className=\"text-sm font-medium\">{Math.abs(trend.value)}%</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n\r\n    <div>\r\n      <h3 className=\"text-sm font-medium text-gray-400 mb-1\">{title}</h3>\r\n      {isLoading ? (\r\n        <div className=\"animate-pulse\">\r\n          <div className=\"h-8 w-20 bg-gray-600 rounded\"></div>\r\n        </div>\r\n      ) : (\r\n        <p className=\"text-2xl font-bold text-white\">{value}</p>\r\n      )}\r\n      {subtitle && (\r\n        <p className=\"text-xs text-gray-500 mt-1\">{subtitle}</p>\r\n      )}\r\n    </div>\r\n  </motion.div>\r\n);\r\n\r\nexport const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({\r\n  studySets,\r\n  sessions,\r\n  documents,\r\n  timeRange,\r\n  isLoading\r\n}) => {\r\n  const metrics = useMemo((): DashboardMetrics => {\r\n    const now = new Date();\r\n    const timeRangeMs = {\r\n      '7d': 7 * 24 * 60 * 60 * 1000,\r\n      '30d': 30 * 24 * 60 * 60 * 1000,\r\n      '90d': 90 * 24 * 60 * 60 * 1000,\r\n      'all': Infinity\r\n    }[timeRange];\r\n\r\n    const filteredSessions = sessions.filter(session => {\r\n      const sessionTime = new Date(session.startTime).getTime();\r\n      return now.getTime() - sessionTime <= timeRangeMs;\r\n    });\r\n\r\n    const totalStudyTime = filteredSessions.reduce((sum, session) => sum + session.timeSpent, 0);\r\n    const totalSessions = filteredSessions.length;\r\n    const averageSessionTime = totalSessions > 0 ? totalStudyTime / totalSessions : 0;\r\n\r\n    const totalItems = filteredSessions.reduce((sum, session) => sum + session.totalItems, 0);\r\n    const reviewedItems = filteredSessions.reduce((sum, session) => sum + session.reviewedItems, 0);\r\n    const completionRate = totalItems > 0 ? (reviewedItems / totalItems) * 100 : 0;\r\n\r\n    const aiGeneratedSets = studySets.filter(set => set.is_ai_generated).length;\r\n\r\n    // Recent activity (sessions in last 24 hours)\r\n    const last24Hours = 24 * 60 * 60 * 1000;\r\n    const recentActivity = sessions.filter(session => {\r\n      const sessionTime = new Date(session.startTime).getTime();\r\n      return now.getTime() - sessionTime <= last24Hours;\r\n    }).length;\r\n\r\n    return {\r\n      totalStudySets: studySets.length,\r\n      totalDocuments: documents.length,\r\n      totalSessions,\r\n      totalStudyTime,\r\n      averageSessionTime,\r\n      completionRate,\r\n      aiGeneratedSets,\r\n      recentActivity\r\n    };\r\n  }, [studySets, sessions, documents, timeRange]);\r\n\r\n  // Calculate trends (compare with previous period)\r\n  const trends = useMemo(() => {\r\n    const now = new Date();\r\n    const timeRangeMs = {\r\n      '7d': 7 * 24 * 60 * 60 * 1000,\r\n      '30d': 30 * 24 * 60 * 60 * 1000,\r\n      '90d': 90 * 24 * 60 * 60 * 1000,\r\n      'all': Infinity\r\n    }[timeRange];\r\n\r\n    if (timeRangeMs === Infinity) return {};\r\n\r\n    const currentPeriodStart = now.getTime() - timeRangeMs;\r\n    const previousPeriodStart = currentPeriodStart - timeRangeMs;\r\n\r\n    const currentSessions = sessions.filter(session => {\r\n      const sessionTime = new Date(session.startTime).getTime();\r\n      return sessionTime >= currentPeriodStart;\r\n    });\r\n\r\n    const previousSessions = sessions.filter(session => {\r\n      const sessionTime = new Date(session.startTime).getTime();\r\n      return sessionTime >= previousPeriodStart && sessionTime < currentPeriodStart;\r\n    });\r\n\r\n    const currentTime = currentSessions.reduce((sum, session) => sum + session.timeSpent, 0);\r\n    const previousTime = previousSessions.reduce((sum, session) => sum + session.timeSpent, 0);\r\n\r\n    const timeTrend = previousTime > 0 ? ((currentTime - previousTime) / previousTime) * 100 : 0;\r\n    const sessionsTrend = previousSessions.length > 0 ? \r\n      ((currentSessions.length - previousSessions.length) / previousSessions.length) * 100 : 0;\r\n\r\n    return {\r\n      studyTime: { value: Math.round(timeTrend), isPositive: timeTrend >= 0 },\r\n      sessions: { value: Math.round(sessionsTrend), isPositive: sessionsTrend >= 0 }\r\n    };\r\n  }, [sessions, timeRange]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Main Metrics Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n        <MetricCard\r\n          title=\"Study Sets\"\r\n          value={metrics.totalStudySets}\r\n          subtitle=\"Total created\"\r\n          icon={HiCollection}\r\n          isLoading={isLoading}\r\n        />\r\n        \r\n        <MetricCard\r\n          title=\"Documents\"\r\n          value={metrics.totalDocuments}\r\n          subtitle=\"Uploaded files\"\r\n          icon={HiDocumentText}\r\n          isLoading={isLoading}\r\n        />\r\n        \r\n        <MetricCard\r\n          title=\"Study Time\"\r\n          value={formatTime(metrics.totalStudyTime)}\r\n          subtitle={`${timeRange === 'all' ? 'All time' : `Last ${timeRange.replace('d', ' days')}`}`}\r\n          icon={HiClock}\r\n          trend={trends.studyTime}\r\n          isLoading={isLoading}\r\n        />\r\n        \r\n        <MetricCard\r\n          title=\"Sessions\"\r\n          value={metrics.totalSessions}\r\n          subtitle={`${timeRange === 'all' ? 'All time' : `Last ${timeRange.replace('d', ' days')}`}`}\r\n          icon={HiAcademicCap}\r\n          trend={trends.sessions}\r\n          isLoading={isLoading}\r\n        />\r\n      </div>\r\n\r\n      {/* Secondary Metrics */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n        <MetricCard\r\n          title=\"Avg Session\"\r\n          value={formatTime(Math.round(metrics.averageSessionTime))}\r\n          subtitle=\"Time per session\"\r\n          icon={HiClock}\r\n          isLoading={isLoading}\r\n        />\r\n        \r\n        <MetricCard\r\n          title=\"Completion Rate\"\r\n          value={`${Math.round(metrics.completionRate)}%`}\r\n          subtitle=\"Items reviewed\"\r\n          icon={HiChartPie}\r\n          isLoading={isLoading}\r\n        />\r\n        \r\n        <MetricCard\r\n          title=\"AI Generated\"\r\n          value={metrics.aiGeneratedSets}\r\n          subtitle=\"Study sets created by AI\"\r\n          icon={HiSparkles}\r\n          isLoading={isLoading}\r\n        />\r\n      </div>\r\n\r\n      {/* Quick Stats */}\r\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Quick Stats</h3>\r\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-primary-400\">{metrics.recentActivity}</div>\r\n            <div className=\"text-sm text-gray-400\">Sessions today</div>\r\n          </div>\r\n          \r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-green-400\">\r\n              {studySets.filter(set => set.type === 'flashcards').length}\r\n            </div>\r\n            <div className=\"text-sm text-gray-400\">Flashcard sets</div>\r\n          </div>\r\n          \r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-blue-400\">\r\n              {studySets.filter(set => set.type === 'quiz').length}\r\n            </div>\r\n            <div className=\"text-sm text-gray-400\">Quiz sets</div>\r\n          </div>\r\n          \r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-purple-400\">\r\n              {Math.round((metrics.aiGeneratedSets / Math.max(metrics.totalStudySets, 1)) * 100)}%\r\n            </div>\r\n            <div className=\"text-sm text-gray-400\">AI generated</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  HiTrendingUp,\r\n  HiTrendingDown,\r\n  HiCheckCircle,\r\n  HiLightningBolt,\r\n  HiChartBar\r\n} from 'react-icons/hi';\r\nimport { StudySet } from '../../../../shared/types';\r\n\r\ninterface StudySession {\r\n  id: string;\r\n  studySetId: string;\r\n  type: 'flashcards' | 'quiz';\r\n  startTime: Date;\r\n  endTime?: Date;\r\n  totalItems: number;\r\n  reviewedItems: number;\r\n  flaggedItems: number;\r\n  correctAnswers?: number;\r\n  timeSpent: number;\r\n}\r\n\r\ninterface PerformanceMetricsProps {\r\n  studySets: StudySet[];\r\n  sessions: StudySession[];\r\n  timeRange: '7d' | '30d' | '90d' | 'all';\r\n  isLoading: boolean;\r\n}\r\n\r\ninterface PerformanceData {\r\n  overallAccuracy: number;\r\n  accuracyTrend: number;\r\n  averageSpeed: number; // items per minute\r\n  speedTrend: number;\r\n  consistencyScore: number;\r\n  improvementRate: number;\r\n  strongestSubjects: Array<{ name: string; accuracy: number }>;\r\n  weakestSubjects: Array<{ name: string; accuracy: number }>;\r\n  dailyPerformance: Array<{ date: string; accuracy: number; speed: number }>;\r\n}\r\n\r\nconst formatSpeed = (itemsPerMinute: number): string => {\r\n  if (itemsPerMinute < 1) {\r\n    return `${Math.round(itemsPerMinute * 60)}s/item`;\r\n  }\r\n  return `${itemsPerMinute.toFixed(1)} items/min`;\r\n};\r\n\r\nconst PerformanceCard: React.FC<{\r\n  title: string;\r\n  value: string | number;\r\n  subtitle?: string;\r\n  icon: React.ComponentType<{ className?: string }>;\r\n  trend?: number;\r\n  isLoading?: boolean;\r\n  color?: 'primary' | 'green' | 'red' | 'yellow';\r\n}> = ({ title, value, subtitle, icon: Icon, trend, isLoading, color = 'primary' }) => {\r\n  const colorClasses = {\r\n    primary: 'bg-primary-500/20 text-primary-400',\r\n    green: 'bg-green-500/20 text-green-400',\r\n    red: 'bg-red-500/20 text-red-400',\r\n    yellow: 'bg-yellow-500/20 text-yellow-400'\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3 }}\r\n      className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\"\r\n    >\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>\r\n          <Icon className=\"w-6 h-6\" />\r\n        </div>\r\n        \r\n        {trend !== undefined && (\r\n          <div className={`flex items-center space-x-1 ${\r\n            trend >= 0 ? 'text-green-400' : 'text-red-400'\r\n          }`}>\r\n            {trend >= 0 ? (\r\n              <HiTrendingUp className=\"w-4 h-4\" />\r\n            ) : (\r\n              <HiTrendingDown className=\"w-4 h-4\" />\r\n            )}\r\n            <span className=\"text-sm font-medium\">{Math.abs(trend).toFixed(1)}%</span>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div>\r\n        <h3 className=\"text-sm font-medium text-gray-400 mb-1\">{title}</h3>\r\n        {isLoading ? (\r\n          <div className=\"animate-pulse\">\r\n            <div className=\"h-8 w-20 bg-gray-600 rounded\"></div>\r\n          </div>\r\n        ) : (\r\n          <p className=\"text-2xl font-bold text-white\">{value}</p>\r\n        )}\r\n        {subtitle && (\r\n          <p className=\"text-xs text-gray-500 mt-1\">{subtitle}</p>\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nconst SubjectPerformanceChart: React.FC<{\r\n  subjects: Array<{ name: string; accuracy: number }>;\r\n  title: string;\r\n  color: string;\r\n}> = ({ subjects, title, color }) => (\r\n  <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n    <h3 className=\"text-lg font-semibold text-white mb-4\">{title}</h3>\r\n    <div className=\"space-y-3\">\r\n      {subjects.length === 0 ? (\r\n        <p className=\"text-gray-400 text-center py-4\">No data available</p>\r\n      ) : (\r\n        subjects.map((subject, index) => (\r\n          <div key={index} className=\"flex items-center justify-between\">\r\n            <span className=\"text-gray-300 truncate flex-1 mr-4\">{subject.name}</span>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-20 bg-gray-700 rounded-full h-2\">\r\n                <div\r\n                  className={`h-2 rounded-full ${color}`}\r\n                  style={{ width: `${subject.accuracy}%` }}\r\n                />\r\n              </div>\r\n              <span className=\"text-white font-medium w-12 text-right\">\r\n                {Math.round(subject.accuracy)}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n        ))\r\n      )}\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({\r\n  studySets,\r\n  sessions,\r\n  timeRange,\r\n  isLoading\r\n}) => {\r\n  const performanceData = useMemo((): PerformanceData => {\r\n    const now = new Date();\r\n    const timeRangeMs = {\r\n      '7d': 7 * 24 * 60 * 60 * 1000,\r\n      '30d': 30 * 24 * 60 * 60 * 1000,\r\n      '90d': 90 * 24 * 60 * 60 * 1000,\r\n      'all': Infinity\r\n    }[timeRange];\r\n\r\n    const filteredSessions = sessions.filter(session => {\r\n      const sessionTime = new Date(session.startTime).getTime();\r\n      return now.getTime() - sessionTime <= timeRangeMs;\r\n    });\r\n\r\n    // Calculate overall accuracy\r\n    const quizSessions = filteredSessions.filter(s => s.type === 'quiz' && s.correctAnswers !== undefined);\r\n    const totalQuizItems = quizSessions.reduce((sum, session) => sum + session.totalItems, 0);\r\n    const totalCorrect = quizSessions.reduce((sum, session) => sum + (session.correctAnswers || 0), 0);\r\n    const overallAccuracy = totalQuizItems > 0 ? (totalCorrect / totalQuizItems) * 100 : 0;\r\n\r\n    // Calculate average speed (items per minute)\r\n    const totalItems = filteredSessions.reduce((sum, session) => sum + session.reviewedItems, 0);\r\n    const totalTimeMinutes = filteredSessions.reduce((sum, session) => sum + session.timeSpent, 0) / 60;\r\n    const averageSpeed = totalTimeMinutes > 0 ? totalItems / totalTimeMinutes : 0;\r\n\r\n    // Calculate subject performance\r\n    const subjectPerformance = new Map<string, { correct: number; total: number }>();\r\n    \r\n    quizSessions.forEach(session => {\r\n      const studySet = studySets.find(set => set.id === session.studySetId);\r\n      if (studySet) {\r\n        const existing = subjectPerformance.get(studySet.name) || { correct: 0, total: 0 };\r\n        subjectPerformance.set(studySet.name, {\r\n          correct: existing.correct + (session.correctAnswers || 0),\r\n          total: existing.total + session.totalItems\r\n        });\r\n      }\r\n    });\r\n\r\n    const subjectAccuracies = Array.from(subjectPerformance.entries())\r\n      .map(([name, data]) => ({\r\n        name,\r\n        accuracy: data.total > 0 ? (data.correct / data.total) * 100 : 0\r\n      }))\r\n      .sort((a, b) => b.accuracy - a.accuracy);\r\n\r\n    const strongestSubjects = subjectAccuracies.slice(0, 5);\r\n    const weakestSubjects = subjectAccuracies.slice(-5).reverse();\r\n\r\n    // Calculate daily performance for the last 7 days\r\n    const dailyPerformance = Array.from({ length: 7 }, (_, i) => {\r\n      const date = new Date();\r\n      date.setDate(date.getDate() - (6 - i));\r\n      const dateStr = date.toISOString().split('T')[0];\r\n      \r\n      const daySessions = filteredSessions.filter(session => {\r\n        const sessionDate = new Date(session.startTime).toISOString().split('T')[0];\r\n        return sessionDate === dateStr;\r\n      });\r\n\r\n      const dayQuizSessions = daySessions.filter(s => s.type === 'quiz' && s.correctAnswers !== undefined);\r\n      const dayTotalItems = dayQuizSessions.reduce((sum, session) => sum + session.totalItems, 0);\r\n      const dayCorrect = dayQuizSessions.reduce((sum, session) => sum + (session.correctAnswers || 0), 0);\r\n      const dayAccuracy = dayTotalItems > 0 ? (dayCorrect / dayTotalItems) * 100 : 0;\r\n\r\n      const dayReviewedItems = daySessions.reduce((sum, session) => sum + session.reviewedItems, 0);\r\n      const dayTimeMinutes = daySessions.reduce((sum, session) => sum + session.timeSpent, 0) / 60;\r\n      const daySpeed = dayTimeMinutes > 0 ? dayReviewedItems / dayTimeMinutes : 0;\r\n\r\n      return {\r\n        date: date.toLocaleDateString('en-US', { weekday: 'short' }),\r\n        accuracy: dayAccuracy,\r\n        speed: daySpeed\r\n      };\r\n    });\r\n\r\n    // Calculate trends (simplified)\r\n    const recentAccuracy = dailyPerformance.slice(-3).reduce((sum, day) => sum + day.accuracy, 0) / 3;\r\n    const earlierAccuracy = dailyPerformance.slice(0, 3).reduce((sum, day) => sum + day.accuracy, 0) / 3;\r\n    const accuracyTrend = earlierAccuracy > 0 ? ((recentAccuracy - earlierAccuracy) / earlierAccuracy) * 100 : 0;\r\n\r\n    const recentSpeed = dailyPerformance.slice(-3).reduce((sum, day) => sum + day.speed, 0) / 3;\r\n    const earlierSpeed = dailyPerformance.slice(0, 3).reduce((sum, day) => sum + day.speed, 0) / 3;\r\n    const speedTrend = earlierSpeed > 0 ? ((recentSpeed - earlierSpeed) / earlierSpeed) * 100 : 0;\r\n\r\n    // Calculate consistency score (lower variance = higher consistency)\r\n    const accuracyVariance = dailyPerformance.reduce((sum, day) => {\r\n      const diff = day.accuracy - overallAccuracy;\r\n      return sum + (diff * diff);\r\n    }, 0) / dailyPerformance.length;\r\n    const consistencyScore = Math.max(0, 100 - Math.sqrt(accuracyVariance));\r\n\r\n    return {\r\n      overallAccuracy,\r\n      accuracyTrend,\r\n      averageSpeed,\r\n      speedTrend,\r\n      consistencyScore,\r\n      improvementRate: accuracyTrend,\r\n      strongestSubjects,\r\n      weakestSubjects,\r\n      dailyPerformance\r\n    };\r\n  }, [sessions, studySets, timeRange]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Main Performance Metrics */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n        <PerformanceCard\r\n          title=\"Overall Accuracy\"\r\n          value={`${Math.round(performanceData.overallAccuracy)}%`}\r\n          subtitle=\"Quiz performance\"\r\n          icon={HiCheckCircle}\r\n          trend={performanceData.accuracyTrend}\r\n          isLoading={isLoading}\r\n          color=\"green\"\r\n        />\r\n        \r\n        <PerformanceCard\r\n          title=\"Average Speed\"\r\n          value={formatSpeed(performanceData.averageSpeed)}\r\n          subtitle=\"Items per minute\"\r\n          icon={HiLightningBolt}\r\n          trend={performanceData.speedTrend}\r\n          isLoading={isLoading}\r\n          color=\"yellow\"\r\n        />\r\n        \r\n        <PerformanceCard\r\n          title=\"Consistency\"\r\n          value={`${Math.round(performanceData.consistencyScore)}%`}\r\n          subtitle=\"Performance stability\"\r\n          icon={HiChartBar}\r\n          isLoading={isLoading}\r\n          color=\"primary\"\r\n        />\r\n        \r\n        <PerformanceCard\r\n          title=\"Improvement\"\r\n          value={`${performanceData.improvementRate >= 0 ? '+' : ''}${performanceData.improvementRate.toFixed(1)}%`}\r\n          subtitle=\"Recent trend\"\r\n          icon={HiTrendingUp}\r\n          isLoading={isLoading}\r\n          color={performanceData.improvementRate >= 0 ? 'green' : 'red'}\r\n        />\r\n      </div>\r\n\r\n      {/* Performance Charts */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        <SubjectPerformanceChart\r\n          subjects={performanceData.strongestSubjects}\r\n          title=\"Strongest Subjects\"\r\n          color=\"bg-green-500\"\r\n        />\r\n        \r\n        <SubjectPerformanceChart\r\n          subjects={performanceData.weakestSubjects}\r\n          title=\"Areas for Improvement\"\r\n          color=\"bg-red-500\"\r\n        />\r\n      </div>\r\n\r\n      {/* Daily Performance Chart */}\r\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Daily Performance (Last 7 Days)</h3>\r\n        <div className=\"flex items-end justify-between h-32 space-x-2\">\r\n          {performanceData.dailyPerformance.map((day, index) => {\r\n            const maxAccuracy = Math.max(...performanceData.dailyPerformance.map(d => d.accuracy), 1);\r\n            const height = (day.accuracy / maxAccuracy) * 100;\r\n            \r\n            return (\r\n              <div key={index} className=\"flex-1 flex flex-col items-center\">\r\n                <div className=\"w-full bg-gray-700 rounded-t relative\" style={{ height: '100px' }}>\r\n                  <motion.div\r\n                    initial={{ height: 0 }}\r\n                    animate={{ height: `${height}%` }}\r\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                    className=\"bg-gradient-to-t from-green-500 to-green-400 rounded-t transition-all duration-300 absolute bottom-0 left-0 right-0\"\r\n                    title={`${Math.round(day.accuracy)}% accuracy, ${formatSpeed(day.speed)}`}\r\n                  />\r\n                </div>\r\n                <div className=\"text-xs text-gray-400 mt-2\">{day.date}</div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>\r\n} from 'react-icons/hi';\r\n\r\ninterface StudySession {\r\n  id: string;\r\n  studySetId: string;\r\n  type: 'flashcards' | 'quiz';\r\n  startTime: Date;\r\n  endTime?: Date;\r\n  totalItems: number;\r\n  reviewedItems: number;\r\n  flaggedItems: number;\r\n  correctAnswers?: number;\r\n  timeSpent: number;\r\n}\r\n\r\ninterface StudyTrendsProps {\r\n  sessions: StudySession[];\r\n  timeRange: '7d' | '30d' | '90d' | 'all';\r\n  isLoading: boolean;\r\n}\r\n\r\ninterface TrendData {\r\n  studyStreak: number;\r\n  longestStreak: number;\r\n  averageSessionsPerDay: number;\r\n  mostProductiveHour: number;\r\n  mostProductiveDay: string;\r\n  weeklyPattern: Array<{ day: string; sessions: number; time: number }>;\r\n  hourlyPattern: Array<{ hour: number; sessions: number; time: number }>;\r\n  monthlyTrend: Array<{ month: string; sessions: number; time: number }>;\r\n}\r\n\r\nconst formatTime = (seconds: number): string => {\r\n  const hours = Math.floor(seconds / 3600);\r\n  const minutes = Math.floor((seconds % 3600) / 60);\r\n  \r\n  if (hours > 0) {\r\n    return `${hours}h ${minutes}m`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes}m`;\r\n  } else {\r\n    return `${seconds}s`;\r\n  }\r\n};\r\n\r\nconst TrendCard: React.FC<{\r\n  title: string;\r\n  value: string | number;\r\n  subtitle?: string;\r\n  icon: React.ComponentType<{ className?: string }>;\r\n  isLoading?: boolean;\r\n  color?: string;\r\n}> = ({ title, value, subtitle, icon: Icon, isLoading, color = 'text-primary-400' }) => (\r\n  <motion.div\r\n    initial={{ opacity: 0, y: 20 }}\r\n    animate={{ opacity: 1, y: 0 }}\r\n    transition={{ duration: 0.3 }}\r\n    className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\"\r\n  >\r\n    <div className=\"flex items-center space-x-3 mb-4\">\r\n      <div className={`p-3 bg-primary-500/20 rounded-lg`}>\r\n        <Icon className={`w-6 h-6 ${color}`} />\r\n      </div>\r\n      <div>\r\n        <h3 className=\"text-sm font-medium text-gray-400\">{title}</h3>\r\n        {isLoading ? (\r\n          <div className=\"animate-pulse\">\r\n            <div className=\"h-6 w-16 bg-gray-600 rounded\"></div>\r\n          </div>\r\n        ) : (\r\n          <p className=\"text-xl font-bold text-white\">{value}</p>\r\n        )}\r\n      </div>\r\n    </div>\r\n    {subtitle && (\r\n      <p className=\"text-xs text-gray-500\">{subtitle}</p>\r\n    )}\r\n  </motion.div>\r\n);\r\n\r\nconst PatternChart: React.FC<{\r\n  title: string;\r\n  data: Array<{ label: string; value: number; time?: number }>;\r\n  type: 'bar' | 'line';\r\n  color: string;\r\n}> = ({ title, data, color }) => {\r\n  const maxValue = Math.max(...data.map(d => d.value), 1);\r\n  \r\n  return (\r\n    <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n      <h3 className=\"text-lg font-semibold text-white mb-4\">{title}</h3>\r\n      <div className=\"space-y-3\">\r\n        {data.map((item, index) => (\r\n          <div key={index} className=\"flex items-center justify-between\">\r\n            <span className=\"text-gray-300 text-sm w-20\">{item.label}</span>\r\n            <div className=\"flex-1 mx-4\">\r\n              <div className=\"w-full bg-gray-700 rounded-full h-2\">\r\n                <motion.div\r\n                  initial={{ width: 0 }}\r\n                  animate={{ width: `${(item.value / maxValue) * 100}%` }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  className={`h-2 rounded-full ${color}`}\r\n                />\r\n              </div>\r\n            </div>\r\n            <div className=\"text-right w-20\">\r\n              <span className=\"text-white font-medium text-sm\">{item.value}</span>\r\n              {item.time && (\r\n                <div className=\"text-xs text-gray-400\">{formatTime(item.time)}</div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const StudyTrends: React.FC<StudyTrendsProps> = ({\r\n  sessions,\r\n  timeRange,\r\n  isLoading\r\n}) => {\r\n  const trendData = useMemo((): TrendData => {\r\n    const now = new Date();\r\n    \r\n    // Calculate study streak\r\n    let studyStreak = 0;\r\n    let longestStreak = 0;\r\n    let currentStreak = 0;\r\n    \r\n    for (let i = 0; i < 365; i++) {\r\n      const checkDate = new Date(now);\r\n      checkDate.setDate(now.getDate() - i);\r\n      \r\n      const hasSessionOnDate = sessions.some(session => {\r\n        const sessionDate = new Date(session.startTime);\r\n        return sessionDate.toDateString() === checkDate.toDateString();\r\n      });\r\n      \r\n      if (hasSessionOnDate) {\r\n        if (i === 0 || studyStreak === i) {\r\n          studyStreak++;\r\n        }\r\n        currentStreak++;\r\n        longestStreak = Math.max(longestStreak, currentStreak);\r\n      } else {\r\n        if (i === 0) {\r\n          // Check yesterday for current streak\r\n          const yesterday = new Date(now);\r\n          yesterday.setDate(now.getDate() - 1);\r\n          const hasYesterdaySession = sessions.some(session => {\r\n            const sessionDate = new Date(session.startTime);\r\n            return sessionDate.toDateString() === yesterday.toDateString();\r\n          });\r\n          if (!hasYesterdaySession) {\r\n            studyStreak = 0;\r\n          }\r\n        }\r\n        currentStreak = 0;\r\n      }\r\n    }\r\n\r\n    // Calculate average sessions per day\r\n    const timeRangeMs = {\r\n      '7d': 7 * 24 * 60 * 60 * 1000,\r\n      '30d': 30 * 24 * 60 * 60 * 1000,\r\n      '90d': 90 * 24 * 60 * 60 * 1000,\r\n      'all': Infinity\r\n    }[timeRange];\r\n\r\n    const filteredSessions = sessions.filter(session => {\r\n      const sessionTime = new Date(session.startTime).getTime();\r\n      return now.getTime() - sessionTime <= timeRangeMs;\r\n    });\r\n\r\n    const days = timeRangeMs === Infinity ? \r\n      Math.max(1, Math.ceil((now.getTime() - Math.min(...sessions.map(s => new Date(s.startTime).getTime()))) / (24 * 60 * 60 * 1000))) :\r\n      Math.ceil(timeRangeMs / (24 * 60 * 60 * 1000));\r\n    \r\n    const averageSessionsPerDay = filteredSessions.length / days;\r\n\r\n    // Calculate hourly pattern\r\n    const hourlyPattern = Array.from({ length: 24 }, (_, hour) => {\r\n      const hourSessions = filteredSessions.filter(session => {\r\n        const sessionHour = new Date(session.startTime).getHours();\r\n        return sessionHour === hour;\r\n      });\r\n      \r\n      return {\r\n        hour,\r\n        sessions: hourSessions.length,\r\n        time: hourSessions.reduce((sum, session) => sum + session.timeSpent, 0)\r\n      };\r\n    });\r\n\r\n    const mostProductiveHour = hourlyPattern.reduce((max, current) => \r\n      current.sessions > max.sessions ? current : max\r\n    ).hour;\r\n\r\n    // Calculate weekly pattern\r\n    const weeklyPattern = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => {\r\n      const daySessions = filteredSessions.filter(session => {\r\n        const sessionDay = new Date(session.startTime).getDay();\r\n        return sessionDay === index;\r\n      });\r\n      \r\n      return {\r\n        day,\r\n        sessions: daySessions.length,\r\n        time: daySessions.reduce((sum, session) => sum + session.timeSpent, 0)\r\n      };\r\n    });\r\n\r\n    const mostProductiveDay = weeklyPattern.reduce((max, current) => \r\n      current.sessions > max.sessions ? current : max\r\n    ).day;\r\n\r\n    // Calculate monthly trend (last 6 months)\r\n    const monthlyTrend = Array.from({ length: 6 }, (_, i) => {\r\n      const date = new Date(now);\r\n      date.setMonth(now.getMonth() - (5 - i));\r\n      const monthName = date.toLocaleDateString('en-US', { month: 'short' });\r\n      \r\n      const monthSessions = sessions.filter(session => {\r\n        const sessionDate = new Date(session.startTime);\r\n        return sessionDate.getMonth() === date.getMonth() && \r\n               sessionDate.getFullYear() === date.getFullYear();\r\n      });\r\n      \r\n      return {\r\n        month: monthName,\r\n        sessions: monthSessions.length,\r\n        time: monthSessions.reduce((sum, session) => sum + session.timeSpent, 0)\r\n      };\r\n    });\r\n\r\n    return {\r\n      studyStreak,\r\n      longestStreak,\r\n      averageSessionsPerDay,\r\n      mostProductiveHour,\r\n      mostProductiveDay,\r\n      weeklyPattern,\r\n      hourlyPattern: hourlyPattern.filter(h => h.sessions > 0), // Only show active hours\r\n      monthlyTrend\r\n    };\r\n  }, [sessions, timeRange]);\r\n\r\n  const formatHour = (hour: number): string => {\r\n    if (hour === 0) return '12 AM';\r\n    if (hour < 12) return `${hour} AM`;\r\n    if (hour === 12) return '12 PM';\r\n    return `${hour - 12} PM`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Streak and Habit Metrics */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n        <TrendCard\r\n          title=\"Current Streak\"\r\n          value={`${trendData.studyStreak} days`}\r\n          subtitle=\"Consecutive study days\"\r\n          icon={HiFire}\r\n          isLoading={isLoading}\r\n          color=\"text-orange-400\"\r\n        />\r\n        \r\n        <TrendCard\r\n          title=\"Longest Streak\"\r\n          value={`${trendData.longestStreak} days`}\r\n          subtitle=\"Personal best\"\r\n          icon={HiTrendingUp}\r\n          isLoading={isLoading}\r\n          color=\"text-green-400\"\r\n        />\r\n        \r\n        <TrendCard\r\n          title=\"Daily Average\"\r\n          value={trendData.averageSessionsPerDay.toFixed(1)}\r\n          subtitle=\"Sessions per day\"\r\n          icon={HiCalendar}\r\n          isLoading={isLoading}\r\n          color=\"text-blue-400\"\r\n        />\r\n        \r\n        <TrendCard\r\n          title=\"Peak Hour\"\r\n          value={formatHour(trendData.mostProductiveHour)}\r\n          subtitle={`Most active time`}\r\n          icon={trendData.mostProductiveHour < 12 ? HiSun : HiMoon}\r\n          isLoading={isLoading}\r\n          color=\"text-yellow-400\"\r\n        />\r\n      </div>\r\n\r\n      {/* Pattern Analysis */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        <PatternChart\r\n          title=\"Weekly Study Pattern\"\r\n          data={trendData.weeklyPattern.map(day => ({\r\n            label: day.day,\r\n            value: day.sessions,\r\n            time: day.time\r\n          }))}\r\n          type=\"bar\"\r\n          color=\"bg-primary-500\"\r\n        />\r\n        \r\n        <PatternChart\r\n          title=\"Active Study Hours\"\r\n          data={trendData.hourlyPattern.slice(0, 8).map(hour => ({\r\n            label: formatHour(hour.hour),\r\n            value: hour.sessions,\r\n            time: hour.time\r\n          }))}\r\n          type=\"bar\"\r\n          color=\"bg-yellow-500\"\r\n        />\r\n      </div>\r\n\r\n      {/* Monthly Trend */}\r\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">6-Month Study Trend</h3>\r\n        <div className=\"flex items-end justify-between h-40 space-x-2\">\r\n          {trendData.monthlyTrend.map((month, index) => {\r\n            const maxSessions = Math.max(...trendData.monthlyTrend.map(m => m.sessions), 1);\r\n            const height = (month.sessions / maxSessions) * 100;\r\n            \r\n            return (\r\n              <div key={index} className=\"flex-1 flex flex-col items-center\">\r\n                <div className=\"w-full bg-gray-700 rounded-t relative\" style={{ height: '120px' }}>\r\n                  <motion.div\r\n                    initial={{ height: 0 }}\r\n                    animate={{ height: `${height}%` }}\r\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                    className=\"bg-gradient-to-t from-purple-500 to-purple-400 rounded-t transition-all duration-300 absolute bottom-0 left-0 right-0\"\r\n                    title={`${month.sessions} sessions, ${formatTime(month.time)}`}\r\n                  />\r\n                </div>\r\n                <div className=\"text-xs text-gray-400 mt-2\">{month.month}</div>\r\n                <div className=\"text-xs text-white font-medium\">{month.sessions}</div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Study Insights */}\r\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Study Insights</h3>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div>\r\n            <h4 className=\"font-medium text-white mb-2\">Best Study Day</h4>\r\n            <p className=\"text-gray-300\">{trendData.mostProductiveDay}</p>\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Most sessions completed on this day of the week\r\n            </p>\r\n          </div>\r\n          \r\n          <div>\r\n            <h4 className=\"font-medium text-white mb-2\">Optimal Study Time</h4>\r\n            <p className=\"text-gray-300\">{formatHour(trendData.mostProductiveHour)}</p>\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Peak productivity hour based on session frequency\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useEffect, useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  Hi<PERSON><PERSON><PERSON><PERSON>,\r\n  HiTrendingUp,\r\n  <PERSON><PERSON><PERSON>,\r\n  HiRefresh,\r\n  HiFilter,\r\n  HiDownload,\r\n  HiExclamationCircle\r\n} from 'react-icons/hi';\r\nimport { StudyAnalytics } from '../components/analytics/StudyAnalytics';\r\nimport { AnalyticsDashboard } from '../components/analytics/AnalyticsDashboard';\r\nimport { PerformanceMetrics } from '../components/analytics/PerformanceMetrics';\r\nimport { StudyTrends } from '../components/analytics/StudyTrends';\r\nimport { Button } from '../components/common/Button';\r\nimport { useStudyStore } from '../stores/studyStore';\r\nimport { useDocumentStore } from '../stores/documentStore';\r\n\r\ninterface TabSection {\r\n  id: string;\r\n  label: string;\r\n  icon: React.ComponentType<{ className?: string }>;\r\n  description: string;\r\n}\r\n\r\nconst analyticsTabs: TabSection[] = [\r\n  {\r\n    id: 'overview',\r\n    label: 'Overview',\r\n    icon: HiC<PERSON><PERSON><PERSON>,\r\n    description: 'General study analytics and progress'\r\n  },\r\n  {\r\n    id: 'performance',\r\n    label: 'Performance',\r\n    icon: HiTrendingUp,\r\n    description: 'Detailed performance metrics and accuracy'\r\n  },\r\n  {\r\n    id: 'trends',\r\n    label: 'Trends',\r\n    icon: HiClock,\r\n    description: 'Study patterns and time analysis'\r\n  }\r\n];\r\n\r\nexport const AnalyticsPage: React.FC = () => {\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const { studySets, sessions, fetchStudySets, fetchStudySessions } = useStudyStore();\r\n  const { documents } = useDocumentStore();\r\n\r\n  useEffect(() => {\r\n    const loadAnalyticsData = async () => {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      try {\r\n        await Promise.all([\r\n          fetchStudySets(),\r\n          fetchStudySessions(timeRange)\r\n        ]);\r\n      } catch (err) {\r\n        setError(err instanceof Error ? err.message : 'Failed to load analytics data');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    loadAnalyticsData();\r\n  }, [timeRange, fetchStudySets, fetchStudySessions]);\r\n\r\n  const handleRefresh = async () => {\r\n    setError(null);\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      await Promise.all([\r\n        fetchStudySets(),\r\n        fetchStudySessions(timeRange)\r\n      ]);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to refresh data');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const exportAnalytics = () => {\r\n    // Create CSV export of analytics data\r\n    const csvData = [\r\n      ['Date', 'Study Set', 'Type', 'Items Reviewed', 'Time Spent (minutes)', 'Accuracy'].join(','),\r\n      ...sessions.map(session => {\r\n        const studySet = studySets.find(set => set.id === session.studySetId);\r\n        return [\r\n          new Date(session.startTime).toLocaleDateString(),\r\n          studySet?.name || 'Unknown',\r\n          session.type,\r\n          session.reviewedItems,\r\n          Math.round(session.timeSpent / 60),\r\n          session.correctAnswers ? `${Math.round((session.correctAnswers / session.totalItems) * 100)}%` : 'N/A'\r\n        ].join(',');\r\n      })\r\n    ].join('\\n');\r\n\r\n    const blob = new Blob([csvData], { type: 'text/csv' });\r\n    const url = window.URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    a.href = url;\r\n    a.download = `study-analytics-${new Date().toISOString().split('T')[0]}.csv`;\r\n    a.click();\r\n    window.URL.revokeObjectURL(url);\r\n  };\r\n\r\n  const renderOverviewTab = () => (\r\n    <div className=\"space-y-6\">\r\n      <AnalyticsDashboard\r\n        studySets={studySets}\r\n        sessions={sessions}\r\n        documents={documents}\r\n        timeRange={timeRange}\r\n        isLoading={isLoading}\r\n      />\r\n      <StudyAnalytics\r\n        studySets={studySets}\r\n        sessions={sessions}\r\n      />\r\n    </div>\r\n  );\r\n\r\n  const renderPerformanceTab = () => (\r\n    <PerformanceMetrics\r\n      studySets={studySets}\r\n      sessions={sessions}\r\n      timeRange={timeRange}\r\n      isLoading={isLoading}\r\n    />\r\n  );\r\n\r\n  const renderTrendsTab = () => (\r\n    <StudyTrends\r\n      sessions={sessions}\r\n      timeRange={timeRange}\r\n      isLoading={isLoading}\r\n    />\r\n  );\r\n\r\n  const renderContent = () => {\r\n    switch (activeTab) {\r\n      case 'overview':\r\n        return renderOverviewTab();\r\n      case 'performance':\r\n        return renderPerformanceTab();\r\n      case 'trends':\r\n        return renderTrendsTab();\r\n      default:\r\n        return renderOverviewTab();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background-primary text-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header */}\r\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0\">\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-white mb-2\">Analytics</h1>\r\n            <p className=\"text-gray-400\">Track your learning progress and performance insights</p>\r\n          </div>\r\n\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Time Range Filter */}\r\n            <div className=\"relative\">\r\n              <select\r\n                value={timeRange}\r\n                onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d' | 'all')}\r\n                className=\"appearance-none bg-background-secondary border border-border-primary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500\"\r\n              >\r\n                <option value=\"7d\">Last 7 days</option>\r\n                <option value=\"30d\">Last 30 days</option>\r\n                <option value=\"90d\">Last 90 days</option>\r\n                <option value=\"all\">All time</option>\r\n              </select>\r\n              <HiFilter className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none\" />\r\n            </div>\r\n\r\n            {/* Export Button */}\r\n            <Button\r\n              onClick={exportAnalytics}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n              disabled={sessions.length === 0}\r\n            >\r\n              <HiDownload className=\"w-4 h-4 mr-2\" />\r\n              Export\r\n            </Button>\r\n\r\n            {/* Refresh Button */}\r\n            <Button\r\n              onClick={handleRefresh}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n              disabled={isLoading}\r\n            >\r\n              <HiRefresh className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />\r\n              Refresh\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Error Display */}\r\n        {error && (\r\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n              <span className=\"text-red-400 font-medium\">Error</span>\r\n            </div>\r\n            <p className=\"text-red-300 mt-1\">{error}</p>\r\n            <Button\r\n              onClick={() => setError(null)}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n              className=\"mt-2\"\r\n            >\r\n              Dismiss\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\r\n          {/* Tab Navigation */}\r\n          <div className=\"lg:col-span-1\">\r\n            <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n              <nav className=\"space-y-2\">\r\n                {analyticsTabs.map((tab) => {\r\n                  const Icon = tab.icon;\r\n                  const isActive = activeTab === tab.id;\r\n\r\n                  return (\r\n                    <button\r\n                      key={tab.id}\r\n                      onClick={() => setActiveTab(tab.id)}\r\n                      className={`\r\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\r\n                        transition-all duration-200\r\n                        ${isActive\r\n                          ? 'bg-primary-500/20 text-primary-400 border border-primary-500/30'\r\n                          : 'text-gray-300 hover:bg-background-tertiary hover:text-white'\r\n                        }\r\n                      `}\r\n                    >\r\n                      <Icon className=\"w-5 h-5\" />\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <span className=\"font-medium block\">{tab.label}</span>\r\n                        <span className=\"text-xs text-gray-500 block truncate\">\r\n                          {tab.description}\r\n                        </span>\r\n                      </div>\r\n                    </button>\r\n                  );\r\n                })}\r\n              </nav>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Tab Content */}\r\n          <div className=\"lg:col-span-3\">\r\n            <motion.div\r\n              key={activeTab}\r\n              initial={{ opacity: 0, x: 20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              {renderContent()}\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import { create } from \"zustand\";\r\nimport { DocumentMetadata, DocumentWithContent } from \"../shared/types\";\r\n\r\ninterface DocumentState {\r\n  documents: DocumentMetadata[];\r\n  selectedDocuments: Set<string>;\r\n  isLoading: boolean;\r\n  uploadProgress: { [key: string]: number };\r\n\r\n  // Actions\r\n  fetchDocuments: () => Promise<void>;\r\n  uploadDocument: (file: File) => Promise<DocumentMetadata>;\r\n  deleteDocument: (id: string) => Promise<void>;\r\n  searchDocuments: (query: string) => Promise<DocumentMetadata[]>;\r\n  getDocument: (id: string) => Promise<DocumentWithContent | null>;\r\n  toggleDocumentSelection: (id: string) => void;\r\n  clearSelection: () => void;\r\n  selectAll: () => void;\r\n  setUploadProgress: (fileName: string, progress: number) => void;\r\n}\r\n\r\nexport const useDocumentStore = create<DocumentState>((set) => ({\r\n  documents: [],\r\n  selectedDocuments: new Set(),\r\n  isLoading: false,\r\n  uploadProgress: {},\r\n\r\n  fetchDocuments: async () => {\r\n    set({ isLoading: true });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(\"/api/documents\", {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch documents\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        set({ documents: result.data, isLoading: false });\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Fetch documents error:\", error);\r\n      set({ isLoading: false });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  uploadDocument: async (file: File) => {\r\n    const formData = new FormData();\r\n    formData.append(\"document\", file);\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(\"/api/documents/upload\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Upload failed\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        // Add new document to the list\r\n        set((state) => ({\r\n          documents: [result.data, ...state.documents],\r\n          uploadProgress: { ...state.uploadProgress, [file.name]: 100 },\r\n        }));\r\n\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Upload document error:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  deleteDocument: async (id: string) => {\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(`/api/documents/${id}`, {\r\n        method: \"DELETE\",\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Delete failed\");\r\n      }\r\n\r\n      // Remove document from the list\r\n      set((state) => ({\r\n        documents: state.documents.filter((doc) => doc.id !== id),\r\n        selectedDocuments: new Set(\r\n          [...state.selectedDocuments].filter((docId) => docId !== id)\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Delete document error:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  searchDocuments: async (query: string) => {\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(\r\n        `/api/documents/search?q=${encodeURIComponent(query)}`,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Search failed\");\r\n      }\r\n\r\n      const result = await response.json();\r\n      return result.success ? result.data : [];\r\n    } catch (error) {\r\n      console.error(\"Search documents error:\", error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  getDocument: async (id: string) => {\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n      const response = await fetch(`/api/documents/${id}`, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        return null;\r\n      }\r\n\r\n      const result = await response.json();\r\n      return result.success ? result.data : null;\r\n    } catch (error) {\r\n      console.error(\"Get document error:\", error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  toggleDocumentSelection: (id: string) => {\r\n    set((state) => {\r\n      const newSelection = new Set(state.selectedDocuments);\r\n      if (newSelection.has(id)) {\r\n        newSelection.delete(id);\r\n      } else {\r\n        newSelection.add(id);\r\n      }\r\n      return { selectedDocuments: newSelection };\r\n    });\r\n  },\r\n\r\n  clearSelection: () => {\r\n    set({ selectedDocuments: new Set() });\r\n  },\r\n\r\n  selectAll: () => {\r\n    set((state) => ({\r\n      selectedDocuments: new Set(state.documents.map((doc) => doc.id)),\r\n    }));\r\n  },\r\n\r\n  setUploadProgress: (fileName: string, progress: number) => {\r\n    set((state) => ({\r\n      uploadProgress: { ...state.uploadProgress, [fileName]: progress },\r\n    }));\r\n  },\r\n}));\r\n", "import { create } from \"zustand\";\r\nimport { StudySet, Flashcard, QuizQuestion } from \"../shared/types\";\r\n\r\n// Utility function for shuffling arrays using Fisher-Yates algorithm\r\nconst shuffleArray = <T>(array: T[]): T[] => {\r\n  const shuffled = [...array];\r\n  for (let i = shuffled.length - 1; i > 0; i--) {\r\n    const j = Math.floor(Math.random() * (i + 1));\r\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\r\n  }\r\n  return shuffled;\r\n};\r\n\r\ninterface StudyAction {\r\n  type:\r\n    | \"NEXT_ITEM\"\r\n    | \"PREVIOUS_ITEM\"\r\n    | \"TOGGLE_FLAG\"\r\n    | \"MARK_REVIEWED\"\r\n    | \"SUBMIT_ANSWER\";\r\n  payload: any;\r\n  previousState: Partial<StudySession>;\r\n  timestamp: number;\r\n}\r\n\r\ninterface StudySession {\r\n  studySetId: string;\r\n  type: \"flashcards\" | \"quiz\";\r\n  startTime: Date;\r\n  currentIndex: number;\r\n  totalItems: number;\r\n  reviewedItems: number[];\r\n  flaggedItems: string[];\r\n  correctAnswers?: number; // For quizzes\r\n  timeSpent: number; // in seconds\r\n  isShuffled: boolean; // Whether the session uses shuffled order\r\n  originalOrder?: number[]; // Original indices for unshuffling if needed\r\n}\r\n\r\ninterface StudySessionData {\r\n  id: string;\r\n  studySetId: string;\r\n  type: \"flashcards\" | \"quiz\";\r\n  startTime: Date;\r\n  endTime?: Date;\r\n  totalItems: number;\r\n  reviewedItems: number;\r\n  flaggedItems: number;\r\n  correctAnswers?: number;\r\n  timeSpent: number;\r\n}\r\n\r\ninterface StudyState {\r\n  currentSession: StudySession | null;\r\n  studySetContent: {\r\n    studySet?: StudySet;\r\n    flashcards?: Flashcard[];\r\n    questions?: QuizQuestion[];\r\n  } | null;\r\n  studySets: StudySet[];\r\n  sessions: StudySessionData[];\r\n  isLoading: boolean;\r\n  error: string | null;\r\n\r\n  // Undo/Redo functionality\r\n  actionHistory: StudyAction[];\r\n  currentActionIndex: number;\r\n  canUndo: boolean;\r\n  canRedo: boolean;\r\n\r\n  // Actions\r\n  startStudySession: (\r\n    studySetId: string,\r\n    type: \"flashcards\" | \"quiz\",\r\n    shuffle?: boolean\r\n  ) => Promise<void>;\r\n  endStudySession: () => void;\r\n  nextItem: () => void;\r\n  previousItem: () => void;\r\n  goToItem: (index: number) => void;\r\n  toggleFlag: (itemId: string) => void;\r\n  markReviewed: (itemId: string) => void;\r\n  submitQuizAnswer: (\r\n    questionId: string,\r\n    answer: string[],\r\n    isCorrect: boolean\r\n  ) => void;\r\n  updateTimeSpent: (seconds: number) => void;\r\n  fetchStudySetContent: (\r\n    studySetId: string,\r\n    forceRefresh?: boolean\r\n  ) => Promise<void>;\r\n  fetchStudySets: () => Promise<void>;\r\n  fetchStudySessions: (\r\n    timeRange?: \"7d\" | \"30d\" | \"90d\" | \"all\"\r\n  ) => Promise<void>;\r\n\r\n  // Cache management methods\r\n  invalidateStudySetContent: (studySetId?: string) => void;\r\n  refreshStudySetContent: (studySetId: string) => Promise<void>;\r\n  invalidateStudySets: () => void;\r\n\r\n  // Undo/Redo actions\r\n  undo: () => void;\r\n  redo: () => void;\r\n  clearHistory: () => void;\r\n  addToHistory: (action: StudyAction) => void;\r\n}\r\n\r\nexport const useStudyStore = create<StudyState>((set, get) => ({\r\n  currentSession: null,\r\n  studySetContent: null,\r\n  studySets: [],\r\n  sessions: [],\r\n  isLoading: false,\r\n  error: null,\r\n\r\n  // Undo/Redo state\r\n  actionHistory: [],\r\n  currentActionIndex: -1,\r\n  canUndo: false,\r\n  canRedo: false,\r\n\r\n  fetchStudySetContent: async (studySetId: string, forceRefresh = false) => {\r\n    // If forceRefresh is true or content doesn't exist, refetch\r\n    const { studySetContent } = get();\r\n    if (\r\n      forceRefresh ||\r\n      !studySetContent ||\r\n      studySetContent.studySet?.id !== studySetId\r\n    ) {\r\n      set({ isLoading: true, error: null });\r\n\r\n      try {\r\n        const token = localStorage.getItem(\"auth_token\");\r\n\r\n        const response = await fetch(`/api/study-sets/${studySetId}/content`, {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        });\r\n\r\n        if (!response.ok) {\r\n          const errorResult = await response.json();\r\n          throw new Error(\r\n            errorResult.error || \"Failed to fetch study set content\"\r\n          );\r\n        }\r\n\r\n        const result = await response.json();\r\n\r\n        if (result.success) {\r\n          set({\r\n            studySetContent: {\r\n              studySet: result.data.studySet,\r\n              flashcards: result.data.flashcards || [],\r\n              questions: result.data.questions || [],\r\n            },\r\n            isLoading: false,\r\n          });\r\n        } else {\r\n          throw new Error(result.error);\r\n        }\r\n      } catch (error: any) {\r\n        set({\r\n          error: error.message || \"Failed to fetch study set content\",\r\n          isLoading: false,\r\n        });\r\n        throw error;\r\n      }\r\n    }\r\n  },\r\n\r\n  startStudySession: async (\r\n    studySetId: string,\r\n    type: \"flashcards\" | \"quiz\",\r\n    shuffle = false\r\n  ) => {\r\n    const { studySetContent, fetchStudySetContent } = get();\r\n\r\n    // Fetch content if not already loaded\r\n    if (!studySetContent || studySetContent.studySet?.id !== studySetId) {\r\n      await fetchStudySetContent(studySetId);\r\n    }\r\n\r\n    const content = get().studySetContent;\r\n    if (!content) {\r\n      throw new Error(\"Failed to load study set content\");\r\n    }\r\n\r\n    const totalItems =\r\n      type === \"flashcards\"\r\n        ? content.flashcards?.length || 0\r\n        : content.questions?.length || 0;\r\n\r\n    if (totalItems === 0) {\r\n      throw new Error(\"No study materials found in this set\");\r\n    }\r\n\r\n    // Handle shuffling\r\n    let originalOrder: number[] | undefined;\r\n    if (shuffle) {\r\n      // Create array of original indices\r\n      originalOrder = Array.from({ length: totalItems }, (_, i) => i);\r\n\r\n      // Shuffle the content arrays\r\n      if (type === \"flashcards\" && content.flashcards) {\r\n        const shuffledFlashcards = shuffleArray(content.flashcards);\r\n        set((state) => ({\r\n          studySetContent: {\r\n            ...state.studySetContent!,\r\n            flashcards: shuffledFlashcards,\r\n          },\r\n        }));\r\n      } else if (type === \"quiz\" && content.questions) {\r\n        const shuffledQuestions = shuffleArray(content.questions);\r\n        set((state) => ({\r\n          studySetContent: {\r\n            ...state.studySetContent!,\r\n            questions: shuffledQuestions,\r\n          },\r\n        }));\r\n      }\r\n    }\r\n\r\n    set({\r\n      currentSession: {\r\n        studySetId,\r\n        type,\r\n        startTime: new Date(),\r\n        currentIndex: 0,\r\n        totalItems,\r\n        reviewedItems: [],\r\n        flaggedItems: [],\r\n        correctAnswers: type === \"quiz\" ? 0 : undefined,\r\n        timeSpent: 0,\r\n        isShuffled: shuffle,\r\n        originalOrder,\r\n      },\r\n    });\r\n  },\r\n\r\n  endStudySession: () => {\r\n    set({ currentSession: null });\r\n  },\r\n\r\n  nextItem: () => {\r\n    const { currentSession, addToHistory } = get();\r\n    if (!currentSession) return;\r\n\r\n    // Implement circular navigation: if at last item, wrap to first item\r\n    const nextIndex =\r\n      currentSession.currentIndex === currentSession.totalItems - 1\r\n        ? 0\r\n        : currentSession.currentIndex + 1;\r\n\r\n    // Record action in history\r\n    addToHistory({\r\n      type: \"NEXT_ITEM\",\r\n      payload: { fromIndex: currentSession.currentIndex, toIndex: nextIndex },\r\n      previousState: { currentIndex: currentSession.currentIndex },\r\n      timestamp: Date.now(),\r\n    });\r\n\r\n    set({\r\n      currentSession: {\r\n        ...currentSession,\r\n        currentIndex: nextIndex,\r\n      },\r\n    });\r\n  },\r\n\r\n  previousItem: () => {\r\n    const { currentSession, addToHistory } = get();\r\n    if (!currentSession) return;\r\n\r\n    // Implement circular navigation: if at first item, wrap to last item\r\n    const prevIndex =\r\n      currentSession.currentIndex === 0\r\n        ? currentSession.totalItems - 1\r\n        : currentSession.currentIndex - 1;\r\n\r\n    // Record action in history\r\n    addToHistory({\r\n      type: \"PREVIOUS_ITEM\",\r\n      payload: { fromIndex: currentSession.currentIndex, toIndex: prevIndex },\r\n      previousState: { currentIndex: currentSession.currentIndex },\r\n      timestamp: Date.now(),\r\n    });\r\n\r\n    set({\r\n      currentSession: {\r\n        ...currentSession,\r\n        currentIndex: prevIndex,\r\n      },\r\n    });\r\n  },\r\n\r\n  goToItem: (index: number) => {\r\n    const { currentSession } = get();\r\n    if (!currentSession) return;\r\n\r\n    const clampedIndex = Math.max(\r\n      0,\r\n      Math.min(index, currentSession.totalItems - 1)\r\n    );\r\n    set({\r\n      currentSession: {\r\n        ...currentSession,\r\n        currentIndex: clampedIndex,\r\n      },\r\n    });\r\n  },\r\n\r\n  toggleFlag: (itemId: string) => {\r\n    const { currentSession, addToHistory } = get();\r\n    if (!currentSession) return;\r\n\r\n    const wasFlagged = currentSession.flaggedItems.includes(itemId);\r\n    const flaggedItems = wasFlagged\r\n      ? currentSession.flaggedItems.filter((id) => id !== itemId)\r\n      : [...currentSession.flaggedItems, itemId];\r\n\r\n    // Record action in history\r\n    addToHistory({\r\n      type: \"TOGGLE_FLAG\",\r\n      payload: { itemId, wasFlagged },\r\n      previousState: { flaggedItems: currentSession.flaggedItems },\r\n      timestamp: Date.now(),\r\n    });\r\n\r\n    set({\r\n      currentSession: {\r\n        ...currentSession,\r\n        flaggedItems,\r\n      },\r\n    });\r\n  },\r\n\r\n  markReviewed: (_itemId: string) => {\r\n    const { currentSession } = get();\r\n    if (!currentSession) return;\r\n\r\n    if (!currentSession.reviewedItems.includes(currentSession.currentIndex)) {\r\n      set({\r\n        currentSession: {\r\n          ...currentSession,\r\n          reviewedItems: [\r\n            ...currentSession.reviewedItems,\r\n            currentSession.currentIndex,\r\n          ],\r\n        },\r\n      });\r\n    }\r\n  },\r\n\r\n  submitQuizAnswer: (\r\n    questionId: string,\r\n    _answer: string[],\r\n    isCorrect: boolean\r\n  ) => {\r\n    const { currentSession, markReviewed } = get();\r\n    if (!currentSession || currentSession.type !== \"quiz\") return;\r\n\r\n    markReviewed(questionId);\r\n\r\n    if (isCorrect) {\r\n      set({\r\n        currentSession: {\r\n          ...currentSession,\r\n          correctAnswers: (currentSession.correctAnswers || 0) + 1,\r\n        },\r\n      });\r\n    }\r\n  },\r\n\r\n  updateTimeSpent: (seconds: number) => {\r\n    const { currentSession } = get();\r\n    if (!currentSession) return;\r\n\r\n    set({\r\n      currentSession: {\r\n        ...currentSession,\r\n        timeSpent: currentSession.timeSpent + seconds,\r\n      },\r\n    });\r\n  },\r\n\r\n  // Helper function to add action to history\r\n  addToHistory: (action: StudyAction) => {\r\n    const { actionHistory, currentActionIndex } = get();\r\n\r\n    // Remove any actions after current index (when undoing then doing new action)\r\n    const newHistory = actionHistory.slice(0, currentActionIndex + 1);\r\n    newHistory.push(action);\r\n\r\n    // Limit history to last 50 actions for performance\r\n    const limitedHistory = newHistory.slice(-50);\r\n\r\n    set({\r\n      actionHistory: limitedHistory,\r\n      currentActionIndex: limitedHistory.length - 1,\r\n      canUndo: limitedHistory.length > 0,\r\n      canRedo: false,\r\n    });\r\n  },\r\n\r\n  undo: () => {\r\n    const { actionHistory, currentActionIndex, currentSession } = get();\r\n\r\n    if (currentActionIndex < 0 || !currentSession) return;\r\n\r\n    const action = actionHistory[currentActionIndex];\r\n\r\n    // Restore previous state\r\n    set({\r\n      currentSession: {\r\n        ...currentSession,\r\n        ...action.previousState,\r\n      },\r\n      currentActionIndex: currentActionIndex - 1,\r\n      canUndo: currentActionIndex > 0,\r\n      canRedo: true,\r\n    });\r\n  },\r\n\r\n  redo: () => {\r\n    const { actionHistory, currentActionIndex, currentSession } = get();\r\n\r\n    if (currentActionIndex >= actionHistory.length - 1 || !currentSession)\r\n      return;\r\n\r\n    const nextActionIndex = currentActionIndex + 1;\r\n    const action = actionHistory[nextActionIndex];\r\n\r\n    // Re-apply the action\r\n    switch (action.type) {\r\n      case \"NEXT_ITEM\":\r\n        get().nextItem();\r\n        break;\r\n      case \"PREVIOUS_ITEM\":\r\n        get().previousItem();\r\n        break;\r\n      case \"TOGGLE_FLAG\":\r\n        get().toggleFlag(action.payload.itemId);\r\n        break;\r\n      case \"MARK_REVIEWED\":\r\n        get().markReviewed(action.payload.itemId);\r\n        break;\r\n    }\r\n\r\n    set({\r\n      currentActionIndex: nextActionIndex,\r\n      canUndo: true,\r\n      canRedo: nextActionIndex < actionHistory.length - 1,\r\n    });\r\n  },\r\n\r\n  clearHistory: () => {\r\n    set({\r\n      actionHistory: [],\r\n      currentActionIndex: -1,\r\n      canUndo: false,\r\n      canRedo: false,\r\n    });\r\n  },\r\n\r\n  fetchStudySets: async () => {\r\n    set({ isLoading: true, error: null });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n\r\n      const response = await fetch(\"/api/study-sets\", {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Failed to fetch study sets\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        set({\r\n          studySets: result.data,\r\n          isLoading: false,\r\n        });\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.message || \"Failed to fetch study sets\",\r\n        isLoading: false,\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  fetchStudySessions: async (timeRange = \"30d\") => {\r\n    set({ isLoading: true, error: null });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"auth_token\");\r\n\r\n      const response = await fetch(\r\n        `/api/study-sessions?timeRange=${timeRange}`,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        const errorResult = await response.json();\r\n        throw new Error(errorResult.error || \"Failed to fetch study sessions\");\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        // Convert date strings to Date objects\r\n        const sessions = result.data.map((session: any) => ({\r\n          ...session,\r\n          startTime: new Date(session.startTime),\r\n          endTime: session.endTime ? new Date(session.endTime) : undefined,\r\n        }));\r\n\r\n        set({\r\n          sessions,\r\n          isLoading: false,\r\n        });\r\n      } else {\r\n        throw new Error(result.error);\r\n      }\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.message || \"Failed to fetch study sessions\",\r\n        isLoading: false,\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Cache management methods\r\n  invalidateStudySetContent: (studySetId?: string) => {\r\n    const { studySetContent } = get();\r\n\r\n    if (studySetId) {\r\n      // Clear cache for specific study set only if it matches\r\n      if (studySetContent?.studySet?.id === studySetId) {\r\n        set({ studySetContent: null });\r\n      }\r\n    } else {\r\n      // Clear all cached study set content\r\n      set({ studySetContent: null });\r\n    }\r\n  },\r\n\r\n  refreshStudySetContent: async (studySetId: string) => {\r\n    // Force a fresh fetch of study set content\r\n    await get().fetchStudySetContent(studySetId, true);\r\n  },\r\n\r\n  invalidateStudySets: () => {\r\n    // Clear the study sets list to force refetch\r\n    set({ studySets: [] });\r\n  },\r\n}));\r\n"], "names": ["formatTime", "seconds", "hours", "Math", "floor", "minutes", "secs", "concat", "StatCard", "memo", "_ref", "title", "value", "subtitle", "icon", "_jsx", "className", "children", "_jsxs", "displayName", "ProgressChart", "_ref2", "sessions", "chartData", "useMemo", "last7Days", "Array", "from", "length", "_", "i", "date", "Date", "setDate", "getDate", "toLocaleDateString", "weekday", "timeSpent", "for<PERSON>ach", "session", "sessionDate", "startTime", "dayIndex", "now", "getTime", "chartIndex", "maxTime", "max", "map", "d", "day", "index", "style", "height", "position", "bottom", "left", "right", "StudyAnalytics", "_ref3", "studySets", "analytics", "_Object$entries$sort$", "_studySets$find", "totalStudyTime", "reduce", "sum", "totalSessions", "averageSessionTime", "totalItemsReviewed", "reviewedItems", "quizSessions", "filter", "s", "type", "undefined", "correctAnswers", "totalQuizItems", "totalItems", "totalCorrect", "averageAccuracy", "today", "studyStreak", "checkDate", "some", "toDateString", "setStudyCount", "acc", "studySetId", "mostStudiedSetId", "Object", "entries", "sort", "_ref4", "_ref5", "a", "b", "mostStudiedSet", "find", "set", "id", "name", "recentActivity", "slice", "round", "studySet", "MetricCard", "Icon", "trend", "isLoading", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "isPositive", "HiTrendingUp", "abs", "AnalyticsDashboard", "documents", "timeRange", "metrics", "timeRangeMs", "Infinity", "filteredSessions", "sessionTime", "completionRate", "aiGeneratedSets", "is_ai_generated", "totalStudySets", "totalDocuments", "trends", "currentPeriodStart", "previousPeriodStart", "currentSessions", "previousSessions", "currentTime", "previousTime", "timeTrend", "sessionsTrend", "studyTime", "HiCollection", "HiDocumentText", "replace", "<PERSON><PERSON><PERSON>", "HiAcademicCap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HiSparkles", "formatSpeed", "itemsPerMinute", "toFixed", "PerformanceCard", "color", "primary", "green", "red", "yellow", "HiTrendingDown", "SubjectPerformanceChart", "subjects", "subject", "width", "accuracy", "PerformanceMetrics", "performanceData", "overallAccuracy", "totalTimeMinutes", "averageSpeed", "subjectPerformance", "Map", "existing", "get", "correct", "total", "subjectAccuracies", "data", "strongestSubjects", "weakestSubjects", "reverse", "dailyPerformance", "dateStr", "toISOString", "split", "daySessions", "dayQuizSessions", "dayTotalItems", "dayCorrect", "dayAccuracy", "dayReviewedItems", "dayTimeMinutes", "daySpeed", "speed", "recentAccuracy", "earlierAccuracy", "accuracyTrend", "recentSpeed", "earlierSpeed", "speedTrend", "accuracy<PERSON><PERSON>ce", "diff", "consistencyScore", "sqrt", "improvementRate", "HiCheckCircle", "HiLightningBolt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxAccuracy", "delay", "TrendCard", "<PERSON><PERSON><PERSON><PERSON>", "maxValue", "item", "label", "time", "StudyTrends", "trendData", "longestStreak", "currentStreak", "yesterday", "days", "ceil", "min", "averageSessionsPerDay", "hourlyPattern", "hour", "hourSessions", "getHours", "mostProductiveHour", "current", "weeklyPattern", "getDay", "mostProductiveDay", "monthlyTrend", "setMonth", "getMonth", "monthName", "month", "monthSessions", "getFullYear", "h", "formatHour", "HiFire", "HiCalendar", "<PERSON><PERSON><PERSON>", "HiMoon", "maxSessions", "m", "analyticsTabs", "description", "AnalyticsPage", "activeTab", "setActiveTab", "useState", "setTimeRange", "setIsLoading", "error", "setError", "fetchStudySets", "fetchStudySessions", "useStudyStore", "useDocumentStore", "useEffect", "async", "Promise", "all", "err", "Error", "message", "loadAnalyticsData", "renderOverviewTab", "onChange", "e", "target", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "onClick", "exportAnalytics", "csvData", "join", "blob", "Blob", "url", "window", "URL", "createObjectURL", "document", "createElement", "href", "download", "click", "revokeObjectURL", "variant", "size", "disabled", "HiDownload", "HiRefresh", "HiExclamationCircle", "tab", "isActive", "x", "renderContent", "create", "selectedDocuments", "Set", "uploadProgress", "fetchDocuments", "token", "localStorage", "getItem", "response", "fetch", "headers", "Authorization", "ok", "result", "json", "success", "console", "uploadDocument", "formData", "FormData", "append", "file", "method", "body", "errorResult", "state", "_objectSpread", "deleteDocument", "doc", "docId", "searchDocuments", "encodeURIComponent", "query", "getDocument", "toggleDocumentSelection", "newSelection", "has", "delete", "add", "clearSelection", "selectAll", "setUploadProgress", "fileName", "progress", "shuffle<PERSON><PERSON><PERSON>", "array", "shuffled", "j", "random", "currentSession", "studySetContent", "actionHistory", "currentActionIndex", "canUndo", "canRedo", "fetchStudySetContent", "_studySetContent$stud", "forceRefresh", "arguments", "flashcards", "questions", "startStudySession", "_studySetContent$stud2", "_content$flashcards", "_content$questions", "shuffle", "content", "originalOrder", "shuffledFlashcards", "shuffledQuestions", "currentIndex", "flaggedItems", "isShuffled", "endStudySession", "nextItem", "addToHistory", "nextIndex", "payload", "fromIndex", "toIndex", "previousState", "timestamp", "previousItem", "prevIndex", "goToItem", "clampedIndex", "toggleFlag", "itemId", "wasFlagged", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_itemId", "submitQuizAnswer", "questionId", "_answer", "isCorrect", "updateTimeSpent", "action", "newHistory", "push", "limitedHistory", "undo", "redo", "nextActionIndex", "clearHistory", "endTime", "invalidateStudySetContent", "_studySetContent$stud3", "refreshStudySetContent", "invalidateStudySets"], "sourceRoot": ""}