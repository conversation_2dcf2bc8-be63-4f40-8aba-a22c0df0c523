import React, { useState } from "react";
import { Button } from "../common/Button";
import { CountInput } from "../common/CountInput";
import { AIGenerationProgress } from "../common/ProgressBar";
import { useDialog } from "../../contexts/DialogContext";
import { DocumentSelector } from "../ai/DocumentSelector";
import { DifficultySelector } from "../common/DifficultySelector";
import { ContentLengthSelector } from "../common/ContentLengthSelector";
import useAuthStore from "../../stores/authStore";
import { HiEye, HiEyeOff } from "react-icons/hi";
import {
  QuizQuestion,
  QuestionType,
  DifficultyLevel,
  ContentLength,
  difficultyLevelToNumber,
  difficultyLevelToString,
  numberToDifficultyLevel,
} from "../../../../shared/types";
import { Checkbox } from "../common/Checkbox";
import { BulkActionsToolbar } from "../ui/BulkActionsToolbar";

interface QuizManagementProps {
  studySetId: string;
  questions: QuizQuestion[];
  onQuestionAdded: (question: QuizQuestion) => void;
  onQuestionUpdated: (question: QuizQuestion) => void;
  onQuestionDeleted: (questionId: string) => void;
  onQuestionsGenerated: (questions: QuizQuestion[]) => void;
}

interface NewQuestion {
  question_text: string;
  question_type: QuestionType;
  options: string[];
  correct_answers: string[];
  explanation: string;
  difficulty_level: number;
}

export const QuizManagement: React.FC<QuizManagementProps> = ({
  studySetId,
  questions,
  onQuestionAdded,
  onQuestionUpdated,
  onQuestionDeleted,
  onQuestionsGenerated,
}) => {
  const { alert, confirm } = useDialog();
  const { user } = useAuthStore();
  const [isAIMode, setIsAIMode] = useState(true);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [documentPageRanges, setDocumentPageRanges] = useState<{
    [documentId: string]: { startPage: number; endPage: number };
  }>({});
  const [questionCount, setQuestionCount] = useState(25);
  const [customPrompt, setCustomPrompt] = useState("");
  const [difficultyLevel, setDifficultyLevel] = useState<DifficultyLevel>(
    DifficultyLevel.MEDIUM
  );
  const [contentLength, setContentLength] = useState<ContentLength>(
    ContentLength.MEDIUM
  );
  const [selectedQuestionTypes, setSelectedQuestionTypes] = useState<
    QuestionType[]
  >(["multiple_choice", "select_all", "true_false", "short_answer"]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newQuestion, setNewQuestion] = useState<NewQuestion>({
    question_text: "",
    question_type: "multiple_choice",
    options: ["", "", "", ""],
    correct_answers: [],
    explanation: "",
    difficulty_level: 3,
  });
  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(
    null
  );

  // Bulk selection state
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [isSelectAllChecked, setIsSelectAllChecked] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);
  const [editForm, setEditForm] = useState<NewQuestion>({
    question_text: "",
    question_type: "multiple_choice",
    options: ["", "", "", ""],
    correct_answers: [],
    explanation: "",
    difficulty_level: 3,
  });

  // Individual question reveal state - track which questions show their answer details
  const [revealedQuestions, setRevealedQuestions] = useState<Set<string>>(
    new Set()
  );

  // Toggle individual question answer details
  const toggleQuestionReveal = (questionId: string) => {
    setRevealedQuestions((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(questionId)) {
        newSet.delete(questionId);
      } else {
        newSet.add(questionId);
      }
      return newSet;
    });
  };

  // Reveal all questions
  const revealAllQuestions = () => {
    const allQuestionIds = new Set(questions.map((q) => q.id));
    setRevealedQuestions(allQuestionIds);
  };

  // Bulk selection handlers
  const handleSelectQuestion = (questionId: string, checked: boolean) => {
    if (checked) {
      setSelectedQuestions((prev) => [...prev, questionId]);
    } else {
      setSelectedQuestions((prev) => prev.filter((id) => id !== questionId));
      setIsSelectAllChecked(false);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    setIsSelectAllChecked(checked);
    if (checked) {
      setSelectedQuestions(questions.map((q) => q.id));
    } else {
      setSelectedQuestions([]);
    }
  };

  const clearSelection = () => {
    setSelectedQuestions([]);
    setIsSelectAllChecked(false);
  };

  const performBulkDeletion = async () => {
    try {
      // Delete selected questions via API
      await Promise.all(
        selectedQuestions.map((id) =>
          fetch(`/api/quiz-questions/${id}`, {
            method: "DELETE",
            headers: {
              Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
            },
          })
        )
      );
      // Update parent list
      selectedQuestions.forEach((id) => onQuestionDeleted(id));
      clearSelection();
    } catch (error) {
      console.error("Bulk deletion failed", error);
      await alert({
        title: "Error",
        message: "Failed to delete selected questions.",
        variant: "error",
      });
    } finally {
      setIsBulkDeleting(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedQuestions.length === 0) return;
    const confirmed = await confirm({
      title: "Delete Questions",
      message: `Are you sure you want to delete ${
        selectedQuestions.length
      } question$${selectedQuestions.length !== 1 ? "s" : ""}?`,
      variant: "danger",
      confirmText: "Delete",
      cancelText: "Cancel",
    });
    if (!confirmed) return;
    setIsBulkDeleting(true);
    await performBulkDeletion();
  };

  // Hide all questions
  const hideAllQuestions = () => {
    setRevealedQuestions(new Set());
  };

  // Handle page range changes
  const handlePageRangeChange = (
    documentId: string,
    range: { startPage: number; endPage: number }
  ) => {
    setDocumentPageRanges((prev) => ({
      ...prev,
      [documentId]: range,
    }));
  };

  const handleAddQuestion = async () => {
    if (!newQuestion.question_text.trim()) {
      await alert({
        title: "Validation Error",
        message: "Question text is required.",
        variant: "error",
      });
      return;
    }

    if (newQuestion.correct_answers.length === 0) {
      await alert({
        title: "Validation Error",
        message: "At least one correct answer is required.",
        variant: "error",
      });
      return;
    }

    // Validate options for multiple choice and select all questions
    if (
      newQuestion.question_type === "multiple_choice" ||
      newQuestion.question_type === "select_all"
    ) {
      const validOptions = newQuestion.options.filter(
        (opt) => opt.trim().length > 0
      );
      if (validOptions.length < 2) {
        await alert({
          title: "Validation Error",
          message:
            "Multiple choice and select all questions require at least 2 options.",
          variant: "error",
        });
        return;
      }
    }

    try {
      const response = await fetch(
        `/api/quiz-questions/study-set/${studySetId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify({
            question_text: newQuestion.question_text.trim(),
            question_type: newQuestion.question_type,
            options:
              newQuestion.question_type === "multiple_choice" ||
              newQuestion.question_type === "select_all"
                ? newQuestion.options.filter((opt) => opt.trim().length > 0)
                : null,
            correct_answers: newQuestion.correct_answers,
            explanation: newQuestion.explanation.trim() || null,
            difficulty_level: newQuestion.difficulty_level,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to create question");
      }

      const result = await response.json();
      onQuestionAdded(result.data);

      // Reset form
      setNewQuestion({
        question_text: "",
        question_type: "multiple_choice",
        options: ["", "", "", ""],
        correct_answers: [],
        explanation: "",
        difficulty_level: 3,
      });
      setShowAddForm(false);

      await alert({
        title: "Success",
        message: "Question added successfully!",
        variant: "success",
      });
    } catch (error: any) {
      await alert({
        title: "Error",
        message: error.message || "Failed to add question",
        variant: "error",
      });
    }
  };

  const handleDeleteQuestion = async (question: QuizQuestion) => {
    const confirmed = await confirm({
      title: "Delete Question",
      message: `Are you sure you want to delete this question?\n\n${question.question_text.substring(
        0,
        100
      )}${question.question_text.length > 100 ? "..." : ""}`,
      variant: "danger",
      confirmText: "Delete",
      cancelText: "Cancel",
    });

    if (!confirmed) return;

    try {
      const response = await fetch(`/api/quiz-questions/${question.id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to delete question");
      }

      onQuestionDeleted(question.id);

      await alert({
        title: "Success",
        message: "Question deleted successfully!",
        variant: "success",
      });
    } catch (error: any) {
      await alert({
        title: "Error",
        message: error.message || "Failed to delete question",
        variant: "error",
      });
    }
  };

  const handleEditQuestion = (question: QuizQuestion) => {
    setEditingQuestion(question);
    setEditForm({
      question_text: question.question_text,
      question_type: question.question_type,
      options: question.options || ["", "", "", ""],
      correct_answers: question.correct_answers,
      explanation: question.explanation || "",
      difficulty_level:
        typeof question.difficulty_level === "string"
          ? difficultyLevelToNumber(
              question.difficulty_level as DifficultyLevel
            )
          : question.difficulty_level || 3,
    });
  };

  const handleSaveEdit = async () => {
    if (!editingQuestion) return;

    if (!editForm.question_text.trim()) {
      await alert({
        title: "Validation Error",
        message: "Question text is required.",
        variant: "error",
      });
      return;
    }

    if (editForm.correct_answers.length === 0) {
      await alert({
        title: "Validation Error",
        message: "At least one correct answer is required.",
        variant: "error",
      });
      return;
    }

    // Validate options for multiple choice and select all questions
    if (
      editForm.question_type === "multiple_choice" ||
      editForm.question_type === "select_all"
    ) {
      const validOptions = editForm.options.filter(
        (opt) => opt.trim().length > 0
      );
      if (validOptions.length < 2) {
        await alert({
          title: "Validation Error",
          message:
            "Multiple choice and select all questions require at least 2 options.",
          variant: "error",
        });
        return;
      }
    }

    try {
      const response = await fetch(
        `/api/quiz-questions/${editingQuestion.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
          body: JSON.stringify({
            question_text: editForm.question_text.trim(),
            question_type: editForm.question_type,
            options:
              editForm.question_type === "multiple_choice" ||
              editForm.question_type === "select_all"
                ? editForm.options.filter((opt) => opt.trim().length > 0)
                : null,
            correct_answers: editForm.correct_answers,
            explanation: editForm.explanation.trim() || null,
            difficulty_level: editForm.difficulty_level,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update question");
      }

      const updatedQuestion = await response.json();
      onQuestionUpdated(updatedQuestion.data);
      setEditingQuestion(null);

      await alert({
        title: "Success",
        message: "Question updated successfully!",
        variant: "success",
      });
    } catch (error: any) {
      await alert({
        title: "Error",
        message: error.message || "Failed to update question",
        variant: "error",
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingQuestion(null);
    setEditForm({
      question_text: "",
      question_type: "multiple_choice",
      options: ["", "", "", ""],
      correct_answers: [],
      explanation: "",
      difficulty_level: 3,
    });
  };

  const calculateCreditCost = () => {
    // New pricing model: 1 credit = 5 quiz questions
    // So cost = Math.ceil(questionCount / 5)
    return Math.ceil(questionCount / 5);
  };

  const handleGenerateQuestions = async () => {
    if (selectedDocuments.length === 0) {
      await alert({
        title: "No Documents Selected",
        message:
          "Please select at least one document to generate questions from.",
        variant: "warning",
      });
      return;
    }

    if (selectedQuestionTypes.length === 0) {
      await alert({
        title: "No Question Types Selected",
        message: "Please select at least one question type to generate.",
        variant: "warning",
      });
      return;
    }

    const creditCost = calculateCreditCost();
    if (user && user.credits_remaining < creditCost) {
      await alert({
        title: "Insufficient Credits",
        message: `You need ${creditCost} credits to generate ${questionCount} questions, but you only have ${user.credits_remaining} credits remaining.`,
        variant: "warning",
      });
      return;
    }

    const confirmed = await confirm({
      title: "Generate Questions",
      message: `Generate ${questionCount} questions for ${creditCost} credits?`,
      confirmText: "Generate",
      cancelText: "Cancel",
    });

    if (!confirmed) return;

    setIsGenerating(true);
    try {
      const response = await fetch("/api/ai/generate-more-quiz-questions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
        },
        body: JSON.stringify({
          studySetId,
          documentIds: selectedDocuments,
          documentPageRanges,
          count: questionCount,
          customPrompt: customPrompt.trim() || undefined,
          difficultyLevel,
          contentLength,
          questionTypes: selectedQuestionTypes,
          existingContent: questions.map((q) => q.question_text),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate questions");
      }

      const result = await response.json();
      onQuestionsGenerated(result.data.questions);

      // Update user credits
      if (user) {
        useAuthStore.getState().updateUser({
          credits_remaining: result.data.creditsRemaining,
        });
      }

      // Refresh study set cache to ensure new content is loaded
      try {
        const { useStudyStore } = await import("../../stores/studyStore");
        const studyStore = useStudyStore.getState();
        await studyStore.refreshStudySetContent(studySetId);
      } catch (cacheError) {
        console.warn("Failed to refresh study set cache:", cacheError);
      }

      await alert({
        title: "Success",
        message: `Generated ${result.data.questions.length} questions successfully!`,
        variant: "success",
      });

      // Reset AI form
      setSelectedDocuments([]);
      setCustomPrompt("");
      setIsAIMode(false);
    } catch (error: any) {
      await alert({
        title: "Error",
        message: error.message || "Failed to generate questions",
        variant: "error",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleQuestionTypeChange = (type: QuestionType) => {
    setNewQuestion((prev) => ({
      ...prev,
      question_type: type,
      options:
        type === "multiple_choice" || type === "select_all"
          ? ["", "", "", ""]
          : [],
      correct_answers: [],
    }));
  };

  const handleOptionChange = (index: number, value: string) => {
    setNewQuestion((prev) => ({
      ...prev,
      options: prev.options.map((opt, i) => (i === index ? value : opt)),
    }));
  };

  const handleCorrectAnswerToggle = (answer: string) => {
    setNewQuestion((prev) => {
      const isSelected = prev.correct_answers.includes(answer);
      if (prev.question_type === "multiple_choice") {
        // Single selection for multiple choice
        return { ...prev, correct_answers: isSelected ? [] : [answer] };
      } else {
        // Multiple selection for select all
        return {
          ...prev,
          correct_answers: isSelected
            ? prev.correct_answers.filter((a) => a !== answer)
            : [...prev.correct_answers, answer],
        };
      }
    });
  };

  const toggleQuestionType = (type: QuestionType) => {
    setSelectedQuestionTypes((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
    );
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <h3 className="text-lg font-semibold text-white">
          Question Management
        </h3>

        <div className="flex flex-wrap gap-2">
          <Button
            onClick={() => setShowAddForm(!showAddForm)}
            variant="secondary"
            size="sm"
          >
            {showAddForm ? "Cancel" : "Add Question"}
          </Button>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-300">AI Mode</span>
            <button
              onClick={() => setIsAIMode(!isAIMode)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isAIMode ? "bg-primary-500" : "bg-gray-600"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAIMode ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Manual Add Form */}
      {showAddForm && (
        <div className="bg-background-secondary rounded-lg p-4 border border-gray-600">
          <h4 className="text-md font-medium text-white mb-4">
            Add New Question
          </h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Question Text
              </label>
              <textarea
                value={newQuestion.question_text}
                onChange={(e) =>
                  setNewQuestion((prev) => ({
                    ...prev,
                    question_text: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={3}
                placeholder="Enter your question..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Question Type
              </label>
              <select
                value={newQuestion.question_type}
                onChange={(e) =>
                  handleQuestionTypeChange(e.target.value as QuestionType)
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              >
                <option value="multiple_choice">Multiple Choice</option>
                <option value="select_all">Select All That Apply</option>
                <option value="true_false">True/False</option>
                <option value="short_answer">Short Answer</option>
              </select>
            </div>

            {/* Options for multiple choice and select all */}
            {(newQuestion.question_type === "multiple_choice" ||
              newQuestion.question_type === "select_all") && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Answer Options
                </label>
                <div className="space-y-2">
                  {newQuestion.options.map((option, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => handleCorrectAnswerToggle(option)}
                        className={`flex-shrink-0 w-5 h-5 border-2 ${
                          newQuestion.question_type === "multiple_choice"
                            ? "rounded-full"
                            : "rounded"
                        } ${
                          newQuestion.correct_answers.includes(option)
                            ? "bg-primary-500 border-primary-500"
                            : "border-gray-400"
                        } flex items-center justify-center`}
                        disabled={!option.trim()}
                      >
                        {newQuestion.correct_answers.includes(option) && (
                          <span className="text-white text-xs">✓</span>
                        )}
                      </button>
                      <input
                        type="text"
                        value={option}
                        onChange={(e) =>
                          handleOptionChange(index, e.target.value)
                        }
                        className="flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                        placeholder={`Option ${index + 1}`}
                      />
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {newQuestion.question_type === "multiple_choice"
                    ? "Click the circle to mark the correct answer"
                    : "Click the squares to mark all correct answers"}
                </p>
              </div>
            )}

            {/* True/False options */}
            {newQuestion.question_type === "true_false" && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Correct Answer
                </label>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={() =>
                      setNewQuestion((prev) => ({
                        ...prev,
                        correct_answers: ["True"],
                      }))
                    }
                    className={`px-4 py-2 rounded-lg border ${
                      newQuestion.correct_answers.includes("True")
                        ? "bg-primary-500 border-primary-500 text-white"
                        : "border-gray-600 text-gray-300 hover:border-gray-500"
                    }`}
                  >
                    True
                  </button>
                  <button
                    type="button"
                    onClick={() =>
                      setNewQuestion((prev) => ({
                        ...prev,
                        correct_answers: ["False"],
                      }))
                    }
                    className={`px-4 py-2 rounded-lg border ${
                      newQuestion.correct_answers.includes("False")
                        ? "bg-primary-500 border-primary-500 text-white"
                        : "border-gray-600 text-gray-300 hover:border-gray-500"
                    }`}
                  >
                    False
                  </button>
                </div>
              </div>
            )}

            {/* Short answer */}
            {newQuestion.question_type === "short_answer" && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Acceptable Answers (one per line)
                </label>
                <textarea
                  value={newQuestion.correct_answers.join("\n")}
                  onChange={(e) =>
                    setNewQuestion((prev) => ({
                      ...prev,
                      correct_answers: e.target.value
                        .split("\n")
                        .filter((a) => a.trim()),
                    }))
                  }
                  className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                  rows={3}
                  placeholder="Enter acceptable answers, one per line..."
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Explanation (Optional)
              </label>
              <textarea
                value={newQuestion.explanation}
                onChange={(e) =>
                  setNewQuestion((prev) => ({
                    ...prev,
                    explanation: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={2}
                placeholder="Explain the correct answer..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Difficulty Level
              </label>
              <select
                value={newQuestion.difficulty_level}
                onChange={(e) =>
                  setNewQuestion((prev) => ({
                    ...prev,
                    difficulty_level: parseInt(e.target.value),
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              >
                <option value={1}>1 - Very Easy</option>
                <option value={2}>2 - Easy</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Hard</option>
                <option value={5}>5 - Very Hard</option>
              </select>
            </div>

            <div className="flex space-x-3">
              <Button onClick={handleAddQuestion} variant="primary">
                Add Question
              </Button>
              <Button onClick={() => setShowAddForm(false)} variant="secondary">
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Form */}
      {editingQuestion && (
        <div className="bg-background-secondary rounded-lg p-4 border border-gray-600">
          <h4 className="text-md font-medium text-white mb-4">Edit Question</h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Question Text
              </label>
              <textarea
                value={editForm.question_text}
                onChange={(e) =>
                  setEditForm((prev) => ({
                    ...prev,
                    question_text: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={3}
                placeholder="Enter your question..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Question Type
              </label>
              <select
                value={editForm.question_type}
                onChange={(e) =>
                  setEditForm((prev) => ({
                    ...prev,
                    question_type: e.target.value as QuestionType,
                    options:
                      e.target.value === "multiple_choice" ||
                      e.target.value === "select_all"
                        ? ["", "", "", ""]
                        : [],
                    correct_answers: [],
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              >
                <option value="multiple_choice">Multiple Choice</option>
                <option value="select_all">Select All That Apply</option>
                <option value="true_false">True/False</option>
                <option value="short_answer">Short Answer</option>
              </select>
            </div>

            {/* Options for multiple choice and select all */}
            {(editForm.question_type === "multiple_choice" ||
              editForm.question_type === "select_all") && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Answer Options
                </label>
                <div className="space-y-2">
                  {editForm.options.map((option, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => {
                          const isSelected =
                            editForm.correct_answers.includes(option);
                          if (editForm.question_type === "multiple_choice") {
                            setEditForm((prev) => ({
                              ...prev,
                              correct_answers: isSelected ? [] : [option],
                            }));
                          } else {
                            setEditForm((prev) => ({
                              ...prev,
                              correct_answers: isSelected
                                ? prev.correct_answers.filter(
                                    (a) => a !== option
                                  )
                                : [...prev.correct_answers, option],
                            }));
                          }
                        }}
                        className={`flex-shrink-0 w-5 h-5 border-2 ${
                          editForm.question_type === "multiple_choice"
                            ? "rounded-full"
                            : "rounded"
                        } ${
                          editForm.correct_answers.includes(option)
                            ? "bg-primary-500 border-primary-500"
                            : "border-gray-400"
                        } flex items-center justify-center`}
                        disabled={!option.trim()}
                      >
                        {editForm.correct_answers.includes(option) && (
                          <span className="text-white text-xs">✓</span>
                        )}
                      </button>
                      <input
                        type="text"
                        value={option}
                        onChange={(e) =>
                          setEditForm((prev) => ({
                            ...prev,
                            options: prev.options.map((opt, i) =>
                              i === index ? e.target.value : opt
                            ),
                          }))
                        }
                        className="flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                        placeholder={`Option ${index + 1}`}
                      />
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {editForm.question_type === "multiple_choice"
                    ? "Click the circle to mark the correct answer"
                    : "Click the squares to mark all correct answers"}
                </p>
              </div>
            )}

            {/* True/False options */}
            {editForm.question_type === "true_false" && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Correct Answer
                </label>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={() =>
                      setEditForm((prev) => ({
                        ...prev,
                        correct_answers: ["True"],
                      }))
                    }
                    className={`px-4 py-2 rounded-lg border ${
                      editForm.correct_answers.includes("True")
                        ? "bg-primary-500 border-primary-500 text-white"
                        : "border-gray-600 text-gray-300 hover:border-gray-500"
                    }`}
                  >
                    True
                  </button>
                  <button
                    type="button"
                    onClick={() =>
                      setEditForm((prev) => ({
                        ...prev,
                        correct_answers: ["False"],
                      }))
                    }
                    className={`px-4 py-2 rounded-lg border ${
                      editForm.correct_answers.includes("False")
                        ? "bg-primary-500 border-primary-500 text-white"
                        : "border-gray-600 text-gray-300 hover:border-gray-500"
                    }`}
                  >
                    False
                  </button>
                </div>
              </div>
            )}

            {/* Short answer */}
            {editForm.question_type === "short_answer" && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Acceptable Answers (one per line)
                </label>
                <textarea
                  value={editForm.correct_answers.join("\n")}
                  onChange={(e) =>
                    setEditForm((prev) => ({
                      ...prev,
                      correct_answers: e.target.value
                        .split("\n")
                        .filter((a) => a.trim()),
                    }))
                  }
                  className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                  rows={3}
                  placeholder="Enter acceptable answers, one per line..."
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Explanation (Optional)
              </label>
              <textarea
                value={editForm.explanation}
                onChange={(e) =>
                  setEditForm((prev) => ({
                    ...prev,
                    explanation: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={2}
                placeholder="Explain the correct answer..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Difficulty Level
              </label>
              <select
                value={editForm.difficulty_level}
                onChange={(e) =>
                  setEditForm((prev) => ({
                    ...prev,
                    difficulty_level: parseInt(e.target.value),
                  }))
                }
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              >
                <option value={1}>1 - Very Easy</option>
                <option value={2}>2 - Easy</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Hard</option>
                <option value={5}>5 - Very Hard</option>
              </select>
            </div>

            <div className="flex space-x-3">
              <Button onClick={handleSaveEdit} variant="primary">
                Save Changes
              </Button>
              <Button onClick={handleCancelEdit} variant="secondary">
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* AI Generation Panel */}
      {isAIMode && (
        <div className="bg-background-secondary rounded-lg p-4 border border-gray-600">
          <h4 className="text-md font-medium text-white mb-4">
            AI Question Generation
          </h4>

          <div className="space-y-4">
            {/* Document Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Select Documents
              </label>
              <DocumentSelector
                selectedDocuments={selectedDocuments}
                onSelectionChange={setSelectedDocuments}
                documentPageRanges={documentPageRanges}
                onPageRangeChange={handlePageRangeChange}
                maxSelection={5}
              />
            </div>

            {/* Generation Controls */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <CountInput
                label="Number of Questions"
                value={questionCount}
                onChange={setQuestionCount}
                min={1}
                max={100}
                placeholder="Enter number (1-100)"
                disabled={isGenerating}
              />

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Credit Cost
                </label>
                <div className="px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium">
                  {calculateCreditCost()} credits
                </div>
              </div>
            </div>

            {/* Question Types */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Question Types
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <Checkbox
                  label="Multiple Choice"
                  checked={selectedQuestionTypes.includes("multiple_choice")}
                  onChange={() => toggleQuestionType("multiple_choice")}
                  size="sm"
                />
                <Checkbox
                  label="Select All"
                  checked={selectedQuestionTypes.includes("select_all")}
                  onChange={() => toggleQuestionType("select_all")}
                  size="sm"
                />
                <Checkbox
                  label="True False"
                  checked={selectedQuestionTypes.includes("true_false")}
                  onChange={() => toggleQuestionType("true_false")}
                  size="sm"
                />
                <Checkbox
                  label="Short Answer"
                  checked={selectedQuestionTypes.includes("short_answer")}
                  onChange={() => toggleQuestionType("short_answer")}
                  size="sm"
                />
              </div>
            </div>

            {/* Difficulty Level */}
            <DifficultySelector
              value={difficultyLevel}
              onChange={setDifficultyLevel}
            />

            {/* Content Length */}
            <ContentLengthSelector
              value={contentLength}
              onChange={setContentLength}
            />

            {/* Custom Prompt */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Custom Instructions (Optional)
              </label>
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                className="w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500"
                rows={3}
                placeholder="Add specific instructions for question generation..."
              />
            </div>

            {/* Generate Button */}
            <Button
              onClick={handleGenerateQuestions}
              disabled={
                selectedDocuments.length === 0 ||
                selectedQuestionTypes.length === 0 ||
                isGenerating
              }
              className="w-full"
              variant="primary"
            >
              {isGenerating
                ? "Generating..."
                : `Generate ${questionCount} Questions`}
            </Button>
          </div>
        </div>
      )}

      {/* AI Generation Progress */}
      <AIGenerationProgress
        isGenerating={isGenerating}
        stage={
          isGenerating ? "Generating quiz questions with AI..." : undefined
        }
        estimatedTime={Math.ceil(questionCount / 8)} // Rough estimate: 1 second per 8 questions
      />

      {/* Bulk Actions Toolbar */}
      <BulkActionsToolbar
        selectedCount={selectedQuestions.length}
        totalCount={questions.length}
        onDeleteSelected={handleBulkDelete}
        onClearSelection={clearSelection}
        isLoading={isBulkDeleting}
        className="mb-4"
        itemType="question"
      />

      {/* Question List */}
      <div className="space-y-3">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={isSelectAllChecked}
              onChange={(checked) => handleSelectAll(checked)}
            />
            <h4 className="text-md font-medium text-white">
              Current Questions ({questions.length})
            </h4>
          </div>
          {questions.length > 0 && (
            <div className="flex items-center space-x-4">
              <p className="text-sm text-gray-400">
                💡 Click on any question to reveal/hide its answer
              </p>
              <div className="flex items-center space-x-2">
                <button
                  onClick={revealAllQuestions}
                  className="flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm"
                >
                  <HiEye className="w-4 h-4" />
                  <span>Show All</span>
                </button>
                <button
                  onClick={hideAllQuestions}
                  className="flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm"
                >
                  <HiEyeOff className="w-4 h-4" />
                  <span>Hide All</span>
                </button>
              </div>
            </div>
          )}
        </div>

        {questions.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            No questions yet. Add some manually or generate them with AI.
          </div>
        ) : (
          <div className="space-y-2">
            {questions.map((question) => (
              <div
                key={question.id}
                className="bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <Checkbox
                    checked={selectedQuestions.includes(question.id)}
                    onChange={(checked) =>
                      handleSelectQuestion(question.id, checked)
                    }
                    className="mr-3 mt-1"
                  />

                  <div className="flex-1 min-w-0">
                    <div
                      className="cursor-pointer group"
                      onClick={() => toggleQuestionReveal(question.id)}
                      title={
                        revealedQuestions.has(question.id)
                          ? "Click to hide answer"
                          : "Click to reveal answer"
                      }
                    >
                      <div className="mb-2">
                        <span className="text-xs text-gray-400 uppercase tracking-wide">
                          Question
                        </span>
                        <p className="text-white font-medium group-hover:text-primary-300 transition-colors">
                          {question.question_text}
                        </p>
                      </div>

                      <div className="mb-2">
                        <span className="text-xs text-gray-400 uppercase tracking-wide">
                          Type
                        </span>
                        <p className="text-gray-300 capitalize group-hover:text-gray-200 transition-colors">
                          {question.question_type.replace("_", " ")}
                        </p>
                      </div>

                      {revealedQuestions.has(question.id) ? (
                        <>
                          {question.question_type === "true_false" && (
                            <div className="mb-2">
                              <span className="text-xs text-gray-400 uppercase tracking-wide">
                                Correct Answer
                              </span>
                              <p className="text-green-400 text-sm font-medium">
                                {question.correct_answers[0]}
                              </p>
                            </div>
                          )}

                          {question.options && (
                            <div className="mb-2">
                              <span className="text-xs text-gray-400 uppercase tracking-wide">
                                Options
                              </span>
                              <ul className="text-gray-300 text-sm group-hover:text-gray-200 transition-colors">
                                {question.options.map((option, index) => (
                                  <li
                                    key={`${question.id}-option-${index}`}
                                    className={`${
                                      question.correct_answers.includes(option)
                                        ? "text-green-400 font-medium"
                                        : ""
                                    }`}
                                  >
                                    {index + 1}. {option}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {question.explanation && (
                            <div className="mb-2">
                              <span className="text-xs text-gray-400 uppercase tracking-wide">
                                Explanation
                              </span>
                              <p className="text-gray-300 text-sm group-hover:text-gray-200 transition-colors">
                                {question.explanation}
                              </p>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="mb-2">
                          <p className="text-gray-500 italic text-sm group-hover:text-primary-400 transition-colors">
                            Click to reveal answer details...
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-gray-400">
                      {question.is_ai_generated && (
                        <span className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
                          AI Generated
                        </span>
                      )}
                      {question.difficulty_level && (
                        <span>
                          Difficulty:{" "}
                          {typeof question.difficulty_level === "string"
                            ? difficultyLevelToString(
                                question.difficulty_level as DifficultyLevel
                              )
                            : difficultyLevelToString(
                                numberToDifficultyLevel(
                                  question.difficulty_level
                                )
                              )}
                        </span>
                      )}
                      <span>
                        Attempted: {question.times_attempted || 0} times
                      </span>
                      <span>Correct: {question.times_correct || 0} times</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleEditQuestion(question)}
                      className="text-gray-400 hover:text-primary-400 p-1"
                      title="Edit question"
                    >
                      ✏️
                    </button>
                    <button
                      onClick={() => handleDeleteQuestion(question)}
                      className="text-gray-400 hover:text-red-400 p-1"
                      title="Delete question"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default QuizManagement;
