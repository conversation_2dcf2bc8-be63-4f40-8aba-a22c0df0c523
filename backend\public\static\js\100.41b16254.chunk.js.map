{"version": 3, "file": "static/js/100.41b16254.chunk.js", "mappings": "wLAwCO,MAAMA,GAAiBC,E,QAAAA,IAAoB,CAACC,EAAKC,KAAG,CACzDC,QAAS,EACTC,aAAc,GACdC,eAAgB,GAChBC,MAAO,KACPC,WAAW,EACXC,MAAO,KAEPC,aAAcC,UACZT,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,uBAAwB,CACnDC,QAAS,CACPC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,kBAK7B,IAAKN,EAASQ,GACZ,MAAM,IAAIC,MAAM,kCAGlB,MAAMC,QAAaV,EAASW,OAC5B,IAAID,EAAKE,QAGP,MAAM,IAAIH,MAAMC,EAAKb,OAAS,2BAF9BP,EAAI,CAAEE,QAASkB,EAAKA,KAAKG,QAASjB,WAAW,GAIjD,CAAE,MAAOC,GACPP,EAAI,CACFO,MAAOA,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAChDlB,WAAW,GAEf,GAGFmB,kBAAmBhB,iBAAmC,IAA5BiB,EAAKC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIG,EAAMH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAC7C3B,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,8BAADG,OACIY,EAAK,YAAAZ,OAAWgB,GAC9C,CACElB,QAAS,CACPC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,kBAM/B,IAAKN,EAASQ,GACZ,MAAM,IAAIC,MAAM,kCAGlB,MAAMC,QAAaV,EAASW,OAC5B,IAAID,EAAKE,QAOP,MAAM,IAAIH,MAAMC,EAAKb,OAAS,gCAN9BP,EAAI,CACFG,aACa,IAAX2B,EAAeV,EAAKA,KAAO,IAAInB,IAAME,gBAAiBiB,EAAKA,MAC7Dd,WAAW,GAKjB,CAAE,MAAOC,GACPP,EAAI,CACFO,MAAOA,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAChDlB,WAAW,GAEf,CACF,EAEAyB,oBAAqBtB,UACnBT,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,uBAAwB,CACnDC,QAAS,CACPC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,kBAK7B,IAAKN,EAASQ,GACZ,MAAM,IAAIC,MAAM,mCAGlB,MAAMC,QAAaV,EAASW,OAC5B,IAAID,EAAKE,QAGP,MAAM,IAAIH,MAAMC,EAAKb,OAAS,mCAF9BP,EAAI,CAAEI,eAAgBgB,EAAKA,KAAMd,WAAW,GAIhD,CAAE,MAAOC,GACPP,EAAI,CACFO,MAAOA,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAChDlB,WAAW,GAEf,GAGF0B,WAAYvB,iBAAsB,IAAfwB,EAAIN,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACxB3B,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,2BAADG,OAA4BmB,GAAQ,CAC9DrB,QAAS,CACPC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,kBAK7B,IAAKN,EAASQ,GACZ,MAAM,IAAIC,MAAM,gCAGlB,MAAMC,QAAaV,EAASW,OAC5B,IAAID,EAAKE,QAGP,MAAM,IAAIH,MAAMC,EAAKb,OAAS,yBAF9BP,EAAI,CAAEK,MAAOe,EAAKA,KAAMd,WAAW,GAIvC,CAAE,MAAOC,GACPP,EAAI,CACFO,MAAOA,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAChDlB,WAAW,GAEf,CACF,EAEA4B,gBAAiBzB,UAQfT,EAAI,CAAEM,WAAW,EAAMC,MAAO,OAC9B,IACE,MAAMG,QAAiBC,MAAM,wBAAyB,CACpDwB,OAAQ,OACRvB,QAAS,CACP,eAAgB,mBAChBC,cAAc,UAADC,OACXC,aAAaC,QAAQ,eACrBC,eAAeD,QAAQ,gBAG3BoB,KAAMC,KAAKC,UAAUC,KAGvB,IAAK7B,EAASQ,GACZ,MAAM,IAAIC,MAAM,sCAGlB,MAAMC,QAAaV,EAASW,OAC5B,GAAID,EAAKE,QAEP,OADAtB,EAAI,CAAEM,WAAW,IACV,CACLgB,SAAS,EACTkB,aAAcpB,EAAKA,KAAKoB,aACxBC,gBAAiBrB,EAAKA,KAAKqB,iBAG7B,MAAM,IAAItB,MAAMC,EAAKb,OAAS,6BAElC,CAAE,MAAOA,GACP,MAAMmC,EACJnC,aAAiBY,MAAQZ,EAAMiB,QAAU,gBAK3C,OAJAxB,EAAI,CACFO,MAAOmC,EACPpC,WAAW,IAEN,CAAEgB,SAAS,EAAOf,MAAOmC,EAClC,GAGFC,WAAYA,IAAM3C,EAAI,CAAEO,MAAO,OAE/BqC,qBAAsBnC,UACpB,UACQoC,QAAQC,IAAI,CAAC7C,IAAMO,eAAgBP,IAAMwB,qBACjD,CAAE,MAAOlB,GACPwC,QAAQxC,MAAM,yCAA0CA,EAC1D,M,kCCvNG,MAAMyC,EAA8CC,IAIpD,IAJqD,QAC1D/C,EAAO,SACPgD,EAAQ,UACR5C,GACD2C,EACC,MAAME,EAAgBC,IACpB,OAAQA,EAAKC,eACX,IAAK,MACH,MAAO,kBACT,IAAK,QACH,MAAO,gBACT,QACE,MAAO,kBAsBPC,EAPoBpD,IACpBA,GAAW,IAAY,CAAEqD,MAAO,iBAAkBC,OAAQ,aAC1DtD,GAAW,GAAW,CAAEqD,MAAO,kBAAmBC,OAAQ,QAC1DtD,GAAW,GAAW,CAAEqD,MAAO,kBAAmBC,OAAQ,OACvD,CAAED,MAAO,eAAgBC,OAAQ,YAGpBC,CAAiBvD,GAEvC,OACEwD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBT,UAAU,mHAAkHC,SAAA,EAE5HF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,mCAAkCC,UAC/CS,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,gCAE1BD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,mCAAkCC,SAAC,oBACjDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAC,uCAIzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAS,+BAAA7C,OAAiCqC,EAAaD,IAAYU,SAAA,CAxC3DR,KACnB,OAAQA,EAAKC,eACX,IAAK,MACH,OAAOgB,EAAAA,EAAAA,KAACE,EAAAA,IAAU,CAACZ,UAAU,YAC/B,IAAK,QACH,OAAOU,EAAAA,EAAAA,KAACG,EAAAA,IAAY,CAACb,UAAU,YACjC,QACE,OAAOU,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,cAkC1Bc,CAAYvB,IACbQ,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEV,EAAS,kBAI5CQ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2BAA0BC,SAAA,EACvCS,EAAAA,EAAAA,KAAA,OAAAT,SACGtD,GACC+D,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,qCAGjBD,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEW,MAAO,IAClBR,QAAS,CAAEQ,MAAO,GAClBP,WAAY,CAAEC,SAAU,GAAKO,MAAO,IAAMf,SAAA,EAE1CS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,gCAA+BC,SAAE1D,EAAQ0E,oBACzDP,EAAAA,EAAAA,KAAA,QAAMV,UAAU,6BAA4BC,SAAC,kBAKnDF,EAAAA,EAAAA,MAAA,OAAKC,UAAS,+BAAA7C,OAAiCwC,EAAcC,MAAK,SAAQK,SAAA,EACxES,EAAAA,EAAAA,KAAA,OAAKV,UAAS,wBAAA7C,OAA0BwC,EAAcC,MAAMsB,QAAQ,QAAS,WAC7ER,EAAAA,EAAAA,KAAA,QAAMV,UAAU,sBAAqBC,SAAEN,EAAcE,eAIxDtD,EAAU,KACTwD,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,GACpBE,QAAS,CAAEF,QAAS,GACpBG,WAAY,CAAEQ,MAAO,IACrBhB,UAAU,6DAA4DC,SAAA,EAEtEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAACS,EAAAA,IAAO,CAACnB,UAAU,0BACnBU,EAAAA,EAAAA,KAAA,QAAMV,UAAU,mCAAkCC,SAAC,4BAErDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,4BAA2BC,SAAC,2EAQ/CF,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKO,MAAO,IACpChB,UAAU,sEAAqEC,SAAA,EAE/ES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,iBAEtDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAC,eACxCS,EAAAA,EAAAA,KAAA,QAAMV,UAAS,eAAA7C,OAAiBqC,EAAaD,IAAYU,SAAEV,QAG7DQ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAC,YACxCS,EAAAA,EAAAA,KAAA,QAAMV,UAAS,eAAA7C,OAAiBwC,EAAcC,OAAQK,SAAEN,EAAcE,aAGxEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAC,uBACxCS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,yBAAwBC,SAAE1D,OAGhB,SAA3BgD,EAASG,gBACRgB,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCAAuCC,UACpDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAC,sEChItCmB,EAA8C9B,IAIpD,IAJqD,aAC1D9C,EAAY,UACZG,EAAS,WACT0E,GACD/B,EACC,MAAOgC,EAAQC,IAAaC,EAAAA,EAAAA,UAAuC,QAC5DC,EAAQC,IAAaF,EAAAA,EAAAA,UAA4B,QAUlDG,EAAuBC,GACpBA,EAAc,EAAI,eAAiB,iBAWtCC,EAAuBrF,EAAa8E,OAAOQ,GAChC,SAAXR,EAA0BQ,EAAYC,aAAe,EAC1C,cAAXT,GAA+BQ,EAAYC,aAAe,GAI1DC,EAAqB,IAAIH,GAAsBI,KAAK,CAACC,EAAGC,IAC7C,SAAXV,EACK,IAAIW,KAAKD,EAAEE,YAAYC,UAAY,IAAIF,KAAKF,EAAEG,YAAYC,UAE1DC,KAAKC,IAAIL,EAAEJ,cAAgBQ,KAAKC,IAAIN,EAAEH,eAyBjD,OACEhC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAElFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,mCAAkCC,SAAC,yBACjDF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CACjC4B,EAAqB5D,OAAO,OAAKzB,EAAayB,OAAO,uBAI1D8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAE1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,MAAA,UACE0C,MAAOnB,EACPoB,SAAWC,GAAMpB,EAAUoB,EAAEC,OAAOH,OACpCzC,UAAU,4KAA2KC,SAAA,EAErLS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,MAAKxC,SAAC,sBACpBS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,OAAMxC,SAAC,kBACrBS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,YAAWxC,SAAC,0BAE5BS,EAAAA,EAAAA,KAACmC,EAAAA,IAAa,CAAC7C,UAAU,sGAI3BD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,MAAA,UACE0C,MAAOhB,EACPiB,SAAWC,GAAMjB,EAAUiB,EAAEC,OAAOH,OACpCzC,UAAU,4KAA2KC,SAAA,EAErLS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,OAAMxC,SAAC,kBACrBS,EAAAA,EAAAA,KAAA,UAAQ+B,MAAM,SAAQxC,SAAC,uBAEzBS,EAAAA,EAAAA,KAACmC,EAAAA,IAAa,CAAC7C,UAAU,sGAI3BD,EAAAA,EAAAA,MAAC+C,EAAAA,EAAM,CACLC,QA9DiBC,KACzB,MAAMC,EAAa,CACjB,CAAC,OAAQ,OAAQ,UAAW,YAAa,eAAeC,KAAK,QAC1DlB,EAAmBmB,IAAIrB,GAAe,CACvC,IAAIM,KAAKN,EAAYO,YAAYe,qBACjCtB,EAAYC,aAAe,EAAI,OAAS,YACxCQ,KAAKC,IAAIV,EAAYC,cACrBD,EAAYuB,eAAe,IAADlG,OACtB2E,EAAYwB,YAAW,MAC3BJ,KAAK,OACPA,KAAK,MAEDK,EAAO,IAAIC,KAAK,CAACP,GAAa,CAAEQ,KAAM,aACtCC,EAAMC,OAAOC,IAAIC,gBAAgBN,GACjCrB,EAAI4B,SAASC,cAAc,KACjC7B,EAAE8B,KAAON,EACTxB,EAAE+B,SAAQ,kBAAA9G,QAAqB,IAAIiF,MAAO8B,cAAcC,MAAM,KAAK,GAAE,QACrEjC,EAAEkC,QACFT,OAAOC,IAAIS,gBAAgBX,IA6CnBY,QAAQ,YACRC,KAAK,KACLC,SAAkC,IAAxBhI,EAAayB,OAAagC,SAAA,EAEpCS,EAAAA,EAAAA,KAAC+D,EAAAA,IAAU,CAACzE,UAAU,iBAAiB,mBAO7CU,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SACvBtD,GAAqC,IAAxBH,EAAayB,QACzByC,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SACvB,IAAIyE,MAAM,IAAIvB,IAAI,CAACwB,EAAGC,KACrBlE,EAAAA,EAAAA,KAAA,OAAaV,UAAU,gBAAeC,UACpCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sCACfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCACfU,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sCAEjBU,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uCARX4E,MAckB,IAA9B5C,EAAmB/D,QACrB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCS,EAAAA,EAAAA,KAACS,EAAAA,IAAO,CAACnB,UAAU,0CACnBU,EAAAA,EAAAA,KAAA,MAAIV,UAAU,yCAAwCC,SAAC,2BACvDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,gBAAeC,SACd,QAAXqB,EACG,gDAA+C,MAAAnE,OACzCmE,EAAM,6BAKpBU,EAAmBmB,IAAI,CAACrB,EAAa+C,KACnC,MAAM,KAAEC,EAAI,KAAEC,GA/HJC,KAClB,MAAMF,EAAO,IAAI1C,KAAK4C,GACtB,MAAO,CACLF,KAAMA,EAAK1B,qBACX2B,KAAMD,EAAKG,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,cA2HhCC,CAAWtD,EAAYO,YACxCgD,EAAWvD,EAAYC,aAAe,EAE5C,OACErB,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKO,MAAe,IAAR6D,GACpC7E,UAAU,qHAAoHC,UAE9HF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAAA,OAAKV,UAAS,oBAAA7C,OAAsBkI,EAAW,kBAAoB,iBAAkBpF,UAxJ3E2B,EAyJYE,EAAYC,aAxJ3CH,EAAc,GACnBlB,EAAAA,EAAAA,KAAC4E,EAAAA,IAAO,CAACtF,UAAU,0BAEnBU,EAAAA,EAAAA,KAAC6E,EAAAA,IAAM,CAACvF,UAAU,+BAwJJD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,yBAAwBC,SACnC6B,EAAYuB,eAAenC,QAAQ,KAAM,KAAKA,QAAQ,QAASsE,GAAKA,EAAEC,kBAEzE/E,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAE6B,EAAYwB,eAClDvD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAE6E,KACzCpE,EAAAA,EAAAA,KAAA,QAAMV,UAAU,gBAAeC,SAAC,YAChCS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAE8E,cAK/ChF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,MAAA,QAAMC,UAAS,iBAAA7C,OAAmBwE,EAAoBG,EAAYC,eAAgB9B,SAAA,CAC/EoF,EAAW,IAAM,IAAK9C,KAAKC,IAAIV,EAAYC,cAAc,cAE3DD,EAAY4D,eACXhF,EAAAA,EAAAA,KAAA,KAAGV,UAAU,6BAA4BC,SAAC,qBA9B3C6B,EAAY6D,IAhJH/D,UAyLvBpF,EAAayB,OAAS,GAAKzB,EAAayB,OAAS,KAAO,IACvDyC,EAAAA,EAAAA,KAAA,OAAKV,UAAU,mBAAkBC,UAC/BS,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CACLC,QAAS1B,EACTiD,QAAQ,YACR3H,UAAWA,EACX6H,SAAU7H,EAAUsD,SACrB,iCCpNE2F,EAA+B,CAC1C,CACED,GAAI,OACJE,KAAM,OACNC,MAAO,EACPlI,QAAS,GACTmI,SAAU,CACR,uBACA,sBACA,8BACA,oBACA,mBAEFC,mBAAe9H,GAEjB,CACEyH,GAAI,gBACJE,KAAM,gBACNC,MAAO,GACPlI,QAAS,IACTmI,SAAU,CACR,wBACA,yBACA,+BACA,gBACA,qBACA,wBAEFC,cAAe,6BAEjB,CACEL,GAAI,YACJE,KAAM,YACNC,MAAO,GACPlI,QAAS,KACTmI,SAAU,CACR,0BACA,wBACA,+BACA,mBACA,qBACA,oBACA,0BAEFE,WAAW,EACXD,cAAe,yBAEjB,CACEL,GAAI,eACJE,KAAM,eACNC,MAAO,IACPlI,QAAS,KACTmI,SAAU,CACR,0BACA,wBACA,+BACA,mBACA,qBACA,oBACA,qBACA,cAEFC,cAAe,4BAEjB,CACEL,GAAI,cACJE,KAAM,cACNC,MAAO,IACPlI,QAAS,KACTmI,SAAU,CACR,0BACA,wBACA,4BACA,oBACA,qBACA,oBACA,qBACA,aACA,uBAEFC,cAAe,4BChENE,EAAgD5G,IAItD,IAJuD,eAC5D6G,EAAc,SACd5G,EAAQ,mBACR6G,GACD9G,EACC,MAAO+G,EAAiBC,IAAsB9E,EAAAA,EAAAA,UAAwB,OAC/D+E,EAAcC,IAAmBhF,EAAAA,EAAAA,WAAS,IAC1C5E,EAAO6J,IAAYjF,EAAAA,EAAAA,UAAwB,OAC3C7D,EAAS+I,IAAclF,EAAAA,EAAAA,UAAwB,OAC/CmF,EAAcC,IAAmBpF,EAAAA,EAAAA,UAAiB,KAClDqF,EAAmBC,IAAwBtF,EAAAA,EAAAA,WAAS,IACpDuF,EAAWC,IAAgBxF,EAAAA,EAAAA,UAChC,kBAEI,qBAAEvC,GAAyB9C,KAC3B,KAAE8K,IAASC,EAAAA,EAAAA,KAsCXpG,EAAeqG,IACnB,OAAQA,GACN,IAAK,gBACH,OAAOzG,EAAAA,EAAAA,KAAC0G,EAAAA,IAAa,CAACpH,UAAU,YAClC,IAAK,YACH,OAAOU,EAAAA,EAAAA,KAAC2G,EAAAA,IAAM,CAACrH,UAAU,YAC3B,IAAK,eACH,OAAOU,EAAAA,EAAAA,KAAC4G,EAAAA,IAAe,CAACtH,UAAU,YACpC,IAAK,cACH,OAAOU,EAAAA,EAAAA,KAACE,EAAAA,IAAU,CAACZ,UAAU,YAC/B,QACE,OAAOU,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,cAQrC,OACED,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvBrD,IACC8D,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wDAAuDC,UACpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAAA,OACEV,UAAU,uBACVuH,QAAQ,YACRC,KAAK,eAAcvH,UAEnBS,EAAAA,EAAAA,KAAA,QACE+G,SAAS,UACTC,EAAE,0NACFC,SAAS,iBAIf5H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,mCAAkCC,SAAC,WACjDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,4BAA2BC,SAAErD,QAE9C8D,EAAAA,EAAAA,KAAA,OAAKV,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,UACEgD,QAASA,IAAM0D,EAAS,MACxBzG,UAAU,+KAA8KC,SAAA,EAExLS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,UAASC,SAAC,aAC1BS,EAAAA,EAAAA,KAAA,OACEV,UAAU,UACVuH,QAAQ,YACRC,KAAK,eAAcvH,UAEnBS,EAAAA,EAAAA,KAAA,QACE+G,SAAS,UACTC,EAAE,qMACFC,SAAS,wBAUtBhK,IACC+C,EAAAA,EAAAA,KAAA,OAAKV,UAAU,4DAA2DC,UACxEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAAA,OACEV,UAAU,yBACVuH,QAAQ,YACRC,KAAK,eAAcvH,UAEnBS,EAAAA,EAAAA,KAAA,QACE+G,SAAS,UACTC,EAAE,wIACFC,SAAS,iBAIf5H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,qCAAoCC,SAAC,aACnDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,8BAA6BC,SAAEtC,QAEhD+C,EAAAA,EAAAA,KAAA,OAAKV,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,UACEgD,QAASA,IAAM2D,EAAW,MAC1B1G,UAAU,yLAAwLC,SAAA,EAElMS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,UAASC,SAAC,aAC1BS,EAAAA,EAAAA,KAAA,OACEV,UAAU,UACVuH,QAAQ,YACRC,KAAK,eAAcvH,UAEnBS,EAAAA,EAAAA,KAAA,QACE+G,SAAS,UACTC,EAAE,qMACFC,SAAS,yBAUvBjH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sEAAqEC,UAClFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mCAAkCC,SAAA,CAAC,iBAChCV,MAEjBmB,EAAAA,EAAAA,KAAA,KAAGV,UAAU,gBAAeC,SAAC,+BAE/BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,sCAAqCC,SAClDkG,KAEHzF,EAAAA,EAAAA,KAAA,QAAMV,UAAU,qBAAoBC,SAAC,qBAM3CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnES,EAAAA,EAAAA,KAAA,UACEqC,QAASA,IAAMiE,EAAa,iBAC5BhH,UAAS,qEAAA7C,OACO,kBAAd4J,EACI,4BACA,kCACH9G,SACJ,mBAGDS,EAAAA,EAAAA,KAAA,UACEqC,QAASA,IAAMiE,EAAa,WAC5BhH,UAAS,qEAAA7C,OACO,YAAd4J,EACI,4BACA,kCACH9G,SACJ,wBAMY,YAAd8G,IACChH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2EAA0EC,SAAA,EACvFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,iCAAgCC,SAAC,2BAG/CS,EAAAA,EAAAA,KAAA,UACEqC,QAASA,IAAM+D,GAAsBD,GACrC7G,UAAU,oEAAmEC,SAE5E4G,EAAoB,OAAS,kBAIjCA,IACC9G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BS,EAAAA,EAAAA,KAAA,SACE+C,KAAK,OACLhB,MAAOkE,EACPjE,SAAWC,GAAMiE,EAAgBjE,EAAEC,OAAOH,MAAMgD,eAChDmC,YAAY,sBACZ5H,UAAU,4LACV6H,UAAW,MAEbnH,EAAAA,EAAAA,KAAA,UACEqC,QAASA,KACP6D,EAAgB,IAChBE,GAAqB,IAEvB9G,UAAU,6DAA4DC,SACvE,gBASM,kBAAd8G,IACChH,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,4BAGtDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,qBAAoBC,SAAC,0EAIlCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uDAAsDC,SAClE2F,EAActE,OAAQ7B,GAAqB,SAAZA,EAAKkG,IAAexC,IAClD,CAAC1D,EAAmBoF,KAClB,MAAMiD,EAAazB,IAAoB5G,EAAKkG,GACtCoC,EAAmBxB,GAAgBuB,EACnCE,GA9LGb,EA8LuB1H,EAAKkG,GA7L1CpG,IAAa4H,GADCA,MAgMT,OACEpH,EAAAA,EAAAA,MAACG,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKO,MAAe,GAAR6D,GACpC7E,UAAS,iIAAA7C,OAGPsC,EAAKwG,UACD,gDACA,sDAAqD,0BAAA9I,OAEzD2K,EAAa,6BAA+B,GAAE,0BAAA3K,OAC9C6K,EAAY,2BAA6B,GAAE,wBAC7C/H,SAAA,CAGCR,EAAKwG,YACJvF,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sDAAqDC,UAClES,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uEAAsEC,SAAC,mBAOzF+H,IACCtH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,0BAAyBC,UACtCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,qEAAoEC,SAAC,oBAMxFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAE1BS,EAAAA,EAAAA,KAAA,OACEV,UAAS,mCAAA7C,OACPsC,EAAKwG,UACD,qCACA,wCACHhG,SAEFa,EAAYrB,EAAKkG,OAIpBjF,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAClDR,EAAKoG,QAIR9F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,gCAA+BC,SAC5CR,EAAK7B,WAER8C,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wBAAuBC,SAAC,mBAGvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,CAAC,IACrB,EAAfR,EAAK7B,QAAY,6BAKvBmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gCAA+BC,SAAA,CAAC,IAC5CR,EAAKqG,UAETpF,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wBAAuBC,SAAC,YACvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,CAAC,KAClCR,EAAKqG,MAAQrG,EAAK7B,SAASqK,QAAQ,GAAG,qBAK7CvH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,iBAAgBC,SAC5BR,EAAKsG,SAAS5C,IACb,CAAC+E,EAAiBC,KAChBpI,EAAAA,EAAAA,MAAA,OAEEC,UAAU,0CAAyCC,SAAA,EAEnDS,EAAAA,EAAAA,KAAC0H,EAAAA,IAAO,CAACpI,UAAU,+CACnBU,EAAAA,EAAAA,KAAA,QAAAT,SAAOiI,MAJFC,OAWbzH,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CACLC,QAASA,IAhVIjG,WACjC,MAAMuL,EAAezC,EAAc0C,KAAM7I,GAASA,EAAKkG,KAAOwB,GAC9D,GAAKkB,EAKL,GAAS,OAAJpB,QAAI,IAAJA,GAAAA,EAAMsB,MAAX,CAKA/B,GAAgB,GAChBF,EAAmBa,GACnBV,EAAS,MACTC,EAAW,MAEX,IAEEtH,QAAQoJ,IAAI,kCAAmCH,GAC/C3B,EAAW,mBAADvJ,OACWkL,EAAaxC,KAAI,oDAAA1I,OAAmDkL,EAAavC,MAAK,iBAIrG7G,IACNmH,GACF,CAAE,MAAOxJ,GACPwC,QAAQxC,MAAM,sBAAuBA,GACrC6J,EAAS,mDACX,CAAC,QACCD,GAAgB,GAChBF,EAAmB,KACrB,CAvBA,MAFEG,EAAS,kEALTA,EAAS,4DA6UwBgC,CAA2BhJ,EAAKkG,IAC/CrB,QAAS7E,EAAKwG,UAAY,UAAY,YACtCjG,UAAU,SACVrD,UAAWoL,EACXvD,SAAU+B,KAAqB,OAAJU,QAAI,IAAJA,GAAAA,EAAMsB,QAASP,EAAU/H,SAEnD8H,EACG,gBACAC,EACA,eACK,OAAJf,QAAI,IAAJA,GAAAA,EAAMsB,MACS,gBAAApL,OACAsC,EAAKoG,MADrB,wBAtGHpG,EAAKkG,WAmHT,YAAdoB,IACChH,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,8BAGtDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,qBAAoBC,SAAC,qEAIlCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCS,EAAAA,EAAAA,KAACE,EAAAA,IAAU,CAACZ,UAAU,0CACtBU,EAAAA,EAAAA,KAAA,MAAIV,UAAU,yCAAwCC,SAAC,mCAGvDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,gBAAeC,SAAC,wGASnCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAClFS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SACpC,kBAAd8G,EACG,wBACA,gCAENhH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,8BAA6BC,SAAC,mBAC5CS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAC,uJAMvCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,8BAA6BC,SAC1B,kBAAd8G,EACG,iBACA,kBAENrG,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SACnB,kBAAd8G,EACG,wGACA,oHC7aL2B,EAAoDpJ,IAG1D,IAH2D,MAChE5C,EAAK,eACLD,GACD6C,EACC,MAAMqJ,GAAYC,EAAAA,EAAAA,SAAQ,KAExB,MAAMC,EAAYnE,MAAMoE,KAAK,CAAE7K,OAAQ,GAAK,CAAC0G,EAAGC,KAC9C,MAAME,EAAO,IAAI1C,KAEjB,OADA0C,EAAKiE,QAAQjE,EAAKkE,WAAa,EAAIpE,IAC5B,CACLE,KAAMA,EAAK1B,mBAAmB,QAAS,CAAE6F,QAAS,UAClDC,SAAUpE,EAAKZ,cAAcC,MAAM,KAAK,GACxCvG,QAAS,KAcb,OATS,OAALlB,QAAK,IAALA,GAAAA,EAAOyM,YAAczE,MAAM0E,QAAQ1M,EAAMyM,aAC3CzM,EAAMyM,WAAWE,QAAQC,IACvB,MAAMC,EAAWV,EAAUW,UAAUC,GAAOA,EAAIP,WAAaI,EAAMxE,OACjD,IAAdyE,IACFV,EAAUU,GAAU3L,QAAU0L,EAAM1L,WAKnCiL,GACN,CAAM,OAALnM,QAAK,IAALA,OAAK,EAALA,EAAOyM,aAELO,EAAWnH,KAAKoH,OAAOhB,EAAUxF,IAAIuE,GAAKA,EAAE9J,SAAU,GACtDgM,EAAmBjB,EAAUkB,OAAO,CAACC,EAAKL,IAAQK,EAAML,EAAI7L,QAAS,GACrEmM,EAAoBH,EAAmB,EAGvCI,EAAYrB,EAAUsB,MAAM,EAAG,GAAGJ,OAAO,CAACC,EAAKL,IAAQK,EAAML,EAAI7L,QAAS,GAAK,EAC/EsM,EAAavB,EAAUsB,MAAM,GAAGJ,OAAO,CAACC,EAAKL,IAAQK,EAAML,EAAI7L,QAAS,GAAK,EAC7EuM,EAAQD,EAAaF,EAAY,KAAOE,EAAaF,EAAY,OAAS,SAC1EI,EAAkBJ,EAAY,EAAIzH,KAAKC,KAAM0H,EAAaF,GAAaA,EAAa,KAAO,EAEjG,OACEjK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,mCAAkCC,SAAC,kBACjDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,wBAAuBC,SAAC,oBAGvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,CAC/B,OAAVkK,GACCzJ,EAAAA,EAAAA,KAACG,EAAAA,IAAY,CAACb,UAAU,2BACZ,SAAVmK,GACFzJ,EAAAA,EAAAA,KAAC2J,EAAAA,IAAc,CAACrK,UAAU,yBACxB,KAEO,WAAVmK,IACCpK,EAAAA,EAAAA,MAAA,QAAMC,UAAS,uBAAA7C,OACH,OAAVgN,EAAiB,iBAAmB,gBACnClK,SAAA,CACAmK,EAAgBnC,QAAQ,GAAG,cAOpCvH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,OAAMC,UACnBS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gDAA+CC,SAC3D0I,EAAUxF,IAAI,CAACsG,EAAK5E,KACnB,MAAMyF,EAASZ,EAAW,EAAKD,EAAI7L,QAAU8L,EAAY,IAAM,EAE/D,OACE3J,EAAAA,EAAAA,MAAA,OAAoBC,UAAU,oCAAmCC,SAAA,EAC/DS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,kCAAiCC,UAC9CS,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEkK,OAAQ,GACnB/J,QAAS,CAAE+J,OAAO,GAADnN,OAAKmN,EAAM,MAC5B9J,WAAY,CAAEC,SAAU,GAAKO,MAAe,GAAR6D,GACpC7E,UAAU,8FACVuK,MAAO,CAAEC,UAAWF,EAAS,EAAI,MAAQ,OAAQrK,UAGjDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,qHAAoHC,UACjIF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6GAA4GC,SAAA,CACxHwJ,EAAI7L,QAAQ,qBAMrB8C,EAAAA,EAAAA,KAAA,QAAMV,UAAU,wBAAuBC,SAAEwJ,EAAI3E,SAlBrC2E,EAAI3E,aA0BtB/E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACrCS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gCAA+BC,SAAE2J,KAChDlJ,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wBAAuBC,SAAC,0BAI3CS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gCAA+BC,SAAE8J,EAAkB9B,QAAQ,MAC1EvH,EAAAA,EAAAA,KAAA,OAAKV,UAAU,wBAAuBC,SAAC,2BAM7CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,sCAAqCC,SAAC,sBACpDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SAClB,OAALvD,QAAK,IAALA,GAAAA,EAAO+N,iBAAmBC,OAAOC,QAAQjO,EAAM+N,kBAAkBR,MAAM,EAAG,GAAG9G,IAAIyH,IAA2B,IAAzBC,EAAWjN,GAAQgN,EACrG,MAAME,EAA8B,OAAdrO,QAAc,IAAdA,OAAc,EAAdA,EAAgB6L,KAAKyC,GAAQA,EAAK1H,iBAAmBwH,GACrEG,EAAcF,EAAgBlN,EAAUkN,EAAcG,sBAAwB,EAEpF,OACElL,EAAAA,EAAAA,MAAA,OAAqBC,UAAU,4CAA2CC,SAAA,EACxES,EAAAA,EAAAA,KAAA,QAAMV,UAAU,2BAA0BC,SACvC4K,EAAU3J,QAAQ,KAAM,QAE3BnB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,aAAYC,SAAA,CACzB+K,EAAY,oBALPH,MAUZnK,EAAAA,EAAAA,KAAA,OAAKV,UAAU,yCAAwCC,SAAC,qCC5H9DiL,EAA2B,CAC/B,CACEvF,GAAI,WACJwF,MAAO,WACPC,KAAMC,EAAAA,IACN/H,YAAa,oCAEf,CACEqC,GAAI,UACJwF,MAAO,sBACPC,KAAMjK,EAAAA,IACNmC,YAAa,mCAEf,CACEqC,GAAI,WACJwF,MAAO,cACPC,KAAME,EAAAA,IACNhI,YAAa,gCAIXiI,EAA2BC,IAC/B,OAAQA,GACN,IAAK,kBACH,MAAO,iBACT,IAAK,uBACH,MAAO,aACT,IAAK,qBACH,MAAO,mBACT,QACE,OACEA,EAActK,QAAQ,KAAM,KAAKA,QAAQ,aAAc,IAAIuK,OAAS,MAK/DC,EAAwBA,KACnC,MAAO3E,EAAWC,IAAgBxF,EAAAA,EAAAA,UAAS,aACrC,KAAEyF,IAASC,EAAAA,EAAAA,MACX,QACJ3K,EAAO,aACPC,EAAY,eACZC,EAAc,MACdC,EAAK,UACLC,EAAS,MACTC,EAAK,aACLC,EAAY,kBACZiB,EAAiB,oBACjBM,EAAmB,WACnBC,EAAU,WACVW,GACE7C,KAEJwP,EAAAA,EAAAA,WAAU,KAER9O,IACAiB,IACAM,IACAC,KACC,CAACxB,EAAciB,EAAmBM,EAAqBC,IAE1D,MAAMuN,EAAgB9O,UACpBkC,UACME,QAAQC,IAAI,CAChBtC,IACAiB,IACAM,IACAC,OAIEwN,EAAoBA,KACxB9L,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBS,EAAAA,EAAAA,KAACrB,EAAa,CACZ9C,QAASA,EACTgD,UAAc,OAAJ0H,QAAI,IAAJA,OAAI,EAAJA,EAAM6E,oBAAqB,OACrCnP,UAAWA,IAIZD,IACCqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDS,EAAAA,EAAAA,KAACgI,EAAgB,CAAChM,MAAOA,EAAOD,eAAgBA,KAEhDsD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAClFS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,qBAGtDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SAClB,OAALvD,QAAK,IAALA,GAAAA,EAAO+N,iBACNC,OAAOC,QAAQjO,EAAM+N,kBAAkBtH,IACrC7D,IAAA,IAAEuL,EAAWjN,GAAQ0B,EAAA,OACnBS,EAAAA,EAAAA,MAAA,OAEEC,UAAU,oCAAmCC,SAAA,EAE7CS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,2BAA0BC,SACvC4K,EAAU3J,QAAQ,KAAM,QAE3BnB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,yBAAwBC,SAAA,CACrCrC,EAAQ,gBAPNiN,MAaXnK,EAAAA,EAAAA,KAAA,OAAKV,UAAU,iCAAgCC,SAAC,qCAU1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEC,SAAA,EAClFS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,wCAAuCC,SAAC,kBACtDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,uDAAsDC,UACpD,OAAdxD,QAAc,IAAdA,OAAc,EAAdA,EAAgBwB,QAAS,EACxBxB,EAAe0G,IAAK4H,IAClBhL,EAAAA,EAAAA,MAAA,OAEEC,UAAU,uEAAsEC,SAAA,EAEhFS,EAAAA,EAAAA,KAAA,MAAIV,UAAU,yCAAwCC,SACnD8K,EAAK1H,eAAenC,QAAQ,KAAM,QAErCnB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAACC,EAAAA,IAAY,CAACX,UAAU,8BACxBD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,iCAAgCC,SAAA,CAAC,cACnC8K,EAAKE,sBAAuB,IACvCM,EAAwBR,EAAK1H,wBAV7B0H,EAAK1H,kBAgBd3C,EAAAA,EAAAA,KAAA,OAAKV,UAAU,+CAA8CC,SAAC,6CAsCxE,OACES,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACES,EAAAA,EAAAA,KAAA,MAAIV,UAAU,qCAAoCC,SAAC,aACnDS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,gBAAeC,SAAC,0CAG/BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,8BAA6BC,UAC1CF,EAAAA,EAAAA,MAAC+C,EAAAA,EAAM,CACLC,QAAS6I,EACTtH,QAAQ,YACRE,SAAU7H,EAAUsD,SAAA,EAEpBS,EAAAA,EAAAA,KAACqL,EAAAA,IAAS,CACR/L,UAAS,gBAAA7C,OAAkBR,EAAY,eAAiB,MACxD,kBAOPC,IACCmD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CS,EAAAA,EAAAA,KAACsL,EAAAA,IAAmB,CAAChM,UAAU,0BAC/BU,EAAAA,EAAAA,KAAA,QAAMV,UAAU,2BAA0BC,SAAC,cAE7CS,EAAAA,EAAAA,KAAA,KAAGV,UAAU,oBAAmBC,SAAErD,KAClC8D,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CACLC,QAAS/D,EACTsF,QAAQ,YACRC,KAAK,KACLvE,UAAU,OAAMC,SACjB,gBAMLF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,sEAAqEC,UAClFS,EAAAA,EAAAA,KAAA,OAAKV,UAAU,YAAWC,SACvBiL,EAAW/H,IAAK8I,IACf,MAAMC,EAAOD,EAAIb,KACXe,EAAWpF,IAAckF,EAAItG,GAEnC,OACE5F,EAAAA,EAAAA,MAAA,UAEEgD,QAASA,IAAMiE,EAAaiF,EAAItG,IAChC3F,UAAS,6KAAA7C,OAILgP,EACI,kEACA,8DAA6D,4BAEnElM,SAAA,EAEFS,EAAAA,EAAAA,KAACwL,EAAI,CAAClM,UAAU,aAChBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BS,EAAAA,EAAAA,KAAA,QAAMV,UAAU,oBAAmBC,SAAEgM,EAAId,SACzCzK,EAAAA,EAAAA,KAAA,QAAMV,UAAU,uCAAsCC,SACnDgM,EAAI3I,mBAhBJ2I,EAAItG,aA2BrBjF,EAAAA,EAAAA,KAAA,OAAKV,UAAU,gBAAeC,UAC5BS,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAG+L,EAAG,IAC1B7L,QAAS,CAAEF,QAAS,EAAG+L,EAAG,GAC1B5L,WAAY,CAAEC,SAAU,IAAMR,SAnGpBoM,MACpB,OAAQtF,GACN,IAAK,WAML,QACE,OAAO8E,IALT,IAAK,UACH,OApBJnL,EAAAA,EAAAA,KAACU,EAAa,CACZ5E,aAAcA,EACdG,UAAWA,EACX0E,WAAYA,IAAMvD,EAAkB,GAAItB,EAAayB,UAkBrD,IAAK,WACH,OAdJyC,EAAAA,EAAAA,KAACwF,EAAc,CACbC,eAAgB5J,EAChBgD,UAAc,OAAJ0H,QAAI,IAAJA,OAAI,EAAJA,EAAM6E,oBAAqB,gBACrC1F,mBAAoBwF,MAyGXS,IALItF,a", "sources": ["stores/creditStore.ts", "components/credits/CreditBalance.tsx", "components/credits/CreditHistory.tsx", "shared/constants.ts", "components/credits/CreditPurchase.tsx", "components/credits/CreditUsageChart.tsx", "pages/CreditsPage.tsx"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { CreditTransaction, AIOperationCost } from \"../shared/types\";\r\n\r\ninterface CreditStats {\r\n  totalUsed: number;\r\n  totalPurchased: number;\r\n  usageByOperation: Record<string, number>;\r\n  dailyUsage: Array<{ date: string; credits: number }>;\r\n}\r\n\r\ninterface CreditState {\r\n  balance: number;\r\n  transactions: CreditTransaction[];\r\n  operationCosts: AIOperationCost[];\r\n  stats: CreditStats | null;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n\r\n  // Actions\r\n  fetchBalance: () => Promise<void>;\r\n  fetchTransactions: (limit?: number, offset?: number) => Promise<void>;\r\n  fetchOperationCosts: () => Promise<void>;\r\n  fetchStats: (days?: number) => Promise<void>;\r\n  purchaseCredits: (packageData: {\r\n    amount: number;\r\n    credits: number;\r\n    price: number;\r\n    email: string;\r\n    name?: string;\r\n    discountCode?: string;\r\n  }) => Promise<{\r\n    success: boolean;\r\n    error?: string;\r\n    clientSecret?: string;\r\n    paymentIntentId?: string;\r\n  }>;\r\n  refreshAfterPurchase: () => Promise<void>;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useCreditStore = create<CreditState>((set, get) => ({\r\n  balance: 0,\r\n  transactions: [],\r\n  operationCosts: [],\r\n  stats: null,\r\n  isLoading: false,\r\n  error: null,\r\n\r\n  fetchBalance: async () => {\r\n    set({ isLoading: true, error: null });\r\n    try {\r\n      const response = await fetch(\"/api/credits/balance\", {\r\n        headers: {\r\n          Authorization: `Bearer ${\r\n            localStorage.getItem(\"auth_token\") ||\r\n            sessionStorage.getItem(\"auth_token\")\r\n          }`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch credit balance\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        set({ balance: data.data.credits, isLoading: false });\r\n      } else {\r\n        throw new Error(data.error || \"Failed to fetch balance\");\r\n      }\r\n    } catch (error) {\r\n      set({\r\n        error: error instanceof Error ? error.message : \"Unknown error\",\r\n        isLoading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchTransactions: async (limit = 50, offset = 0) => {\r\n    set({ isLoading: true, error: null });\r\n    try {\r\n      const response = await fetch(\r\n        `/api/credits/history?limit=${limit}&offset=${offset}`,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${\r\n              localStorage.getItem(\"auth_token\") ||\r\n              sessionStorage.getItem(\"auth_token\")\r\n            }`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch credit history\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        set({\r\n          transactions:\r\n            offset === 0 ? data.data : [...get().transactions, ...data.data],\r\n          isLoading: false,\r\n        });\r\n      } else {\r\n        throw new Error(data.error || \"Failed to fetch transactions\");\r\n      }\r\n    } catch (error) {\r\n      set({\r\n        error: error instanceof Error ? error.message : \"Unknown error\",\r\n        isLoading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchOperationCosts: async () => {\r\n    set({ isLoading: true, error: null });\r\n    try {\r\n      const response = await fetch(\"/api/credits/pricing\", {\r\n        headers: {\r\n          Authorization: `Bearer ${\r\n            localStorage.getItem(\"auth_token\") ||\r\n            sessionStorage.getItem(\"auth_token\")\r\n          }`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch operation costs\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        set({ operationCosts: data.data, isLoading: false });\r\n      } else {\r\n        throw new Error(data.error || \"Failed to fetch operation costs\");\r\n      }\r\n    } catch (error) {\r\n      set({\r\n        error: error instanceof Error ? error.message : \"Unknown error\",\r\n        isLoading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchStats: async (days = 30) => {\r\n    set({ isLoading: true, error: null });\r\n    try {\r\n      const response = await fetch(`/api/credits/stats?days=${days}`, {\r\n        headers: {\r\n          Authorization: `Bearer ${\r\n            localStorage.getItem(\"auth_token\") ||\r\n            sessionStorage.getItem(\"auth_token\")\r\n          }`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch credit stats\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        set({ stats: data.data, isLoading: false });\r\n      } else {\r\n        throw new Error(data.error || \"Failed to fetch stats\");\r\n      }\r\n    } catch (error) {\r\n      set({\r\n        error: error instanceof Error ? error.message : \"Unknown error\",\r\n        isLoading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  purchaseCredits: async (packageData: {\r\n    amount: number;\r\n    credits: number;\r\n    price: number;\r\n    email: string;\r\n    name?: string;\r\n    discountCode?: string;\r\n  }) => {\r\n    set({ isLoading: true, error: null });\r\n    try {\r\n      const response = await fetch(\"/api/credits/purchase\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${\r\n            localStorage.getItem(\"auth_token\") ||\r\n            sessionStorage.getItem(\"auth_token\")\r\n          }`,\r\n        },\r\n        body: JSON.stringify(packageData),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to initiate credit purchase\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        set({ isLoading: false });\r\n        return {\r\n          success: true,\r\n          clientSecret: data.data.clientSecret,\r\n          paymentIntentId: data.data.paymentIntentId,\r\n        };\r\n      } else {\r\n        throw new Error(data.error || \"Failed to purchase credits\");\r\n      }\r\n    } catch (error) {\r\n      const errorMessage =\r\n        error instanceof Error ? error.message : \"Unknown error\";\r\n      set({\r\n        error: errorMessage,\r\n        isLoading: false,\r\n      });\r\n      return { success: false, error: errorMessage };\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n\r\n  refreshAfterPurchase: async () => {\r\n    try {\r\n      await Promise.all([get().fetchBalance(), get().fetchTransactions()]);\r\n    } catch (error) {\r\n      console.error(\"Failed to refresh data after purchase:\", error);\r\n    }\r\n  },\r\n}));\r\n", "import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { \r\n  HiCreditCard, \r\n  HiTrendingUp, \r\n  HiClock,\r\n  HiSparkles\r\n} from 'react-icons/hi';\r\n\r\ninterface CreditBalanceProps {\r\n  balance: number;\r\n  userTier: string;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport const CreditBalance: React.FC<CreditBalanceProps> = ({\r\n  balance,\r\n  userTier,\r\n  isLoading\r\n}) => {\r\n  const getTierColor = (tier: string) => {\r\n    switch (tier.toLowerCase()) {\r\n      case 'pro':\r\n        return 'text-purple-400';\r\n      case 'basic':\r\n        return 'text-blue-400';\r\n      default:\r\n        return 'text-gray-400';\r\n    }\r\n  };\r\n\r\n  const getTierIcon = (tier: string) => {\r\n    switch (tier.toLowerCase()) {\r\n      case 'pro':\r\n        return <HiSparkles className=\"w-5 h-5\" />;\r\n      case 'basic':\r\n        return <HiTrendingUp className=\"w-5 h-5\" />;\r\n      default:\r\n        return <HiCreditCard className=\"w-5 h-5\" />;\r\n    }\r\n  };\r\n\r\n  const getBalanceStatus = (balance: number) => {\r\n    if (balance >= 100) return { color: 'text-green-400', status: 'Excellent' };\r\n    if (balance >= 50) return { color: 'text-yellow-400', status: 'Good' };\r\n    if (balance >= 10) return { color: 'text-orange-400', status: 'Low' };\r\n    return { color: 'text-red-400', status: 'Critical' };\r\n  };\r\n\r\n  const balanceStatus = getBalanceStatus(balance);\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n      {/* Main Balance Card */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n        className=\"md:col-span-2 bg-gradient-to-br from-primary-500/20 to-purple-600/20 rounded-lg p-6 border border-primary-500/30\"\r\n      >\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className=\"p-3 bg-primary-500/20 rounded-lg\">\r\n              <HiCreditCard className=\"w-6 h-6 text-primary-400\" />\r\n            </div>\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-white\">Credit Balance</h3>\r\n              <p className=\"text-gray-400 text-sm\">Available for AI generation</p>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className={`flex items-center space-x-2 ${getTierColor(userTier)}`}>\r\n            {getTierIcon(userTier)}\r\n            <span className=\"font-medium\">{userTier} Plan</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-end space-x-4\">\r\n          <div>\r\n            {isLoading ? (\r\n              <div className=\"animate-pulse\">\r\n                <div className=\"h-12 w-32 bg-gray-600 rounded\"></div>\r\n              </div>\r\n            ) : (\r\n              <motion.div\r\n                initial={{ scale: 0.8 }}\r\n                animate={{ scale: 1 }}\r\n                transition={{ duration: 0.3, delay: 0.2 }}\r\n              >\r\n                <span className=\"text-4xl font-bold text-white\">{balance.toLocaleString()}</span>\r\n                <span className=\"text-xl text-gray-400 ml-2\">credits</span>\r\n              </motion.div>\r\n            )}\r\n          </div>\r\n          \r\n          <div className={`flex items-center space-x-1 ${balanceStatus.color} mb-2`}>\r\n            <div className={`w-2 h-2 rounded-full ${balanceStatus.color.replace('text-', 'bg-')}`}></div>\r\n            <span className=\"text-sm font-medium\">{balanceStatus.status}</span>\r\n          </div>\r\n        </div>\r\n\r\n        {balance < 10 && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ delay: 0.5 }}\r\n            className=\"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg\"\r\n          >\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiClock className=\"w-4 h-4 text-red-400\" />\r\n              <span className=\"text-red-400 text-sm font-medium\">Low Balance Warning</span>\r\n            </div>\r\n            <p className=\"text-red-300 text-sm mt-1\">\r\n              Consider purchasing more credits to continue using AI features.\r\n            </p>\r\n          </motion.div>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* Quick Stats Card */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5, delay: 0.1 }}\r\n        className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\"\r\n      >\r\n        <h4 className=\"text-lg font-semibold text-white mb-4\">Quick Stats</h4>\r\n        \r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-gray-400 text-sm\">Plan Type</span>\r\n            <span className={`font-medium ${getTierColor(userTier)}`}>{userTier}</span>\r\n          </div>\r\n          \r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-gray-400 text-sm\">Status</span>\r\n            <span className={`font-medium ${balanceStatus.color}`}>{balanceStatus.status}</span>\r\n          </div>\r\n          \r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-gray-400 text-sm\">Credits Available</span>\r\n            <span className=\"text-white font-medium\">{balance}</span>\r\n          </div>\r\n\r\n          {userTier.toLowerCase() === 'free' && (\r\n            <div className=\"pt-3 border-t border-border-secondary\">\r\n              <p className=\"text-gray-400 text-xs\">\r\n                Upgrade to Basic or Pro for more credits and features\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  Hi<PERSON>lock,\r\n  HiPlus,\r\n  HiMinus,\r\n  HiChevronDown,\r\n  HiDownload\r\n} from 'react-icons/hi';\r\nimport { CreditTransaction } from '../../../../shared/types';\r\nimport { Button } from '../common/Button';\r\n\r\ninterface CreditHistoryProps {\r\n  transactions: CreditTransaction[];\r\n  isLoading: boolean;\r\n  onLoadMore: () => void;\r\n}\r\n\r\nexport const CreditHistory: React.FC<CreditHistoryProps> = ({\r\n  transactions,\r\n  isLoading,\r\n  onLoadMore\r\n}) => {\r\n  const [filter, setFilter] = useState<'all' | 'used' | 'purchased'>('all');\r\n  const [sortBy, setSortBy] = useState<'date' | 'amount'>('date');\r\n\r\n  const getTransactionIcon = (creditsUsed: number) => {\r\n    return creditsUsed > 0 ? (\r\n      <HiMinus className=\"w-4 h-4 text-red-400\" />\r\n    ) : (\r\n      <HiPlus className=\"w-4 h-4 text-green-400\" />\r\n    );\r\n  };\r\n\r\n  const getTransactionColor = (creditsUsed: number) => {\r\n    return creditsUsed > 0 ? 'text-red-400' : 'text-green-400';\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    return {\r\n      date: date.toLocaleDateString(),\r\n      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\r\n    };\r\n  };\r\n\r\n  const filteredTransactions = transactions.filter(transaction => {\r\n    if (filter === 'used') return transaction.credits_used > 0;\r\n    if (filter === 'purchased') return transaction.credits_used < 0;\r\n    return true;\r\n  });\r\n\r\n  const sortedTransactions = [...filteredTransactions].sort((a, b) => {\r\n    if (sortBy === 'date') {\r\n      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n    } else {\r\n      return Math.abs(b.credits_used) - Math.abs(a.credits_used);\r\n    }\r\n  });\r\n\r\n  const exportTransactions = () => {\r\n    const csvContent = [\r\n      ['Date', 'Type', 'Credits', 'Operation', 'Description'].join(','),\r\n      ...sortedTransactions.map(transaction => [\r\n        new Date(transaction.created_at).toLocaleDateString(),\r\n        transaction.credits_used > 0 ? 'Used' : 'Purchased',\r\n        Math.abs(transaction.credits_used),\r\n        transaction.operation_type,\r\n        `\"${transaction.description}\"`\r\n      ].join(','))\r\n    ].join('\\n');\r\n\r\n    const blob = new Blob([csvContent], { type: 'text/csv' });\r\n    const url = window.URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    a.href = url;\r\n    a.download = `credit-history-${new Date().toISOString().split('T')[0]}.csv`;\r\n    a.click();\r\n    window.URL.revokeObjectURL(url);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold text-white\">Transaction History</h3>\r\n          <p className=\"text-gray-400 text-sm\">\r\n            {filteredTransactions.length} of {transactions.length} transactions\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* Filter Dropdown */}\r\n          <div className=\"relative\">\r\n            <select\r\n              value={filter}\r\n              onChange={(e) => setFilter(e.target.value as 'all' | 'used' | 'purchased')}\r\n              className=\"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500\"\r\n            >\r\n              <option value=\"all\">All Transactions</option>\r\n              <option value=\"used\">Credits Used</option>\r\n              <option value=\"purchased\">Credits Purchased</option>\r\n            </select>\r\n            <HiChevronDown className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none\" />\r\n          </div>\r\n\r\n          {/* Sort Dropdown */}\r\n          <div className=\"relative\">\r\n            <select\r\n              value={sortBy}\r\n              onChange={(e) => setSortBy(e.target.value as 'date' | 'amount')}\r\n              className=\"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500\"\r\n            >\r\n              <option value=\"date\">Sort by Date</option>\r\n              <option value=\"amount\">Sort by Amount</option>\r\n            </select>\r\n            <HiChevronDown className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none\" />\r\n          </div>\r\n\r\n          {/* Export Button */}\r\n          <Button\r\n            onClick={exportTransactions}\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n            disabled={transactions.length === 0}\r\n          >\r\n            <HiDownload className=\"w-4 h-4 mr-2\" />\r\n            Export\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Transaction List */}\r\n      <div className=\"space-y-3\">\r\n        {isLoading && transactions.length === 0 ? (\r\n          <div className=\"space-y-3\">\r\n            {[...Array(5)].map((_, i) => (\r\n              <div key={i} className=\"animate-pulse\">\r\n                <div className=\"bg-background-tertiary rounded-lg p-4\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-8 h-8 bg-gray-600 rounded-full\"></div>\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"h-4 bg-gray-600 rounded w-1/3 mb-2\"></div>\r\n                      <div className=\"h-3 bg-gray-600 rounded w-1/2\"></div>\r\n                    </div>\r\n                    <div className=\"h-6 bg-gray-600 rounded w-16\"></div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : sortedTransactions.length === 0 ? (\r\n          <div className=\"text-center py-12\">\r\n            <HiClock className=\"w-12 h-12 text-gray-500 mx-auto mb-4\" />\r\n            <h4 className=\"text-lg font-medium text-gray-400 mb-2\">No Transactions Found</h4>\r\n            <p className=\"text-gray-500\">\r\n              {filter === 'all' \r\n                ? \"You haven't made any credit transactions yet.\"\r\n                : `No ${filter} transactions found.`\r\n              }\r\n            </p>\r\n          </div>\r\n        ) : (\r\n          sortedTransactions.map((transaction, index) => {\r\n            const { date, time } = formatDate(transaction.created_at);\r\n            const isCredit = transaction.credits_used < 0;\r\n            \r\n            return (\r\n              <motion.div\r\n                key={transaction.id}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.3, delay: index * 0.05 }}\r\n                className=\"bg-background-tertiary rounded-lg p-4 border border-border-secondary hover:border-border-primary transition-colors\"\r\n              >\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className={`p-2 rounded-full ${isCredit ? 'bg-green-500/20' : 'bg-red-500/20'}`}>\r\n                      {getTransactionIcon(transaction.credits_used)}\r\n                    </div>\r\n                    \r\n                    <div>\r\n                      <h4 className=\"font-medium text-white\">\r\n                        {transaction.operation_type.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\r\n                      </h4>\r\n                      <p className=\"text-gray-400 text-sm\">{transaction.description}</p>\r\n                      <div className=\"flex items-center space-x-2 mt-1\">\r\n                        <span className=\"text-gray-500 text-xs\">{date}</span>\r\n                        <span className=\"text-gray-600\">•</span>\r\n                        <span className=\"text-gray-500 text-xs\">{time}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"text-right\">\r\n                    <span className={`font-semibold ${getTransactionColor(transaction.credits_used)}`}>\r\n                      {isCredit ? '+' : '-'}{Math.abs(transaction.credits_used)} credits\r\n                    </span>\r\n                    {transaction.study_set_id && (\r\n                      <p className=\"text-gray-500 text-xs mt-1\">Study Set</p>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            );\r\n          })\r\n        )}\r\n      </div>\r\n\r\n      {/* Load More Button */}\r\n      {transactions.length > 0 && transactions.length % 50 === 0 && (\r\n        <div className=\"mt-6 text-center\">\r\n          <Button\r\n            onClick={onLoadMore}\r\n            variant=\"secondary\"\r\n            isLoading={isLoading}\r\n            disabled={isLoading}\r\n          >\r\n            Load More Transactions\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "// ----------------------------------------\n// Pricing & Subscription Constants\n// ----------------------------------------\n\nimport { PricingTier, SubscriptionTier } from \"./types\";\n\nexport const PRICING_TIERS: PricingTier[] = [\n  {\n    id: \"Free\",\n    name: \"Free\",\n    price: 0,\n    credits: 25,\n    features: [\n      \"25 credits per month\",\n      \"Basic AI generation\",\n      \"Document upload (up to 5MB)\",\n      \"Community support\",\n      \"Basic analytics\",\n    ],\n    stripePriceId: undefined,\n  },\n  {\n    id: \"Study Starter\",\n    name: \"Study Starter\",\n    price: 29,\n    credits: 500,\n    features: [\n      \"500 credits per month\",\n      \"Advanced AI generation\",\n      \"Document upload (up to 10MB)\",\n      \"Email support\",\n      \"Detailed analytics\",\n      \"Export functionality\",\n    ],\n    stripePriceId: \"price_starter_placeholder\",\n  },\n  {\n    id: \"Study Pro\",\n    name: \"Study Pro\",\n    price: 59,\n    credits: 1200,\n    features: [\n      \"1,200 credits per month\",\n      \"Premium AI generation\",\n      \"Document upload (up to 25MB)\",\n      \"Priority support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Collaboration features\",\n    ],\n    isPopular: true,\n    stripePriceId: \"price_pro_placeholder\",\n  },\n  {\n    id: \"Study Master\",\n    name: \"Study Master\",\n    price: 119,\n    credits: 2500,\n    features: [\n      \"2,500 credits per month\",\n      \"Premium AI generation\",\n      \"Document upload (up to 50MB)\",\n      \"Priority support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Team collaboration\",\n      \"API access\",\n    ],\n    stripePriceId: \"price_master_placeholder\",\n  },\n  {\n    id: \"Study Elite\",\n    name: \"Study Elite\",\n    price: 239,\n    credits: 5500,\n    features: [\n      \"5,500 credits per month\",\n      \"Premium AI generation\",\n      \"Unlimited document upload\",\n      \"Dedicated support\",\n      \"Advanced analytics\",\n      \"Custom study sets\",\n      \"Team collaboration\",\n      \"API access\",\n      \"White-label options\",\n    ],\n    stripePriceId: \"price_elite_placeholder\",\n  },\n];\n\n// ----------------------------------------\n// Credit System Constants\n// ----------------------------------------\n\nexport const CREDIT_COSTS = {\n  FLASHCARD_GENERATION: 1, // 1 credit per 5 flashcards\n  QUIZ_GENERATION: 1, // 1 credit per 5 quiz questions\n  FLEX_GENERATION: 1, // 1 credit per 5 flex items\n  DOCUMENT_PROCESSING: 0, // Free document processing\n} as const;\n\nexport const ITEMS_PER_CREDIT = {\n  FLASHCARDS: 5,\n  QUIZ_QUESTIONS: 5,\n  FLEX_ITEMS: 5,\n} as const;\n\n// ----------------------------------------\n// Subscription Limits\n// ----------------------------------------\n\nexport const SUBSCRIPTION_LIMITS = {\n  Free: {\n    maxDocumentSize: 5 * 1024 * 1024, // 5MB\n    maxDocuments: 10,\n    maxStudySets: 5,\n    creditsPerMonth: 25,\n  },\n  \"Study Starter\": {\n    maxDocumentSize: 10 * 1024 * 1024, // 10MB\n    maxDocuments: 50,\n    maxStudySets: 25,\n    creditsPerMonth: 500,\n  },\n  \"Study Pro\": {\n    maxDocumentSize: 25 * 1024 * 1024, // 25MB\n    maxDocuments: 200,\n    maxStudySets: 100,\n    creditsPerMonth: 1200,\n  },\n  \"Study Master\": {\n    maxDocumentSize: 50 * 1024 * 1024, // 50MB\n    maxDocuments: 500,\n    maxStudySets: 250,\n    creditsPerMonth: 2500,\n  },\n  \"Study Elite\": {\n    maxDocumentSize: 100 * 1024 * 1024, // 100MB (effectively unlimited)\n    maxDocuments: 1000,\n    maxStudySets: 500,\n    creditsPerMonth: 5500,\n  },\n} as const;\n\n// ----------------------------------------\n// Feature Flags\n// ----------------------------------------\n\nexport const FEATURE_FLAGS = {\n  Free: {\n    advancedAnalytics: false,\n    exportFunctionality: false,\n    prioritySupport: false,\n    customStudySets: false,\n    collaboration: false,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Starter\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: false,\n    customStudySets: false,\n    collaboration: false,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Pro\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: false,\n    whiteLabelOptions: false,\n  },\n  \"Study Master\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: true,\n    whiteLabelOptions: false,\n  },\n  \"Study Elite\": {\n    advancedAnalytics: true,\n    exportFunctionality: true,\n    prioritySupport: true,\n    customStudySets: true,\n    collaboration: true,\n    apiAccess: true,\n    whiteLabelOptions: true,\n  },\n} as const;\n\n// ----------------------------------------\n// Helper Functions\n// ----------------------------------------\n\nexport const getPricingTier = (\n  tier: SubscriptionTier\n): PricingTier | undefined => {\n  return PRICING_TIERS.find((t) => t.id === tier);\n};\n\nexport const getSubscriptionLimits = (tier: SubscriptionTier) => {\n  return SUBSCRIPTION_LIMITS[tier];\n};\n\nexport const getFeatureFlags = (tier: SubscriptionTier) => {\n  return FEATURE_FLAGS[tier];\n};\n\nexport const hasFeature = (\n  tier: SubscriptionTier,\n  feature: keyof typeof FEATURE_FLAGS.Free\n): boolean => {\n  return FEATURE_FLAGS[tier][feature];\n};\n\nexport const canUploadDocument = (\n  tier: SubscriptionTier,\n  fileSize: number\n): boolean => {\n  const limits = getSubscriptionLimits(tier);\n  return fileSize <= limits.maxDocumentSize;\n};\n\nexport const getRemainingDocuments = (\n  tier: SubscriptionTier,\n  currentCount: number\n): number => {\n  const limits = getSubscriptionLimits(tier);\n  return Math.max(0, limits.maxDocuments - currentCount);\n};\n\nexport const getRemainingStudySets = (\n  tier: SubscriptionTier,\n  currentCount: number\n): number => {\n  const limits = getSubscriptionLimits(tier);\n  return Math.max(0, limits.maxStudySets - currentCount);\n};\n\n// ----------------------------------------\n// Stripe Configuration\n// ----------------------------------------\n\nexport const STRIPE_CONFIG = {\n  publishableKey: \"NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY\",\n  webhookSecret: \"STRIPE_WEBHOOK_SECRET\",\n  successUrl: \"FRONTEND_URL\" + \"/subscription/success\",\n  cancelUrl: \"FRONTEND_URL\" + \"/subscription/cancel\",\n} as const;\n\n// ----------------------------------------\n// API Endpoints\n// ----------------------------------------\n\nexport const API_ENDPOINTS = {\n  // Authentication\n  LOGIN: \"/api/auth/login\",\n  REGISTER: \"/api/auth/register\",\n  LOGOUT: \"/api/auth/logout\",\n  REFRESH: \"/api/auth/refresh\",\n\n  // User Management\n  PROFILE: \"/api/user/profile\",\n  UPDATE_PROFILE: \"/api/user/update\",\n\n  // Subscription Management\n  CREATE_SUBSCRIPTION: \"/api/subscription/create\",\n  CANCEL_SUBSCRIPTION: \"/api/subscription/cancel\",\n  UPDATE_SUBSCRIPTION: \"/api/subscription/update\",\n  GET_SUBSCRIPTION: \"/api/subscription/status\",\n\n  // Payment Processing\n  CREATE_PAYMENT_INTENT: \"/api/payment/create-intent\",\n  CONFIRM_PAYMENT: \"/api/payment/confirm\",\n  WEBHOOK: \"/api/payment/webhook\",\n\n  // Credit Management\n  GET_CREDITS: \"/api/credits/balance\",\n  PURCHASE_CREDITS: \"/api/credits/purchase\",\n  CREDIT_HISTORY: \"/api/credits/history\",\n\n  // Document Management\n  UPLOAD_DOCUMENT: \"/api/documents/upload\",\n  GET_DOCUMENTS: \"/api/documents\",\n  DELETE_DOCUMENT: \"/api/documents/delete\",\n\n  // Study Set Management\n  CREATE_STUDY_SET: \"/api/study-sets/create\",\n  GET_STUDY_SETS: \"/api/study-sets\",\n  DELETE_STUDY_SET: \"/api/study-sets/delete\",\n\n  // AI Generation\n  GENERATE_FLASHCARDS: \"/api/ai/generate-flashcards\",\n  GENERATE_QUIZ: \"/api/ai/generate-quiz\",\n  GENERATE_FLEX: \"/api/ai/generate-flex\",\n} as const;\n", "import React, { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport {\n  HiCreditCard,\n  HiSparkles,\n  HiCheck,\n  HiStar,\n  HiLightningBolt,\n  HiAcademicCap,\n} from \"react-icons/hi\";\nimport { Button } from \"../common/Button\";\nimport { useCreditStore } from \"../../stores/creditStore\";\nimport useAuthStore from \"../../stores/authStore\";\nimport { PRICING_TIERS } from \"../../shared/constants\";\nimport type { PricingTier } from \"../../shared/types\";\n\ninterface CreditPurchaseProps {\n  currentBalance: number;\n  userTier: string;\n  onPurchaseComplete: () => void;\n}\n\nexport const CreditPurchase: React.FC<CreditPurchaseProps> = ({\n  currentBalance,\n  userTier,\n  onPurchaseComplete,\n}) => {\n  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [discountCode, setDiscountCode] = useState<string>(\"\");\n  const [showDiscountInput, setShowDiscountInput] = useState(false);\n  const [activeTab, setActiveTab] = useState<\"subscriptions\" | \"credits\">(\n    \"subscriptions\"\n  );\n  const { refreshAfterPurchase } = useCreditStore();\n  const { user } = useAuthStore();\n\n  const handleSubscriptionPurchase = async (tierId: string) => {\n    const selectedTier = PRICING_TIERS.find((tier) => tier.id === tierId);\n    if (!selectedTier) {\n      setError(\"Selected subscription tier not found. Please try again.\");\n      return;\n    }\n\n    if (!user?.email) {\n      setError(\"User email not available. Please log out and log back in.\");\n      return;\n    }\n\n    setIsProcessing(true);\n    setSelectedPackage(tierId);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      // TODO: Implement Stripe subscription creation\n      console.log(\"Creating subscription for tier:\", selectedTier);\n      setSuccess(\n        `Subscription to ${selectedTier.name} will be implemented in the next phase. Price: $${selectedTier.price}/month`\n      );\n\n      // Refresh credit data\n      await refreshAfterPurchase();\n      onPurchaseComplete();\n    } catch (error) {\n      console.error(\"Subscription error:\", error);\n      setError(\"Failed to create subscription. Please try again.\");\n    } finally {\n      setIsProcessing(false);\n      setSelectedPackage(null);\n    }\n  };\n\n  const getTierIcon = (tierId: string) => {\n    switch (tierId) {\n      case \"Study Starter\":\n        return <HiAcademicCap className=\"w-6 h-6\" />;\n      case \"Study Pro\":\n        return <HiStar className=\"w-6 h-6\" />;\n      case \"Study Master\":\n        return <HiLightningBolt className=\"w-6 h-6\" />;\n      case \"Study Elite\":\n        return <HiSparkles className=\"w-6 h-6\" />;\n      default:\n        return <HiCreditCard className=\"w-6 h-6\" />;\n    }\n  };\n\n  const isCurrentTier = (tierId: string) => {\n    return userTier === tierId;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Error Display */}\n      {error && (\n        <div className=\"bg-red-900/20 border border-red-500/50 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <svg\n                className=\"h-5 w-5 text-red-400\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-400\">Error</h3>\n              <div className=\"mt-1 text-sm text-red-300\">{error}</div>\n            </div>\n            <div className=\"ml-auto pl-3\">\n              <button\n                onClick={() => setError(null)}\n                className=\"inline-flex rounded-md bg-red-900/20 p-1.5 text-red-400 hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-900\"\n              >\n                <span className=\"sr-only\">Dismiss</span>\n                <svg\n                  className=\"h-4 w-4\"\n                  viewBox=\"0 0 20 20\"\n                  fill=\"currentColor\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Success Display */}\n      {success && (\n        <div className=\"bg-green-900/20 border border-green-500/50 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <svg\n                className=\"h-5 w-5 text-green-400\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-green-400\">Success</h3>\n              <div className=\"mt-1 text-sm text-green-300\">{success}</div>\n            </div>\n            <div className=\"ml-auto pl-3\">\n              <button\n                onClick={() => setSuccess(null)}\n                className=\"inline-flex rounded-md bg-green-900/20 p-1.5 text-green-400 hover:bg-green-900/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-green-900\"\n              >\n                <span className=\"sr-only\">Dismiss</span>\n                <svg\n                  className=\"h-4 w-4\"\n                  viewBox=\"0 0 20 20\"\n                  fill=\"currentColor\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Current Balance Display */}\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-white\">\n              Current Plan: {userTier}\n            </h3>\n            <p className=\"text-gray-400\">Your available credits</p>\n          </div>\n          <div className=\"text-right\">\n            <span className=\"text-2xl font-bold text-primary-400\">\n              {currentBalance}\n            </span>\n            <span className=\"text-gray-400 ml-2\">credits</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"flex space-x-1 bg-background-tertiary rounded-lg p-1\">\n        <button\n          onClick={() => setActiveTab(\"subscriptions\")}\n          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n            activeTab === \"subscriptions\"\n              ? \"bg-primary-500 text-white\"\n              : \"text-gray-400 hover:text-white\"\n          }`}\n        >\n          Monthly Plans\n        </button>\n        <button\n          onClick={() => setActiveTab(\"credits\")}\n          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n            activeTab === \"credits\"\n              ? \"bg-primary-500 text-white\"\n              : \"text-gray-400 hover:text-white\"\n          }`}\n        >\n          One-Time Credits\n        </button>\n      </div>\n\n      {/* Discount Code Section */}\n      {activeTab === \"credits\" && (\n        <div className=\"bg-background-secondary rounded-lg p-4 border border-border-primary mb-6\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h4 className=\"text-md font-medium text-white\">\n              Have a discount code?\n            </h4>\n            <button\n              onClick={() => setShowDiscountInput(!showDiscountInput)}\n              className=\"text-sm text-primary-400 hover:text-primary-300 transition-colors\"\n            >\n              {showDiscountInput ? \"Hide\" : \"Enter Code\"}\n            </button>\n          </div>\n\n          {showDiscountInput && (\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"text\"\n                value={discountCode}\n                onChange={(e) => setDiscountCode(e.target.value.toUpperCase())}\n                placeholder=\"Enter discount code\"\n                className=\"flex-1 px-3 py-2 bg-background-tertiary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                maxLength={20}\n              />\n              <button\n                onClick={() => {\n                  setDiscountCode(\"\");\n                  setShowDiscountInput(false);\n                }}\n                className=\"px-3 py-2 text-gray-400 hover:text-white transition-colors\"\n              >\n                Clear\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Subscription Plans */}\n      {activeTab === \"subscriptions\" && (\n        <div>\n          <h3 className=\"text-lg font-semibold text-white mb-2\">\n            Choose Your Study Plan\n          </h3>\n          <p className=\"text-gray-400 mb-6\">\n            Monthly subscriptions with automatic credit refills. Cancel anytime.\n          </p>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {PRICING_TIERS.filter((tier) => tier.id !== \"Free\").map(\n              (tier: PricingTier, index: number) => {\n                const isSelected = selectedPackage === tier.id;\n                const isProcessingThis = isProcessing && isSelected;\n                const isCurrent = isCurrentTier(tier.id);\n\n                return (\n                  <motion.div\n                    key={tier.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                    className={`\n                    relative bg-background-secondary rounded-lg p-6 border transition-all duration-200\n                    ${\n                      tier.isPopular\n                        ? \"border-primary-500 ring-2 ring-primary-500/20\"\n                        : \"border-border-primary hover:border-border-secondary\"\n                    }\n                    ${isSelected ? \"ring-2 ring-primary-500/50\" : \"\"}\n                    ${isCurrent ? \"ring-2 ring-green-500/50\" : \"\"}\n                  `}\n                  >\n                    {/* Popular Badge */}\n                    {tier.isPopular && (\n                      <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                        <div className=\"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium\">\n                          Most Popular\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Current Plan Badge */}\n                    {isCurrent && (\n                      <div className=\"absolute -top-3 right-4\">\n                        <div className=\"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium\">\n                          Current Plan\n                        </div>\n                      </div>\n                    )}\n\n                    <div className=\"text-center\">\n                      {/* Icon */}\n                      <div\n                        className={`inline-flex p-3 rounded-lg mb-4 ${\n                          tier.isPopular\n                            ? \"bg-primary-500/20 text-primary-400\"\n                            : \"bg-background-tertiary text-gray-400\"\n                        }`}\n                      >\n                        {getTierIcon(tier.id)}\n                      </div>\n\n                      {/* Tier Name */}\n                      <h4 className=\"text-lg font-semibold text-white mb-2\">\n                        {tier.name}\n                      </h4>\n\n                      {/* Credits */}\n                      <div className=\"mb-4\">\n                        <span className=\"text-3xl font-bold text-white\">\n                          {tier.credits}\n                        </span>\n                        <div className=\"text-gray-400 text-sm\">\n                          credits/month\n                        </div>\n                        <div className=\"text-green-400 text-xs\">\n                          ~{tier.credits * 5} flashcards/quizzes\n                        </div>\n                      </div>\n\n                      {/* Price */}\n                      <div className=\"mb-4\">\n                        <span className=\"text-2xl font-bold text-white\">\n                          ${tier.price}\n                        </span>\n                        <div className=\"text-gray-400 text-sm\">/month</div>\n                        <div className=\"text-gray-400 text-xs\">\n                          ${(tier.price / tier.credits).toFixed(3)} per credit\n                        </div>\n                      </div>\n\n                      {/* Features */}\n                      <div className=\"space-y-2 mb-6\">\n                        {tier.features.map(\n                          (feature: string, featureIndex: number) => (\n                            <div\n                              key={featureIndex}\n                              className=\"flex items-center text-sm text-gray-300\"\n                            >\n                              <HiCheck className=\"w-4 h-4 text-green-400 mr-2 flex-shrink-0\" />\n                              <span>{feature}</span>\n                            </div>\n                          )\n                        )}\n                      </div>\n\n                      {/* Subscribe Button */}\n                      <Button\n                        onClick={() => handleSubscriptionPurchase(tier.id)}\n                        variant={tier.isPopular ? \"primary\" : \"secondary\"}\n                        className=\"w-full\"\n                        isLoading={isProcessingThis}\n                        disabled={isProcessing || !user?.email || isCurrent}\n                      >\n                        {isProcessingThis\n                          ? \"Processing...\"\n                          : isCurrent\n                          ? \"Current Plan\"\n                          : !user?.email\n                          ? \"Login Required\"\n                          : `Subscribe to ${tier.name}`}\n                      </Button>\n                    </div>\n                  </motion.div>\n                );\n              }\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* One-Time Credit Packages */}\n      {activeTab === \"credits\" && (\n        <div>\n          <h3 className=\"text-lg font-semibold text-white mb-2\">\n            One-Time Credit Packages\n          </h3>\n          <p className=\"text-gray-400 mb-6\">\n            Purchase credits that never expire. Perfect for occasional use.\n          </p>\n\n          <div className=\"text-center py-12\">\n            <HiSparkles className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-xl font-medium text-gray-900 mb-2\">\n              One-Time Packages Coming Soon\n            </h3>\n            <p className=\"text-gray-600\">\n              We're working on one-time credit packages. For now, please choose\n              a subscription plan above.\n            </p>\n          </div>\n        </div>\n      )}\n\n      {/* Additional Information */}\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\n        <h4 className=\"text-lg font-semibold text-white mb-4\">\n          {activeTab === \"subscriptions\"\n            ? \"Subscription Benefits\"\n            : \"One-Time Purchase Benefits\"}\n        </h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h5 className=\"font-medium text-white mb-2\">Safe & Secure</h5>\n            <p className=\"text-gray-400 text-sm\">\n              Student-safe payments through Stripe. Your payment info is never\n              stored. Perfect for using your student card or parent's card with\n              permission.\n            </p>\n          </div>\n          <div>\n            <h5 className=\"font-medium text-white mb-2\">\n              {activeTab === \"subscriptions\"\n                ? \"Cancel Anytime\"\n                : \"Never Expire\"}\n            </h5>\n            <p className=\"text-gray-400 text-sm\">\n              {activeTab === \"subscriptions\"\n                ? \"No long-term commitments. Cancel your subscription anytime and keep using credits until they run out.\"\n                : \"One-time credit purchases never expire - perfect for semester planning. Use them at your own pace!\"}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { HiTrendingUp, HiTrendingDown } from 'react-icons/hi';\r\nimport { AIOperationCost } from '../../../../shared/types';\r\n\r\ninterface CreditStats {\r\n  totalUsed: number;\r\n  totalPurchased: number;\r\n  usageByOperation: Record<string, number>;\r\n  dailyUsage: Array<{ date: string; credits: number }>;\r\n}\r\n\r\ninterface CreditUsageChartProps {\r\n  stats: CreditStats;\r\n  operationCosts: AIOperationCost[];\r\n}\r\n\r\nexport const CreditUsageChart: React.FC<CreditUsageChartProps> = ({\r\n  stats,\r\n  operationCosts\r\n}) => {\r\n  const chartData = useMemo(() => {\r\n    // Get last 7 days of usage data\r\n    const last7Days = Array.from({ length: 7 }, (_, i) => {\r\n      const date = new Date();\r\n      date.setDate(date.getDate() - (6 - i));\r\n      return {\r\n        date: date.toLocaleDateString('en-US', { weekday: 'short' }),\r\n        fullDate: date.toISOString().split('T')[0],\r\n        credits: 0\r\n      };\r\n    });\r\n\r\n    // Map actual usage data to the 7-day structure (with defensive programming)\r\n    if (stats?.dailyUsage && Array.isArray(stats.dailyUsage)) {\r\n      stats.dailyUsage.forEach(usage => {\r\n        const dayIndex = last7Days.findIndex(day => day.fullDate === usage.date);\r\n        if (dayIndex !== -1) {\r\n          last7Days[dayIndex].credits = usage.credits;\r\n        }\r\n      });\r\n    }\r\n\r\n    return last7Days;\r\n  }, [stats?.dailyUsage]);\r\n\r\n  const maxUsage = Math.max(...chartData.map(d => d.credits), 1);\r\n  const totalWeeklyUsage = chartData.reduce((sum, day) => sum + day.credits, 0);\r\n  const averageDailyUsage = totalWeeklyUsage / 7;\r\n\r\n  // Calculate trend (comparing first half vs second half of the week)\r\n  const firstHalf = chartData.slice(0, 3).reduce((sum, day) => sum + day.credits, 0) / 3;\r\n  const secondHalf = chartData.slice(4).reduce((sum, day) => sum + day.credits, 0) / 3;\r\n  const trend = secondHalf > firstHalf ? 'up' : secondHalf < firstHalf ? 'down' : 'stable';\r\n  const trendPercentage = firstHalf > 0 ? Math.abs(((secondHalf - firstHalf) / firstHalf) * 100) : 0;\r\n\r\n  return (\r\n    <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold text-white\">Usage Trends</h3>\r\n          <p className=\"text-gray-400 text-sm\">Last 7 days</p>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {trend === 'up' ? (\r\n            <HiTrendingUp className=\"w-5 h-5 text-green-400\" />\r\n          ) : trend === 'down' ? (\r\n            <HiTrendingDown className=\"w-5 h-5 text-red-400\" />\r\n          ) : null}\r\n          \r\n          {trend !== 'stable' && (\r\n            <span className={`text-sm font-medium ${\r\n              trend === 'up' ? 'text-green-400' : 'text-red-400'\r\n            }`}>\r\n              {trendPercentage.toFixed(1)}%\r\n            </span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Chart */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex items-end justify-between h-32 space-x-2\">\r\n          {chartData.map((day, index) => {\r\n            const height = maxUsage > 0 ? (day.credits / maxUsage) * 100 : 0;\r\n            \r\n            return (\r\n              <div key={day.date} className=\"flex-1 flex flex-col items-center\">\r\n                <div className=\"w-full flex justify-center mb-2\">\r\n                  <motion.div\r\n                    initial={{ height: 0 }}\r\n                    animate={{ height: `${height}%` }}\r\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                    className=\"w-full max-w-8 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-sm relative group\"\r\n                    style={{ minHeight: height > 0 ? '4px' : '0px' }}\r\n                  >\r\n                    {/* Tooltip */}\r\n                    <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                      <div className=\"bg-background-tertiary border border-border-primary rounded px-2 py-1 text-xs text-white whitespace-nowrap\">\r\n                        {day.credits} credits\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                </div>\r\n                \r\n                <span className=\"text-xs text-gray-400\">{day.date}</span>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Summary */}\r\n      <div className=\"grid grid-cols-2 gap-4\">\r\n        <div className=\"bg-background-tertiary rounded-lg p-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-white\">{totalWeeklyUsage}</div>\r\n            <div className=\"text-gray-400 text-sm\">Total This Week</div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"bg-background-tertiary rounded-lg p-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-white\">{averageDailyUsage.toFixed(1)}</div>\r\n            <div className=\"text-gray-400 text-sm\">Daily Average</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Usage Efficiency */}\r\n      <div className=\"mt-4 pt-4 border-t border-border-secondary\">\r\n        <h4 className=\"text-sm font-medium text-white mb-3\">Usage Efficiency</h4>\r\n        <div className=\"space-y-2\">\r\n          {stats?.usageByOperation ? Object.entries(stats.usageByOperation).slice(0, 3).map(([operation, credits]) => {\r\n            const operationCost = operationCosts?.find(cost => cost.operation_type === operation);\r\n            const generations = operationCost ? credits * operationCost.operations_per_credit : 0;\r\n\r\n            return (\r\n              <div key={operation} className=\"flex justify-between items-center text-sm\">\r\n                <span className=\"text-gray-300 capitalize\">\r\n                  {operation.replace(/_/g, ' ')}\r\n                </span>\r\n                <span className=\"text-white\">\r\n                  {generations} generations\r\n                </span>\r\n              </div>\r\n            );\r\n          }) : (\r\n            <div className=\"text-gray-400 text-sm text-center py-2\">\r\n              No usage data available\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  HiCreditCard,\r\n  Hi<PERSON><PERSON><PERSON>cyDollar,\r\n  Hi<PERSON>hartBar,\r\n  <PERSON><PERSON><PERSON>,\r\n  HiRefresh,\r\n  HiExclamationCircle,\r\n} from \"react-icons/hi\";\r\nimport { useCreditStore } from \"../stores/creditStore\";\r\nimport { useAuthStore } from \"../stores/authStore\";\r\nimport { Button } from \"../components/common/Button\";\r\nimport { CreditBalance } from \"../components/credits/CreditBalance\";\r\nimport { CreditHistory } from \"../components/credits/CreditHistory\";\r\nimport { CreditPurchase } from \"../components/credits/CreditPurchase\";\r\nimport { CreditUsageChart } from \"../components/credits/CreditUsageChart\";\r\n\r\ninterface TabSection {\r\n  id: string;\r\n  label: string;\r\n  icon: React.ComponentType<{ className?: string }>;\r\n  description: string;\r\n}\r\n\r\nconst creditTabs: TabSection[] = [\r\n  {\r\n    id: \"overview\",\r\n    label: \"Overview\",\r\n    icon: HiChartBar,\r\n    description: \"Credit balance and usage summary\",\r\n  },\r\n  {\r\n    id: \"history\",\r\n    label: \"Transaction History\",\r\n    icon: Hi<PERSON><PERSON>,\r\n    description: \"Detailed credit transaction log\",\r\n  },\r\n  {\r\n    id: \"purchase\",\r\n    label: \"Buy Credits\",\r\n    icon: HiCurrencyDollar,\r\n    description: \"Purchase additional credits\",\r\n  },\r\n];\r\n\r\nconst getOperationDisplayName = (operationType: string): string => {\r\n  switch (operationType) {\r\n    case \"quiz_generation\":\r\n      return \"quiz questions\";\r\n    case \"flashcard_generation\":\r\n      return \"flashcards\";\r\n    case \"additional_content\":\r\n      return \"flex generations\";\r\n    default:\r\n      return (\r\n        operationType.replace(/_/g, \" \").replace(\"generation\", \"\").trim() + \"s\"\r\n      );\r\n  }\r\n};\r\n\r\nexport const CreditsPage: React.FC = () => {\r\n  const [activeTab, setActiveTab] = useState(\"overview\");\r\n  const { user } = useAuthStore();\r\n  const {\r\n    balance,\r\n    transactions,\r\n    operationCosts,\r\n    stats,\r\n    isLoading,\r\n    error,\r\n    fetchBalance,\r\n    fetchTransactions,\r\n    fetchOperationCosts,\r\n    fetchStats,\r\n    clearError,\r\n  } = useCreditStore();\r\n\r\n  useEffect(() => {\r\n    // Fetch initial data when component mounts\r\n    fetchBalance();\r\n    fetchTransactions();\r\n    fetchOperationCosts();\r\n    fetchStats();\r\n  }, [fetchBalance, fetchTransactions, fetchOperationCosts, fetchStats]);\r\n\r\n  const handleRefresh = async () => {\r\n    clearError();\r\n    await Promise.all([\r\n      fetchBalance(),\r\n      fetchTransactions(),\r\n      fetchOperationCosts(),\r\n      fetchStats(),\r\n    ]);\r\n  };\r\n\r\n  const renderOverviewTab = () => (\r\n    <div className=\"space-y-6\">\r\n      {/* Credit Balance Section */}\r\n      <CreditBalance\r\n        balance={balance}\r\n        userTier={user?.subscription_tier || \"Free\"}\r\n        isLoading={isLoading}\r\n      />\r\n\r\n      {/* Usage Statistics */}\r\n      {stats && (\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n          <CreditUsageChart stats={stats} operationCosts={operationCosts} />\r\n\r\n          <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">\r\n              Usage Breakdown\r\n            </h3>\r\n            <div className=\"space-y-3\">\r\n              {stats?.usageByOperation ? (\r\n                Object.entries(stats.usageByOperation).map(\r\n                  ([operation, credits]) => (\r\n                    <div\r\n                      key={operation}\r\n                      className=\"flex justify-between items-center\"\r\n                    >\r\n                      <span className=\"text-gray-300 capitalize\">\r\n                        {operation.replace(/_/g, \" \")}\r\n                      </span>\r\n                      <span className=\"text-white font-medium\">\r\n                        {credits} credits\r\n                      </span>\r\n                    </div>\r\n                  )\r\n                )\r\n              ) : (\r\n                <div className=\"text-gray-400 text-center py-4\">\r\n                  No usage data available\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Operation Costs */}\r\n      <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Credit Costs</h3>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          {operationCosts?.length > 0 ? (\r\n            operationCosts.map((cost) => (\r\n              <div\r\n                key={cost.operation_type}\r\n                className=\"bg-background-tertiary rounded-lg p-4 border border-border-secondary\"\r\n              >\r\n                <h4 className=\"font-medium text-white capitalize mb-2\">\r\n                  {cost.operation_type.replace(/_/g, \" \")}\r\n                </h4>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <HiCreditCard className=\"w-4 h-4 text-primary-400\" />\r\n                  <span className=\"text-primary-400 font-semibold\">\r\n                    1 credit = {cost.operations_per_credit}{\" \"}\r\n                    {getOperationDisplayName(cost.operation_type)}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div className=\"col-span-full text-gray-400 text-center py-8\">\r\n              No operation cost data available\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderHistoryTab = () => (\r\n    <CreditHistory\r\n      transactions={transactions}\r\n      isLoading={isLoading}\r\n      onLoadMore={() => fetchTransactions(50, transactions.length)}\r\n    />\r\n  );\r\n\r\n  const renderPurchaseTab = () => (\r\n    <CreditPurchase\r\n      currentBalance={balance}\r\n      userTier={user?.subscription_tier || \"Study Starter\"}\r\n      onPurchaseComplete={handleRefresh}\r\n    />\r\n  );\r\n\r\n  const renderContent = () => {\r\n    switch (activeTab) {\r\n      case \"overview\":\r\n        return renderOverviewTab();\r\n      case \"history\":\r\n        return renderHistoryTab();\r\n      case \"purchase\":\r\n        return renderPurchaseTab();\r\n      default:\r\n        return renderOverviewTab();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background-primary text-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center mb-8\">\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-white mb-2\">Credits</h1>\r\n            <p className=\"text-gray-400\">Manage your AI generation credits</p>\r\n          </div>\r\n\r\n          <div className=\"flex items-center space-x-4\">\r\n            <Button\r\n              onClick={handleRefresh}\r\n              variant=\"secondary\"\r\n              disabled={isLoading}\r\n            >\r\n              <HiRefresh\r\n                className={`w-4 h-4 mr-2 ${isLoading ? \"animate-spin\" : \"\"}`}\r\n              />\r\n              Refresh\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Error Display */}\r\n        {error && (\r\n          <div className=\"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <HiExclamationCircle className=\"w-5 h-5 text-red-400\" />\r\n              <span className=\"text-red-400 font-medium\">Error</span>\r\n            </div>\r\n            <p className=\"text-red-300 mt-1\">{error}</p>\r\n            <Button\r\n              onClick={clearError}\r\n              variant=\"secondary\"\r\n              size=\"sm\"\r\n              className=\"mt-2\"\r\n            >\r\n              Dismiss\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\r\n          {/* Tab Navigation */}\r\n          <div className=\"lg:col-span-1\">\r\n            <div className=\"bg-background-secondary rounded-lg p-6 border border-border-primary\">\r\n              <nav className=\"space-y-2\">\r\n                {creditTabs.map((tab) => {\r\n                  const Icon = tab.icon;\r\n                  const isActive = activeTab === tab.id;\r\n\r\n                  return (\r\n                    <button\r\n                      key={tab.id}\r\n                      onClick={() => setActiveTab(tab.id)}\r\n                      className={`\r\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\r\n                        transition-all duration-200\r\n                        ${\r\n                          isActive\r\n                            ? \"bg-primary-500/20 text-primary-400 border border-primary-500/30\"\r\n                            : \"text-gray-300 hover:bg-background-tertiary hover:text-white\"\r\n                        }\r\n                      `}\r\n                    >\r\n                      <Icon className=\"w-5 h-5\" />\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <span className=\"font-medium block\">{tab.label}</span>\r\n                        <span className=\"text-xs text-gray-500 block truncate\">\r\n                          {tab.description}\r\n                        </span>\r\n                      </div>\r\n                    </button>\r\n                  );\r\n                })}\r\n              </nav>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Tab Content */}\r\n          <div className=\"lg:col-span-3\">\r\n            <motion.div\r\n              key={activeTab}\r\n              initial={{ opacity: 0, x: 20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              {renderContent()}\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": ["useCreditStore", "create", "set", "get", "balance", "transactions", "operationCosts", "stats", "isLoading", "error", "fetchBalance", "async", "response", "fetch", "headers", "Authorization", "concat", "localStorage", "getItem", "sessionStorage", "ok", "Error", "data", "json", "success", "credits", "message", "fetchTransactions", "limit", "arguments", "length", "undefined", "offset", "fetchOperationCosts", "fetchStats", "days", "purchaseCredits", "method", "body", "JSON", "stringify", "packageData", "clientSecret", "paymentIntentId", "errorMessage", "clearError", "refreshAfterPurchase", "Promise", "all", "console", "CreditBalance", "_ref", "userTier", "getTierColor", "tier", "toLowerCase", "balanceStatus", "color", "status", "getBalanceStatus", "_jsxs", "className", "children", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "_jsx", "HiCreditCard", "HiSparkles", "HiTrendingUp", "getTierIcon", "scale", "delay", "toLocaleString", "replace", "<PERSON><PERSON><PERSON>", "CreditHistory", "onLoadMore", "filter", "setFilter", "useState", "sortBy", "setSortBy", "getTransactionColor", "creditsUsed", "filteredTransactions", "transaction", "credits_used", "sortedTransactions", "sort", "a", "b", "Date", "created_at", "getTime", "Math", "abs", "value", "onChange", "e", "target", "HiChevronDown", "<PERSON><PERSON>", "onClick", "exportTransactions", "csv<PERSON><PERSON>nt", "join", "map", "toLocaleDateString", "operation_type", "description", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "document", "createElement", "href", "download", "toISOString", "split", "click", "revokeObjectURL", "variant", "size", "disabled", "HiDownload", "Array", "_", "i", "index", "date", "time", "dateString", "toLocaleTimeString", "hour", "minute", "formatDate", "isCredit", "HiMinus", "HiPlus", "l", "toUpperCase", "study_set_id", "id", "PRICING_TIERS", "name", "price", "features", "stripePriceId", "isPopular", "CreditPurchase", "currentBalance", "onPurchaseComplete", "selected<PERSON><PERSON><PERSON>", "setSelectedPackage", "isProcessing", "setIsProcessing", "setError", "setSuccess", "discountCode", "setDiscountCode", "showDiscountInput", "setShowDiscountInput", "activeTab", "setActiveTab", "user", "useAuthStore", "tierId", "HiAcademicCap", "HiStar", "HiLightningBolt", "viewBox", "fill", "fillRule", "d", "clipRule", "placeholder", "max<PERSON><PERSON><PERSON>", "isSelected", "isProcessingThis", "isCurrent", "toFixed", "feature", "featureIndex", "<PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON>", "find", "email", "log", "handleSubscriptionPurchase", "CreditUsageChart", "chartData", "useMemo", "last7Days", "from", "setDate", "getDate", "weekday", "fullDate", "dailyUsage", "isArray", "for<PERSON>ach", "usage", "dayIndex", "findIndex", "day", "maxUsage", "max", "totalWeeklyUsage", "reduce", "sum", "averageDailyUsage", "firstHalf", "slice", "secondHalf", "trend", "trendPercentage", "HiTrendingDown", "height", "style", "minHeight", "usageByOperation", "Object", "entries", "_ref2", "operation", "operationCost", "cost", "generations", "operations_per_credit", "creditTabs", "label", "icon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getOperationDisplayName", "operationType", "trim", "CreditsPage", "useEffect", "handleRefresh", "renderOverviewTab", "subscription_tier", "HiRefresh", "HiExclamationCircle", "tab", "Icon", "isActive", "x", "renderContent"], "sourceRoot": ""}