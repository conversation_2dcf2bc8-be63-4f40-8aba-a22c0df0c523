# Phase 2: Database Schema & Security Setup

**Priority**: CRITICAL - Required before any data operations
**Dependencies**: Phase 1 (Foundation Setup)
**Estimated Time**: 2-3 hours

## Overview

Set up Supabase database with complete schema, Row Level Security policies, and stored procedures for secure operations.

## Tasks

### 2.1 Supabase Project Setup

**Prerequisites:**

- Create Supabase project at https://supabase.com
- Get project URL and service role key
- Configure environment variables

**Environment Variables** (backend/.env):

```
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key
SUPABASE_ANON_KEY=your_anon_key
OPENROUTER_API_KEY=your_openrouter_key
STRIPE_SECRET_KEY=your_stripe_secret
STRIPE_WEBHOOK_SECRET=your_webhook_secret
JWT_SECRET=your_jwt_secret
NODE_ENV=development
PORT=3001
```

### 2.2 Database Schema Creation

**Execute in Supabase SQL Editor** (in exact order):

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255),
    subscription_tier VARCHAR(30) DEFAULT 'Study Starter' CHECK (subscription_tier IN ('Study Starter', 'Scholar Pro', 'Academic Year Pass')),
    credits_remaining INTEGER DEFAULT 500 CHECK (credits_remaining >= 0),
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    stripe_customer_id VARCHAR(255),
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(10) NOT NULL CHECK (file_type IN ('pdf', 'docx', 'txt', 'pptx')),
    file_size INTEGER NOT NULL CHECK (file_size > 0 AND file_size <= 10485760), -- 10MB limit
    content_text TEXT,
    supabase_storage_path VARCHAR(500) NOT NULL,
    page_count INTEGER CHECK (page_count > 0), -- Total pages for PDF/slides for PPTX
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_error TEXT,
    CONSTRAINT valid_filename CHECK (LENGTH(filename) > 0)
);

-- Study sets table
CREATE TABLE study_sets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('flashcards', 'quiz')),
    is_ai_generated BOOLEAN DEFAULT FALSE,
    source_documents JSONB, -- Array of {id, filename}
    custom_prompt TEXT,
    total_items INTEGER DEFAULT 0 CHECK (total_items >= 0),
    last_studied_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_name CHECK (LENGTH(name) > 0)
);

-- Flashcards table
CREATE TABLE flashcards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    study_set_id UUID NOT NULL REFERENCES study_sets(id) ON DELETE CASCADE,
    front TEXT NOT NULL,
    back TEXT NOT NULL,
    is_flagged BOOLEAN DEFAULT FALSE,
    is_ai_generated BOOLEAN DEFAULT FALSE,
    difficulty_level INTEGER CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    times_reviewed INTEGER DEFAULT 0 CHECK (times_reviewed >= 0),
    last_reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_content CHECK (LENGTH(front) > 0 AND LENGTH(back) > 0)
);

-- Quiz questions table
CREATE TABLE quiz_questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    study_set_id UUID NOT NULL REFERENCES study_sets(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_type VARCHAR(20) NOT NULL CHECK (question_type IN ('multiple_choice', 'select_all', 'true_false', 'short_answer')),
    options JSONB, -- Array of strings for multiple choice/select all
    correct_answers JSONB NOT NULL, -- Array of correct answers
    explanation TEXT,
    is_ai_generated BOOLEAN DEFAULT FALSE,
    difficulty_level INTEGER CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    times_attempted INTEGER DEFAULT 0 CHECK (times_attempted >= 0),
    times_correct INTEGER DEFAULT 0 CHECK (times_correct >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_question CHECK (LENGTH(question_text) > 0),
    CONSTRAINT valid_attempts CHECK (times_correct <= times_attempted)
);

-- Credit transactions table (append-only for audit)
CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    credits_used INTEGER NOT NULL, -- Negative for additions, positive for deductions
    operation_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    study_set_id UUID REFERENCES study_sets(id),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI operation costs configuration
CREATE TABLE ai_operation_costs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    operation_type VARCHAR(50) NOT NULL UNIQUE,
    credits_required INTEGER NOT NULL CHECK (credits_required > 0),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.3 Initial Data Setup

```sql
-- Insert default AI operation costs
INSERT INTO ai_operation_costs (operation_type, credits_required, description) VALUES
('flashcard_generation', 1, 'Generate flashcards from documents'),
('quiz_generation', 1, 'Generate quiz questions from documents'),
('additional_content', 1, 'Add more content to existing study set');
```

### 2.4 Row Level Security Policies

**Critical for data isolation between users:**

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_sets ENABLE ROW LEVEL SECURITY;
ALTER TABLE flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE quiz_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Documents policies
CREATE POLICY "Users can view own documents" ON documents FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own documents" ON documents FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own documents" ON documents FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own documents" ON documents FOR DELETE USING (auth.uid() = user_id);

-- Study sets policies
CREATE POLICY "Users can view own study sets" ON study_sets FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own study sets" ON study_sets FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own study sets" ON study_sets FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own study sets" ON study_sets FOR DELETE USING (auth.uid() = user_id);

-- Flashcards inherit study set ownership
CREATE POLICY "Users can view own flashcards" ON flashcards FOR SELECT
USING (EXISTS (SELECT 1 FROM study_sets WHERE study_sets.id = flashcards.study_set_id AND study_sets.user_id = auth.uid()));

CREATE POLICY "Users can insert own flashcards" ON flashcards FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM study_sets WHERE study_sets.id = flashcards.study_set_id AND study_sets.user_id = auth.uid()));

CREATE POLICY "Users can update own flashcards" ON flashcards FOR UPDATE
USING (EXISTS (SELECT 1 FROM study_sets WHERE study_sets.id = flashcards.study_set_id AND study_sets.user_id = auth.uid()));

CREATE POLICY "Users can delete own flashcards" ON flashcards FOR DELETE
USING (EXISTS (SELECT 1 FROM study_sets WHERE study_sets.id = flashcards.study_set_id AND study_sets.user_id = auth.uid()));

-- Quiz questions inherit study set ownership (same pattern as flashcards)
CREATE POLICY "Users can view own quiz questions" ON quiz_questions FOR SELECT
USING (EXISTS (SELECT 1 FROM study_sets WHERE study_sets.id = quiz_questions.study_set_id AND study_sets.user_id = auth.uid()));

CREATE POLICY "Users can insert own quiz questions" ON quiz_questions FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM study_sets WHERE study_sets.id = quiz_questions.study_set_id AND study_sets.user_id = auth.uid()));

CREATE POLICY "Users can update own quiz questions" ON quiz_questions FOR UPDATE
USING (EXISTS (SELECT 1 FROM study_sets WHERE study_sets.id = quiz_questions.study_set_id AND study_sets.user_id = auth.uid()));

CREATE POLICY "Users can delete own quiz questions" ON quiz_questions FOR DELETE
USING (EXISTS (SELECT 1 FROM study_sets WHERE study_sets.id = quiz_questions.study_set_id AND study_sets.user_id = auth.uid()));

-- Credit transactions (read-only for users, append-only)
CREATE POLICY "Users can view own credit transactions" ON credit_transactions FOR SELECT USING (auth.uid() = user_id);

-- AI operation costs (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view operation costs" ON ai_operation_costs FOR SELECT TO authenticated USING (is_active = true);
```

### 2.5 Critical Stored Procedures

**For atomic credit operations:**

```sql
-- Atomic credit deduction function
CREATE OR REPLACE FUNCTION deduct_credits(
    p_user_id UUID,
    p_credits_to_deduct INTEGER,
    p_operation_type VARCHAR,
    p_description TEXT,
    p_metadata JSONB DEFAULT '{}',
    p_study_set_id UUID DEFAULT NULL
) RETURNS TABLE(success BOOLEAN, remaining_credits INTEGER, message TEXT) AS $$
DECLARE
    current_credits INTEGER;
BEGIN
    -- Get current credits with row lock
    SELECT credits_remaining INTO current_credits
    FROM users
    WHERE id = p_user_id
    FOR UPDATE;

    -- Check if user exists
    IF current_credits IS NULL THEN
        RETURN QUERY SELECT FALSE, 0, 'User not found';
        RETURN;
    END IF;

    -- Check if sufficient credits
    IF current_credits < p_credits_to_deduct THEN
        RETURN QUERY SELECT FALSE, current_credits, 'Insufficient credits';
        RETURN;
    END IF;

    -- Deduct credits
    UPDATE users
    SET credits_remaining = credits_remaining - p_credits_to_deduct,
        updated_at = NOW()
    WHERE id = p_user_id;

    -- Log transaction
    INSERT INTO credit_transactions (
        user_id, credits_used, operation_type, description, metadata, study_set_id
    ) VALUES (
        p_user_id, p_credits_to_deduct, p_operation_type, p_description, p_metadata, p_study_set_id
    );

    -- Return success
    RETURN QUERY SELECT TRUE, (current_credits - p_credits_to_deduct), 'Credits deducted successfully';
END;
$$ LANGUAGE plpgsql;

-- Add credits function (for purchases/subscriptions)
CREATE OR REPLACE FUNCTION add_credits(
    p_user_id UUID,
    p_credits_to_add INTEGER,
    p_source VARCHAR,
    p_reference_id VARCHAR DEFAULT NULL
) RETURNS TABLE(success BOOLEAN, new_balance INTEGER, message TEXT) AS $$
DECLARE
    new_credits INTEGER;
BEGIN
    -- Add credits
    UPDATE users
    SET credits_remaining = credits_remaining + p_credits_to_add,
        updated_at = NOW()
    WHERE id = p_user_id
    RETURNING credits_remaining INTO new_credits;

    -- Check if user exists
    IF new_credits IS NULL THEN
        RETURN QUERY SELECT FALSE, 0, 'User not found';
        RETURN;
    END IF;

    -- Log transaction (negative value indicates addition)
    INSERT INTO credit_transactions (
        user_id, credits_used, operation_type, description, metadata
    ) VALUES (
        p_user_id, -p_credits_to_add, 'credit_purchase',
        'Credits added: ' || p_source,
        jsonb_build_object('source', p_source, 'reference_id', p_reference_id)
    );

    -- Return success
    RETURN QUERY SELECT TRUE, new_credits, 'Credits added successfully';
END;
$$ LANGUAGE plpgsql;
```

### 2.6 Database Indexes for Performance

```sql
-- Performance indexes
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_uploaded_at ON documents(uploaded_at DESC);
CREATE INDEX idx_documents_page_count ON documents(page_count) WHERE page_count IS NOT NULL;
CREATE INDEX idx_study_sets_user_id ON study_sets(user_id);
CREATE INDEX idx_study_sets_created_at ON study_sets(created_at DESC);
CREATE INDEX idx_flashcards_study_set_id ON flashcards(study_set_id);
CREATE INDEX idx_quiz_questions_study_set_id ON quiz_questions(study_set_id);
CREATE INDEX idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX idx_credit_transactions_created_at ON credit_transactions(created_at DESC);
CREATE INDEX idx_ai_operation_costs_type ON ai_operation_costs(operation_type) WHERE is_active = true;
```

### 2.7 Database Triggers

```sql
-- Update study set total_items when flashcards/questions change
CREATE OR REPLACE FUNCTION update_study_set_total_items()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_TABLE_NAME = 'flashcards' THEN
        UPDATE study_sets
        SET total_items = (
            SELECT COUNT(*) FROM flashcards WHERE study_set_id = COALESCE(NEW.study_set_id, OLD.study_set_id)
        ),
        updated_at = NOW()
        WHERE id = COALESCE(NEW.study_set_id, OLD.study_set_id);
    ELSIF TG_TABLE_NAME = 'quiz_questions' THEN
        UPDATE study_sets
        SET total_items = (
            SELECT COUNT(*) FROM quiz_questions WHERE study_set_id = COALESCE(NEW.study_set_id, OLD.study_set_id)
        ),
        updated_at = NOW()
        WHERE id = COALESCE(NEW.study_set_id, OLD.study_set_id);
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER flashcards_update_total AFTER INSERT OR UPDATE OR DELETE ON flashcards
    FOR EACH ROW EXECUTE FUNCTION update_study_set_total_items();

CREATE TRIGGER quiz_questions_update_total AFTER INSERT OR UPDATE OR DELETE ON quiz_questions
    FOR EACH ROW EXECUTE FUNCTION update_study_set_total_items();

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_study_sets_updated_at BEFORE UPDATE ON study_sets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Acceptance Criteria

- [ ] Supabase project created and configured
- [ ] All tables created with correct schema and constraints
- [ ] Row Level Security policies implemented and tested
- [ ] Stored procedures for credit operations created and tested
- [ ] Database indexes created for performance
- [ ] Triggers for automatic updates working
- [ ] Initial AI operation costs data inserted
- [ ] Environment variables configured in backend
- [ ] Database connection tested from backend
- [ ] All foreign key relationships working correctly
- [ ] Check constraints preventing invalid data

## Next Phase Dependencies

- Phase 3 (Authentication) requires users table and RLS policies
- Phase 4 (Document Management) requires documents table and storage setup
- Phase 5 (AI Integration) requires ai_operation_costs and credit_transactions tables
