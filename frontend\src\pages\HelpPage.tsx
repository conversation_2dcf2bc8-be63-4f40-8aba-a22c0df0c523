import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  HiQuestionMarkCircle, 
  HiSparkles, 
  HiDocumentText, 
  HiAcademicCap,
  HiChevronDown,
  HiChevronRight,
  HiMail,
  HiExternalLink
} from 'react-icons/hi';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'getting-started' | 'flashcards' | 'quizzes' | 'documents' | 'billing';
}

const faqData: FAQItem[] = [
  {
    id: '1',
    category: 'getting-started',
    question: 'How do I get started with ChewyAI?',
    answer: 'Start by uploading your documents in the Documents section, then use our AI-powered tools to generate flashcards or quizzes from your content. You can also create study materials manually.'
  },
  {
    id: '2',
    category: 'documents',
    question: 'What file formats are supported?',
    answer: 'ChewyAI supports PDF, DOCX, TXT, and PPTX files. Make sure your documents contain readable text for the best AI generation results.'
  },
  {
    id: '3',
    category: 'flashcards',
    question: 'How does AI flashcard generation work?',
    answer: 'Our AI analyzes your uploaded documents and creates relevant flashcards with questions and answers. You can specify the number of cards to generate and provide custom prompts for better results.'
  },
  {
    id: '4',
    category: 'quizzes',
    question: 'Can I create different types of quiz questions?',
    answer: 'Yes! ChewyAI supports multiple choice, true/false, and short answer questions. You can specify the question types when generating quizzes with AI.'
  },
  {
    id: '5',
    category: 'billing',
    question: 'How does the credit system work?',
    answer: 'Credits are used for AI-powered features like generating flashcards and quizzes. Manual creation is always free. You can purchase credit packages or subscribe for unlimited usage.'
  },
  {
    id: '6',
    category: 'getting-started',
    question: 'How do I study with my created materials?',
    answer: 'Navigate to your study sets from the dashboard and choose your preferred study mode. Use keyboard shortcuts for efficient navigation during study sessions.'
  }
];

const categories = [
  { id: 'getting-started', label: 'Getting Started', icon: HiAcademicCap },
  { id: 'documents', label: 'Documents', icon: HiDocumentText },
  { id: 'flashcards', label: 'Flashcards', icon: HiSparkles },
  { id: 'quizzes', label: 'Quizzes', icon: HiQuestionMarkCircle },
  { id: 'billing', label: 'Billing & Credits', icon: HiMail }
];

export const HelpPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('getting-started');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const filteredFAQs = faqData.filter(faq => faq.category === selectedCategory);

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <div className="min-h-screen bg-background-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl font-bold text-white mb-4">Help & Support</h1>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Find answers to common questions and learn how to make the most of ChewyAI
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Category Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
              <h2 className="text-lg font-semibold text-white mb-4">Categories</h2>
              <nav className="space-y-2">
                {categories.map((category) => {
                  const Icon = category.icon;
                  const isActive = selectedCategory === category.id;
                  
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`
                        w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left
                        transition-all duration-200
                        ${isActive 
                          ? 'bg-primary-500/20 text-primary-400 border border-primary-500/30' 
                          : 'text-gray-300 hover:bg-background-tertiary hover:text-white'
                        }
                      `}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{category.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Contact Support */}
            <div className="bg-background-secondary rounded-lg p-6 border border-border-primary mt-6">
              <h3 className="text-lg font-semibold text-white mb-3">Need More Help?</h3>
              <p className="text-gray-400 text-sm mb-4">
                Can't find what you're looking for? Contact our support team.
              </p>
              <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors">
                <HiMail className="w-4 h-4" />
                <span>Contact Support</span>
                <HiExternalLink className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* FAQ Content */}
          <div className="lg:col-span-3">
            <motion.div
              key={selectedCategory}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-background-secondary rounded-lg border border-border-primary"
            >
              <div className="p-6 border-b border-border-primary">
                <h2 className="text-2xl font-bold text-white">
                  {categories.find(cat => cat.id === selectedCategory)?.label} FAQ
                </h2>
                <p className="text-gray-400 mt-2">
                  Frequently asked questions about {categories.find(cat => cat.id === selectedCategory)?.label.toLowerCase()}
                </p>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {filteredFAQs.map((faq, index) => (
                    <motion.div
                      key={faq.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="border border-border-primary rounded-lg overflow-hidden"
                    >
                      <button
                        onClick={() => toggleFAQ(faq.id)}
                        className="w-full flex items-center justify-between p-4 text-left hover:bg-background-tertiary transition-colors"
                      >
                        <span className="font-medium text-white pr-4">{faq.question}</span>
                        {expandedFAQ === faq.id ? (
                          <HiChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                        ) : (
                          <HiChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
                        )}
                      </button>
                      
                      <motion.div
                        initial={false}
                        animate={{
                          height: expandedFAQ === faq.id ? 'auto' : 0,
                          opacity: expandedFAQ === faq.id ? 1 : 0
                        }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="p-4 pt-0 border-t border-border-primary">
                          <p className="text-gray-300 leading-relaxed">{faq.answer}</p>
                        </div>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>

                {filteredFAQs.length === 0 && (
                  <div className="text-center py-12">
                    <HiQuestionMarkCircle className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-400 mb-2">No FAQs Available</h3>
                    <p className="text-gray-500">
                      We're working on adding more helpful content for this category.
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-background-secondary rounded-lg p-6 border border-border-primary text-center">
            <HiSparkles className="w-12 h-12 text-primary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Create Flashcards</h3>
            <p className="text-gray-400 text-sm mb-4">
              Generate AI-powered flashcards from your documents
            </p>
            <button className="text-primary-400 hover:text-primary-300 font-medium">
              Get Started →
            </button>
          </div>

          <div className="bg-background-secondary rounded-lg p-6 border border-border-primary text-center">
            <HiQuestionMarkCircle className="w-12 h-12 text-primary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Create Quizzes</h3>
            <p className="text-gray-400 text-sm mb-4">
              Build interactive quizzes with multiple question types
            </p>
            <button className="text-primary-400 hover:text-primary-300 font-medium">
              Get Started →
            </button>
          </div>

          <div className="bg-background-secondary rounded-lg p-6 border border-border-primary text-center">
            <HiDocumentText className="w-12 h-12 text-primary-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Upload Documents</h3>
            <p className="text-gray-400 text-sm mb-4">
              Upload and manage your study documents
            </p>
            <button className="text-primary-400 hover:text-primary-300 font-medium">
              Get Started →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
