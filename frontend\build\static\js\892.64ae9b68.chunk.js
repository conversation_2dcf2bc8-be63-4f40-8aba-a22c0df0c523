"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[892],{2171:(e,t,s)=>{s.d(t,{q:()=>r});var a=s(8957);const r=(0,s(5914).vt)(e=>({documents:[],selectedDocuments:new Set,isLoading:!1,uploadProgress:{},fetchDocuments:async()=>{e({isLoading:!0});try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents",{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)throw new Error("Failed to fetch documents");const a=await s.json();if(!a.success)throw new Error(a.error);e({documents:a.data,isLoading:!1})}catch(t){throw console.error("Fetch documents error:",t),e({isLoading:!1}),t}},uploadDocument:async t=>{const s=new FormData;s.append("document",t);try{const r=localStorage.getItem("auth_token"),n=await fetch("/api/documents/upload",{method:"POST",headers:{Authorization:"Bearer ".concat(r)},body:s});if(!n.ok){const e=await n.json();throw new Error(e.error||"Upload failed")}const o=await n.json();if(o.success)return e(e=>({documents:[o.data,...e.documents],uploadProgress:(0,a.A)((0,a.A)({},e.uploadProgress),{},{[t.name]:100})})),o.data;throw new Error(o.error)}catch(r){throw console.error("Upload document error:",r),r}},deleteDocument:async t=>{try{const s=localStorage.getItem("auth_token"),a=await fetch("/api/documents/".concat(t),{method:"DELETE",headers:{Authorization:"Bearer ".concat(s)}});if(!a.ok){const e=await a.json();throw new Error(e.error||"Delete failed")}e(e=>({documents:e.documents.filter(e=>e.id!==t),selectedDocuments:new Set([...e.selectedDocuments].filter(e=>e!==t))}))}catch(s){throw console.error("Delete document error:",s),s}},searchDocuments:async e=>{try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents/search?q=".concat(encodeURIComponent(e)),{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)throw new Error("Search failed");const a=await s.json();return a.success?a.data:[]}catch(t){return console.error("Search documents error:",t),[]}},getDocument:async e=>{try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)return null;const a=await s.json();return a.success?a.data:null}catch(t){return console.error("Get document error:",t),null}},toggleDocumentSelection:t=>{e(e=>{const s=new Set(e.selectedDocuments);return s.has(t)?s.delete(t):s.add(t),{selectedDocuments:s}})},clearSelection:()=>{e({selectedDocuments:new Set})},selectAll:()=>{e(e=>({selectedDocuments:new Set(e.documents.map(e=>e.id))}))},setUploadProgress:(t,s)=>{e(e=>({uploadProgress:(0,a.A)((0,a.A)({},e.uploadProgress),{},{[t]:s})}))}}))},6892:(e,t,s)=>{s.r(t),s.d(t,{Dashboard:()=>m});var a=s(9643),r=s(7192),n=s(4859),o=s(8957),c=s(4471),l=s(2171),i=s(6507);const d=()=>{const[e,t]=(0,a.useState)(!1),[s,n]=(0,a.useState)([]),[d,m]=(0,a.useState)([]),{uploadDocument:u,setUploadProgress:h}=(0,l.q)(),x=(0,r.Zp)(),p=(0,a.useCallback)(async e=>{t(!0),n([]),m([]);const s=[],a=[];for(const t of e)try{if(t.size>52428800){s.push("".concat(t.name,": File size exceeds 50MB limit"));continue}if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.openxmlformats-officedocument.presentationml.presentation"].includes(t.type)){s.push("".concat(t.name,": Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files."));continue}h(t.name,0),await u(t),h(t.name,100),a.push(t.name)}catch(r){s.push("".concat(t.name,": ").concat(r instanceof Error?r.message:"Unknown error"))}n(s),m(a),t(!1),a.length>0&&setTimeout(()=>m([]),3e3)},[u,h]),{getRootProps:g,getInputProps:f,isDragActive:y}=(0,c.VB)({onDrop:p,accept:{"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},multiple:!0,maxFiles:10,disabled:e});return(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsxs)("div",(0,o.A)((0,o.A)({},g()),{},{className:"\n          border-2 border-dashed rounded-xl p-12 text-center cursor-pointer transition-all duration-200\n          ".concat(y?"border-primary-500 bg-primary-500/10 scale-[1.02]":"border-gray-600 hover:border-primary-500 hover:bg-primary-500/5","\n          ").concat(e?"opacity-50 cursor-not-allowed":"","\n          bg-background-secondary/50\n        "),children:[(0,i.jsx)("input",(0,o.A)({},f())),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("div",{className:"flex justify-center",children:(0,i.jsx)("div",{className:"\n              p-4 rounded-full transition-colors\n              ".concat(y?"bg-primary-500/20":"bg-gray-800/50","\n            "),children:(0,i.jsx)("svg",{className:"h-16 w-16 transition-colors ".concat(y?"text-primary-400":"text-gray-400"),stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,i.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})})})}),y?(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-primary-400 mb-2",children:"Drop your documents here"}),(0,i.jsx)("p",{className:"text-gray-300",children:"Release to upload your files"})]}):(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Upload Your Documents"}),(0,i.jsxs)("p",{className:"text-lg text-gray-300 mb-2",children:["Drag & drop files here, or"," ",(0,i.jsx)("span",{className:"text-primary-500 font-semibold hover:text-primary-400 transition-colors",children:"browse to select"})]}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Supports PDF, DOCX, TXT, PPTX files (max 50MB each)"})]}),!y&&!e&&(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-4 pt-4",children:[(0,i.jsx)("button",{onClick:e=>{e.stopPropagation(),x("/documents")},className:"text-sm text-gray-400 hover:text-primary-400 transition-colors flex items-center gap-2",children:"\ud83d\udcc4 Manage existing documents"}),(0,i.jsx)("span",{className:"hidden sm:block text-gray-600",children:"\u2022"}),(0,i.jsx)("button",{onClick:e=>{e.stopPropagation(),x("/create-study-set")},className:"text-sm text-gray-400 hover:text-primary-400 transition-colors flex items-center gap-2",children:"\ud83d\udcda Create study set"})]})]})]})),e&&(0,i.jsxs)("div",{className:"mt-4 bg-background-secondary rounded-lg p-4 border border-gray-700",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"}),(0,i.jsx)("p",{className:"text-sm font-medium text-gray-300",children:"Uploading files..."})]}),(0,i.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-primary-500 h-2 rounded-full animate-pulse",style:{width:"60%"}})})]}),d.length>0&&(0,i.jsxs)("div",{className:"mt-4 bg-green-900/20 border border-green-700 rounded-lg p-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)("svg",{className:"h-5 w-5 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,i.jsx)("h4",{className:"text-green-400 font-medium",children:"Successfully uploaded:"})]}),(0,i.jsx)("ul",{className:"text-sm text-green-300 space-y-1",children:d.map((e,t)=>(0,i.jsxs)("li",{children:["\u2022 ",e]},t))})]}),s.length>0&&(0,i.jsxs)("div",{className:"mt-4 bg-red-900/20 border border-red-700 rounded-lg p-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)("svg",{className:"h-5 w-5 text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,i.jsx)("h4",{className:"text-red-400 font-medium",children:"Upload errors:"})]}),(0,i.jsx)("ul",{className:"text-sm text-red-300 space-y-1",children:s.map((e,t)=>(0,i.jsxs)("li",{children:["\u2022 ",e]},t))})]})]})},m=(0,a.memo)(()=>{const e=(0,r.Zp)(),[t,s]=(0,a.useState)([]),[o,c]=(0,a.useState)(!0),[l,m]=(0,a.useState)(null);(0,a.useEffect)(()=>{u()},[]);const u=async()=>{try{const e=localStorage.getItem("auth_token"),t=await fetch("/api/study-sets",{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw new Error("Failed to fetch study sets");const a=await t.json();if(!a.success)throw new Error(a.error||"Failed to fetch study sets");s(a.data)}catch(l){m(l.message)}finally{c(!1)}},h=e=>new Date(e).toLocaleDateString();return(0,i.jsx)("div",{className:"min-h-screen bg-background-primary text-white",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Your Study Sets"}),(0,i.jsx)("p",{className:"text-gray-400 mt-2",children:"Create, manage, and study your flashcard sets and quizzes"})]}),(0,i.jsx)("div",{className:"flex space-x-4",children:(0,i.jsx)(n.$,{onClick:()=>e("/create-study-set"),variant:"primary",children:"Create Study Set"})})]}),(0,i.jsx)(d,{}),(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Study Sets"}),o?(0,i.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),(0,i.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading study sets..."})]}):l?(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsxs)("div",{className:"text-red-400 mb-4",children:["Error: ",l]}),(0,i.jsx)(n.$,{onClick:u,variant:"secondary",children:"Try Again"})]}):0===t.length?(0,i.jsxs)("div",{className:"text-center py-12 bg-gray-800/50 rounded-lg",children:[(0,i.jsx)("div",{className:"text-gray-400 mb-4",children:"No study sets found"}),(0,i.jsx)("p",{className:"text-gray-500 mb-6",children:"Create your first study set to get started with studying"}),(0,i.jsx)("div",{className:"flex justify-center",children:(0,i.jsx)(n.$,{onClick:()=>e("/create-study-set"),variant:"primary",children:"Create Study Set"})})]}):(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(t=>(0,i.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer",onClick:()=>e("/study-sets/".concat(t.id)),children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-white truncate",children:t.name}),(0,i.jsx)("span",{className:"text-xs bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:t.type})]}),(0,i.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Items:"}),(0,i.jsx)("span",{children:"flashcards"===t.type?t.flashcard_count||0:t.quiz_question_count||0})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Created:"}),(0,i.jsx)("span",{children:h(t.created_at)})]}),t.last_studied_at&&(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Last studied:"}),(0,i.jsx)("span",{children:h(t.last_studied_at)})]}),t.is_ai_generated&&(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{className:"text-xs",children:"\ud83e\udd16"}),(0,i.jsx)("span",{children:"AI Generated"})]})]}),(0,i.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-700",children:(0,i.jsx)("div",{onClick:s=>{s.stopPropagation(),e("/study-sets/".concat(t.id))},children:(0,i.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",children:"Start Studying"})})})]},t.id))})]})]})})})}}]);
//# sourceMappingURL=892.64ae9b68.chunk.js.map