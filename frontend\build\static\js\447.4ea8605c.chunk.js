"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[447],{5447:(e,r,s)=>{s.r(r),s.d(r,{CreateStudySetPage:()=>c});var a=s(9643),t=s(7192),l=s(4859),n=s(467),i=s(1721),d=s(6507);const c=()=>{const e=(0,t.Zp)(),{alert:r}=(0,i.s)(),[s,c]=(0,a.useState)(""),[o,m]=(0,a.useState)("flashcards"),[u,h]=(0,a.useState)({}),[x,y]=(0,a.useState)(!1);return(0,d.jsx)("div",{className:"min-h-screen bg-background-primary text-white",children:(0,d.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Create New Study Set"}),(0,d.jsx)("p",{className:"text-gray-400",children:"Create an empty study set that you can populate with flashcards or quiz questions"})]}),(0,d.jsx)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:(0,d.jsxs)("form",{onSubmit:async a=>{if(a.preventDefault(),(()=>{const e={};return s.trim()||(e.name="Study set name is required"),h(e),0===Object.keys(e).length})()){y(!0);try{const r=await(async e=>{const r=localStorage.getItem("auth_token"),s=await fetch("/api/study-sets",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r)},body:JSON.stringify(e)});if(!s.ok){const e=await s.json();throw new Error(e.error||"Failed to create study set")}const a=await s.json();if(a.success)return a.data;throw new Error(a.error)})({name:s.trim(),type:o});e("/study-sets/".concat(r.id))}catch(t){await r({title:"Error",message:t.message||"Failed to create study set",variant:"error"})}finally{y(!1)}}},className:"space-y-6",children:[(0,d.jsx)(n.p,{label:"Study Set Name",value:s,onChange:c,placeholder:"e.g., Biology Chapter 5, History Final Review",error:u.name,required:!0,className:"w-full"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Primary Content Type"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsx)("div",{className:"\n                    p-4 rounded-lg border-2 cursor-pointer transition-all\n                    ".concat("flashcards"===o?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500","\n                  "),onClick:()=>m("flashcards"),children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"\n                      w-4 h-4 rounded-full border-2 flex-shrink-0\n                      ".concat("flashcards"===o?"border-primary-500 bg-primary-500":"border-gray-500","\n                    "),children:"flashcards"===o&&(0,d.jsx)("div",{className:"w-full h-full rounded-full bg-white scale-50"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-white",children:"Flashcards"}),(0,d.jsx)("p",{className:"text-sm text-gray-400",children:"Question and answer cards for memorization"})]})]})}),(0,d.jsx)("div",{className:"\n                    p-4 rounded-lg border-2 cursor-pointer transition-all\n                    ".concat("quiz"===o?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500","\n                  "),onClick:()=>m("quiz"),children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"\n                      w-4 h-4 rounded-full border-2 flex-shrink-0\n                      ".concat("quiz"===o?"border-primary-500 bg-primary-500":"border-gray-500","\n                    "),children:"quiz"===o&&(0,d.jsx)("div",{className:"w-full h-full rounded-full bg-white scale-50"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-white",children:"Quiz Questions"}),(0,d.jsx)("p",{className:"text-sm text-gray-400",children:"Multiple choice, true/false, and short answer questions"})]})]})})]})]}),(0,d.jsx)("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("div",{className:"w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5",children:"\ud83d\udca1"}),(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("p",{className:"text-blue-400 font-medium mb-1",children:"What happens next?"}),(0,d.jsx)("p",{className:"text-blue-300",children:"After creating your study set, you'll be able to add content manually or use AI to generate flashcards and quiz questions from your documents."})]})]})}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)(l.$,{type:"button",variant:"secondary",onClick:()=>e("/dashboard"),className:"flex-1",children:"Cancel"}),(0,d.jsx)(l.$,{type:"submit",variant:"primary",isLoading:x,className:"flex-1",children:"Create Study Set"})]})]})})]})})}}}]);
//# sourceMappingURL=447.4ea8605c.chunk.js.map