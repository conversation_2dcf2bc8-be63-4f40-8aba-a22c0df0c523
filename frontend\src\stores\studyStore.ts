import { create } from "zustand";
import { StudySet, Flashcard, QuizQuestion } from "../../../shared/types";

// Utility function for shuffling arrays using Fisher<PERSON><PERSON> algorithm
const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

interface StudyAction {
  type:
    | "NEXT_ITEM"
    | "PREVIOUS_ITEM"
    | "TOGGLE_FLAG"
    | "MARK_REVIEWED"
    | "SUBMIT_ANSWER";
  payload: any;
  previousState: Partial<StudySession>;
  timestamp: number;
}

interface StudySession {
  studySetId: string;
  type: "flashcards" | "quiz";
  startTime: Date;
  currentIndex: number;
  totalItems: number;
  reviewedItems: number[];
  flaggedItems: string[];
  correctAnswers?: number; // For quizzes
  timeSpent: number; // in seconds
  isShuffled: boolean; // Whether the session uses shuffled order
  originalOrder?: number[]; // Original indices for unshuffling if needed
}

interface StudySessionData {
  id: string;
  studySetId: string;
  type: "flashcards" | "quiz";
  startTime: Date;
  endTime?: Date;
  totalItems: number;
  reviewedItems: number;
  flaggedItems: number;
  correctAnswers?: number;
  timeSpent: number;
}

interface StudyState {
  currentSession: StudySession | null;
  studySetContent: {
    studySet?: StudySet;
    flashcards?: Flashcard[];
    questions?: QuizQuestion[];
  } | null;
  studySets: StudySet[];
  sessions: StudySessionData[];
  isLoading: boolean;
  error: string | null;

  // Undo/Redo functionality
  actionHistory: StudyAction[];
  currentActionIndex: number;
  canUndo: boolean;
  canRedo: boolean;

  // Actions
  startStudySession: (
    studySetId: string,
    type: "flashcards" | "quiz",
    shuffle?: boolean
  ) => Promise<void>;
  endStudySession: () => void;
  nextItem: () => void;
  previousItem: () => void;
  goToItem: (index: number) => void;
  toggleFlag: (itemId: string) => void;
  markReviewed: (itemId: string) => void;
  submitQuizAnswer: (
    questionId: string,
    answer: string[],
    isCorrect: boolean
  ) => void;
  updateTimeSpent: (seconds: number) => void;
  fetchStudySetContent: (
    studySetId: string,
    forceRefresh?: boolean
  ) => Promise<void>;
  fetchStudySets: () => Promise<void>;
  fetchStudySessions: (
    timeRange?: "7d" | "30d" | "90d" | "all"
  ) => Promise<void>;

  // Cache management methods
  invalidateStudySetContent: (studySetId?: string) => void;
  refreshStudySetContent: (studySetId: string) => Promise<void>;
  invalidateStudySets: () => void;

  // Undo/Redo actions
  undo: () => void;
  redo: () => void;
  clearHistory: () => void;
  addToHistory: (action: StudyAction) => void;
}

export const useStudyStore = create<StudyState>((set, get) => ({
  currentSession: null,
  studySetContent: null,
  studySets: [],
  sessions: [],
  isLoading: false,
  error: null,

  // Undo/Redo state
  actionHistory: [],
  currentActionIndex: -1,
  canUndo: false,
  canRedo: false,

  fetchStudySetContent: async (studySetId: string, forceRefresh = false) => {
    // If forceRefresh is true or content doesn't exist, refetch
    const { studySetContent } = get();
    if (
      forceRefresh ||
      !studySetContent ||
      studySetContent.studySet?.id !== studySetId
    ) {
      set({ isLoading: true, error: null });

      try {
        const token = localStorage.getItem("auth_token");

        const response = await fetch(`/api/study-sets/${studySetId}/content`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const errorResult = await response.json();
          throw new Error(
            errorResult.error || "Failed to fetch study set content"
          );
        }

        const result = await response.json();

        if (result.success) {
          set({
            studySetContent: {
              studySet: result.data.studySet,
              flashcards: result.data.flashcards || [],
              questions: result.data.questions || [],
            },
            isLoading: false,
          });
        } else {
          throw new Error(result.error);
        }
      } catch (error: any) {
        set({
          error: error.message || "Failed to fetch study set content",
          isLoading: false,
        });
        throw error;
      }
    }
  },

  startStudySession: async (
    studySetId: string,
    type: "flashcards" | "quiz",
    shuffle = false
  ) => {
    const { studySetContent, fetchStudySetContent } = get();

    // Fetch content if not already loaded
    if (!studySetContent || studySetContent.studySet?.id !== studySetId) {
      await fetchStudySetContent(studySetId);
    }

    const content = get().studySetContent;
    if (!content) {
      throw new Error("Failed to load study set content");
    }

    const totalItems =
      type === "flashcards"
        ? content.flashcards?.length || 0
        : content.questions?.length || 0;

    if (totalItems === 0) {
      throw new Error("No study materials found in this set");
    }

    // Handle shuffling
    let originalOrder: number[] | undefined;
    if (shuffle) {
      // Create array of original indices
      originalOrder = Array.from({ length: totalItems }, (_, i) => i);

      // Shuffle the content arrays
      if (type === "flashcards" && content.flashcards) {
        const shuffledFlashcards = shuffleArray(content.flashcards);
        set((state) => ({
          studySetContent: {
            ...state.studySetContent!,
            flashcards: shuffledFlashcards,
          },
        }));
      } else if (type === "quiz" && content.questions) {
        const shuffledQuestions = shuffleArray(content.questions);
        set((state) => ({
          studySetContent: {
            ...state.studySetContent!,
            questions: shuffledQuestions,
          },
        }));
      }
    }

    set({
      currentSession: {
        studySetId,
        type,
        startTime: new Date(),
        currentIndex: 0,
        totalItems,
        reviewedItems: [],
        flaggedItems: [],
        correctAnswers: type === "quiz" ? 0 : undefined,
        timeSpent: 0,
        isShuffled: shuffle,
        originalOrder,
      },
    });
  },

  endStudySession: () => {
    set({ currentSession: null });
  },

  nextItem: () => {
    const { currentSession, addToHistory } = get();
    if (!currentSession) return;

    // Implement circular navigation: if at last item, wrap to first item
    const nextIndex =
      currentSession.currentIndex === currentSession.totalItems - 1
        ? 0
        : currentSession.currentIndex + 1;

    // Record action in history
    addToHistory({
      type: "NEXT_ITEM",
      payload: { fromIndex: currentSession.currentIndex, toIndex: nextIndex },
      previousState: { currentIndex: currentSession.currentIndex },
      timestamp: Date.now(),
    });

    set({
      currentSession: {
        ...currentSession,
        currentIndex: nextIndex,
      },
    });
  },

  previousItem: () => {
    const { currentSession, addToHistory } = get();
    if (!currentSession) return;

    // Implement circular navigation: if at first item, wrap to last item
    const prevIndex =
      currentSession.currentIndex === 0
        ? currentSession.totalItems - 1
        : currentSession.currentIndex - 1;

    // Record action in history
    addToHistory({
      type: "PREVIOUS_ITEM",
      payload: { fromIndex: currentSession.currentIndex, toIndex: prevIndex },
      previousState: { currentIndex: currentSession.currentIndex },
      timestamp: Date.now(),
    });

    set({
      currentSession: {
        ...currentSession,
        currentIndex: prevIndex,
      },
    });
  },

  goToItem: (index: number) => {
    const { currentSession } = get();
    if (!currentSession) return;

    const clampedIndex = Math.max(
      0,
      Math.min(index, currentSession.totalItems - 1)
    );
    set({
      currentSession: {
        ...currentSession,
        currentIndex: clampedIndex,
      },
    });
  },

  toggleFlag: (itemId: string) => {
    const { currentSession, addToHistory } = get();
    if (!currentSession) return;

    const wasFlagged = currentSession.flaggedItems.includes(itemId);
    const flaggedItems = wasFlagged
      ? currentSession.flaggedItems.filter((id) => id !== itemId)
      : [...currentSession.flaggedItems, itemId];

    // Record action in history
    addToHistory({
      type: "TOGGLE_FLAG",
      payload: { itemId, wasFlagged },
      previousState: { flaggedItems: currentSession.flaggedItems },
      timestamp: Date.now(),
    });

    set({
      currentSession: {
        ...currentSession,
        flaggedItems,
      },
    });
  },

  markReviewed: (_itemId: string) => {
    const { currentSession } = get();
    if (!currentSession) return;

    if (!currentSession.reviewedItems.includes(currentSession.currentIndex)) {
      set({
        currentSession: {
          ...currentSession,
          reviewedItems: [
            ...currentSession.reviewedItems,
            currentSession.currentIndex,
          ],
        },
      });
    }
  },

  submitQuizAnswer: (
    questionId: string,
    _answer: string[],
    isCorrect: boolean
  ) => {
    const { currentSession, markReviewed } = get();
    if (!currentSession || currentSession.type !== "quiz") return;

    markReviewed(questionId);

    if (isCorrect) {
      set({
        currentSession: {
          ...currentSession,
          correctAnswers: (currentSession.correctAnswers || 0) + 1,
        },
      });
    }
  },

  updateTimeSpent: (seconds: number) => {
    const { currentSession } = get();
    if (!currentSession) return;

    set({
      currentSession: {
        ...currentSession,
        timeSpent: currentSession.timeSpent + seconds,
      },
    });
  },

  // Helper function to add action to history
  addToHistory: (action: StudyAction) => {
    const { actionHistory, currentActionIndex } = get();

    // Remove any actions after current index (when undoing then doing new action)
    const newHistory = actionHistory.slice(0, currentActionIndex + 1);
    newHistory.push(action);

    // Limit history to last 50 actions for performance
    const limitedHistory = newHistory.slice(-50);

    set({
      actionHistory: limitedHistory,
      currentActionIndex: limitedHistory.length - 1,
      canUndo: limitedHistory.length > 0,
      canRedo: false,
    });
  },

  undo: () => {
    const { actionHistory, currentActionIndex, currentSession } = get();

    if (currentActionIndex < 0 || !currentSession) return;

    const action = actionHistory[currentActionIndex];

    // Restore previous state
    set({
      currentSession: {
        ...currentSession,
        ...action.previousState,
      },
      currentActionIndex: currentActionIndex - 1,
      canUndo: currentActionIndex > 0,
      canRedo: true,
    });
  },

  redo: () => {
    const { actionHistory, currentActionIndex, currentSession } = get();

    if (currentActionIndex >= actionHistory.length - 1 || !currentSession)
      return;

    const nextActionIndex = currentActionIndex + 1;
    const action = actionHistory[nextActionIndex];

    // Re-apply the action
    switch (action.type) {
      case "NEXT_ITEM":
        get().nextItem();
        break;
      case "PREVIOUS_ITEM":
        get().previousItem();
        break;
      case "TOGGLE_FLAG":
        get().toggleFlag(action.payload.itemId);
        break;
      case "MARK_REVIEWED":
        get().markReviewed(action.payload.itemId);
        break;
    }

    set({
      currentActionIndex: nextActionIndex,
      canUndo: true,
      canRedo: nextActionIndex < actionHistory.length - 1,
    });
  },

  clearHistory: () => {
    set({
      actionHistory: [],
      currentActionIndex: -1,
      canUndo: false,
      canRedo: false,
    });
  },

  fetchStudySets: async () => {
    set({ isLoading: true, error: null });

    try {
      const token = localStorage.getItem("auth_token");

      const response = await fetch("/api/study-sets", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || "Failed to fetch study sets");
      }

      const result = await response.json();

      if (result.success) {
        set({
          studySets: result.data,
          isLoading: false,
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      set({
        error: error.message || "Failed to fetch study sets",
        isLoading: false,
      });
      throw error;
    }
  },

  fetchStudySessions: async (timeRange = "30d") => {
    set({ isLoading: true, error: null });

    try {
      const token = localStorage.getItem("auth_token");

      const response = await fetch(
        `/api/study-sessions?timeRange=${timeRange}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || "Failed to fetch study sessions");
      }

      const result = await response.json();

      if (result.success) {
        // Convert date strings to Date objects
        const sessions = result.data.map((session: any) => ({
          ...session,
          startTime: new Date(session.startTime),
          endTime: session.endTime ? new Date(session.endTime) : undefined,
        }));

        set({
          sessions,
          isLoading: false,
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      set({
        error: error.message || "Failed to fetch study sessions",
        isLoading: false,
      });
      throw error;
    }
  },

  // Cache management methods
  invalidateStudySetContent: (studySetId?: string) => {
    const { studySetContent } = get();

    if (studySetId) {
      // Clear cache for specific study set only if it matches
      if (studySetContent?.studySet?.id === studySetId) {
        set({ studySetContent: null });
      }
    } else {
      // Clear all cached study set content
      set({ studySetContent: null });
    }
  },

  refreshStudySetContent: async (studySetId: string) => {
    // Force a fresh fetch of study set content
    await get().fetchStudySetContent(studySetId, true);
  },

  invalidateStudySets: () => {
    // Clear the study sets list to force refetch
    set({ studySets: [] });
  },
}));
