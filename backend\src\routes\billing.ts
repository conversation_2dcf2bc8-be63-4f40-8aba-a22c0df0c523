import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { stripeService } from '../services/stripeService';
import { supabase } from '../services/supabaseService';

const router = express.Router();

// Get billing data (invoices, payment methods, etc.)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({ 
        success: false, 
        error: 'User ID is required' 
      });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (!profile?.stripe_customer_id) {
      // User has no Stripe customer - return empty billing data
      return res.json({
        success: true,
        data: {
          invoices: [],
          paymentMethods: [],
          defaultPaymentMethod: null,
          upcomingInvoice: null
        }
      });
    }

    // Get billing data from Stripe
    const [invoices, paymentMethods, upcomingInvoice] = await Promise.all([
      stripeService.getCustomerInvoices(profile.stripe_customer_id, 10),
      stripeService.getCustomerPaymentMethods(profile.stripe_customer_id),
      stripeService.getUpcomingInvoice(profile.stripe_customer_id).catch(() => null)
    ]);

    // Get default payment method
    const customer = await stripeService.getCustomer(profile.stripe_customer_id);
    const defaultPaymentMethod = customer && 'invoice_settings' in customer
      ? customer.invoice_settings?.default_payment_method
      : null;

    res.json({
      success: true,
      data: {
        invoices,
        paymentMethods,
        defaultPaymentMethod,
        upcomingInvoice
      }
    });

  } catch (error) {
    console.error('Error fetching billing data:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch billing data' 
    });
  }
});

// Download invoice
router.get('/invoices/:invoiceId/download', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { invoiceId } = req.params;

    if (!userId || !invoiceId) {
      return res.status(400).json({ 
        success: false, 
        error: 'User ID and invoice ID are required' 
      });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.status(404).json({ 
        success: false, 
        error: 'Customer not found' 
      });
    }

    // Verify invoice belongs to customer
    const invoice = await stripeService.getInvoice(invoiceId);
    if (invoice.customer !== profile.stripe_customer_id) {
      return res.status(403).json({ 
        success: false, 
        error: 'Access denied' 
      });
    }

    // Get invoice PDF URL
    if (!invoice.invoice_pdf) {
      return res.status(404).json({ 
        success: false, 
        error: 'Invoice PDF not available' 
      });
    }

    // Redirect to Stripe's PDF URL
    res.redirect(invoice.invoice_pdf);

  } catch (error) {
    console.error('Error downloading invoice:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to download invoice' 
    });
  }
});

// Set default payment method
router.post('/payment-methods/default', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { paymentMethodId } = req.body;

    if (!userId || !paymentMethodId) {
      return res.status(400).json({ 
        success: false, 
        error: 'User ID and payment method ID are required' 
      });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.status(404).json({ 
        success: false, 
        error: 'Customer not found' 
      });
    }

    // Verify payment method belongs to customer
    const paymentMethod = await stripeService.getPaymentMethod(paymentMethodId);
    if (paymentMethod.customer !== profile.stripe_customer_id) {
      return res.status(403).json({ 
        success: false, 
        error: 'Access denied' 
      });
    }

    // Set as default payment method
    await stripeService.setDefaultPaymentMethod(profile.stripe_customer_id, paymentMethodId);

    res.json({ 
      success: true, 
      message: 'Default payment method updated' 
    });

  } catch (error) {
    console.error('Error setting default payment method:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to update default payment method' 
    });
  }
});

// Add payment method
router.post('/payment-methods', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { paymentMethodId } = req.body;

    if (!userId || !paymentMethodId) {
      return res.status(400).json({ 
        success: false, 
        error: 'User ID and payment method ID are required' 
      });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id, email, name')
      .eq('id', userId)
      .single();

    if (!profile) {
      return res.status(404).json({ 
        success: false, 
        error: 'User not found' 
      });
    }

    // Create or get customer if needed
    const customerId = await stripeService.createOrGetCustomer(userId, profile.email, profile.name);

    // Attach payment method to customer
    await stripeService.attachPaymentMethod(paymentMethodId, customerId);

    res.json({ 
      success: true, 
      message: 'Payment method added' 
    });

  } catch (error) {
    console.error('Error adding payment method:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to add payment method' 
    });
  }
});

// Remove payment method
router.delete('/payment-methods/:paymentMethodId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { paymentMethodId } = req.params;

    if (!userId || !paymentMethodId) {
      return res.status(400).json({ 
        success: false, 
        error: 'User ID and payment method ID are required' 
      });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.status(404).json({ 
        success: false, 
        error: 'Customer not found' 
      });
    }

    // Verify payment method belongs to customer
    const paymentMethod = await stripeService.getPaymentMethod(paymentMethodId);
    if (paymentMethod.customer !== profile.stripe_customer_id) {
      return res.status(403).json({ 
        success: false, 
        error: 'Access denied' 
      });
    }

    // Detach payment method
    await stripeService.detachPaymentMethod(paymentMethodId);

    res.json({ 
      success: true, 
      message: 'Payment method removed' 
    });

  } catch (error) {
    console.error('Error removing payment method:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to remove payment method' 
    });
  }
});

export default router;
