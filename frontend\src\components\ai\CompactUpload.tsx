import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useDocumentStore } from '../../stores/documentStore';

interface CompactUploadProps {
  onUploadComplete?: () => void;
}

export const CompactUpload: React.FC<CompactUploadProps> = ({ onUploadComplete }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const [uploadSuccess, setUploadSuccess] = useState<string[]>([]);
  const { uploadDocument, setUploadProgress } = useDocumentStore();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsUploading(true);
    setUploadErrors([]);
    setUploadSuccess([]);

    const errors: string[] = [];
    const successes: string[] = [];

    for (const file of acceptedFiles) {
      try {
        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
          errors.push(`${file.name}: File size exceeds 50MB limit`);
          continue;
        }

        // Validate file type
        const allowedTypes = [
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ];

        if (!allowedTypes.includes(file.type)) {
          errors.push(`${file.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);
          continue;
        }

        // Set initial progress
        setUploadProgress(file.name, 0);

        // Upload file
        await uploadDocument(file);
        
        // Set completion progress
        setUploadProgress(file.name, 100);
        successes.push(file.name);
      } catch (error) {
        errors.push(`${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    setUploadErrors(errors);
    setUploadSuccess(successes);
    setIsUploading(false);

    // Call callback if provided
    if (onUploadComplete && successes.length > 0) {
      onUploadComplete();
    }

    // Auto-clear success messages after 3 seconds
    if (successes.length > 0) {
      setTimeout(() => setUploadSuccess([]), 3000);
    }
  }, [uploadDocument, setUploadProgress, onUploadComplete]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx']
    },
    multiple: true,
    maxFiles: 10,
    disabled: isUploading
  });

  return (
    <div className="space-y-3">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200
          ${isDragActive 
            ? 'border-primary-500 bg-primary-500/10' 
            : 'border-gray-600 hover:border-primary-500 hover:bg-primary-500/5'
          }
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-2">
          {/* Upload Icon */}
          <svg
            className={`mx-auto h-8 w-8 transition-colors ${
              isDragActive ? 'text-primary-400' : 'text-gray-400'
            }`}
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          
          {/* Upload Text */}
          {isDragActive ? (
            <p className="text-primary-400 font-medium">Drop files here...</p>
          ) : (
            <div>
              <p className="text-gray-300">
                <span className="font-medium">Upload new documents</span> or drag & drop here
              </p>
              <p className="text-xs text-gray-500 mt-1">
                PDF, DOCX, TXT, PPTX (max 50MB each)
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="bg-background-secondary rounded-lg p-3 border border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
            <p className="text-xs font-medium text-gray-300">Uploading files...</p>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-1.5">
            <div className="bg-primary-500 h-1.5 rounded-full animate-pulse" style={{ width: '60%' }}></div>
          </div>
        </div>
      )}

      {/* Success Messages */}
      {uploadSuccess.length > 0 && (
        <div className="bg-green-900/20 border border-green-700 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-1">
            <svg className="h-4 w-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <h4 className="text-green-400 font-medium text-sm">Successfully uploaded:</h4>
          </div>
          <ul className="text-xs text-green-300 space-y-0.5">
            {uploadSuccess.map((filename, index) => (
              <li key={index}>• {filename}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Error Messages */}
      {uploadErrors.length > 0 && (
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-1">
            <svg className="h-4 w-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <h4 className="text-red-400 font-medium text-sm">Upload errors:</h4>
          </div>
          <ul className="text-xs text-red-300 space-y-0.5">
            {uploadErrors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
