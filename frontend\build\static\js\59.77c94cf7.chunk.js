"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[59],{4059:(e,t,s)=>{s.r(t),s.d(t,{HelpPage:()=>l});var r=s(9643),a=s(8002),i=s(344),n=s(6507);const o=[{id:"1",category:"getting-started",question:"How do I get started with ChewyAI?",answer:"Start by uploading your documents in the Documents section, then use our AI-powered tools to generate flashcards or quizzes from your content. You can also create study materials manually."},{id:"2",category:"documents",question:"What file formats are supported?",answer:"ChewyAI supports PDF, DOCX, TXT, and PPTX files. Make sure your documents contain readable text for the best AI generation results."},{id:"3",category:"flashcards",question:"How does AI flashcard generation work?",answer:"Our AI analyzes your uploaded documents and creates relevant flashcards with questions and answers. You can specify the number of cards to generate and provide custom prompts for better results."},{id:"4",category:"quizzes",question:"Can I create different types of quiz questions?",answer:"Yes! ChewyAI supports multiple choice, true/false, and short answer questions. You can specify the question types when generating quizzes with AI."},{id:"5",category:"billing",question:"How does the credit system work?",answer:"Credits are used for AI-powered features like generating flashcards and quizzes. Manual creation is always free. You can purchase credit packages or subscribe for unlimited usage."},{id:"6",category:"getting-started",question:"How do I study with my created materials?",answer:"Navigate to your study sets from the dashboard and choose your preferred study mode. Use keyboard shortcuts for efficient navigation during study sessions."}],d=[{id:"getting-started",label:"Getting Started",icon:i.dHv},{id:"documents",label:"Documents",icon:i.gpD},{id:"flashcards",label:"Flashcards",icon:i.pCw},{id:"quizzes",label:"Quizzes",icon:i.xR_},{id:"billing",label:"Billing & Credits",icon:i.d8c}],l=()=>{var e,t;const[s,l]=(0,r.useState)("getting-started"),[c,m]=(0,r.useState)(null),u=o.filter(e=>e.category===s);return(0,n.jsx)("div",{className:"min-h-screen bg-background-primary text-white",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,n.jsx)("div",{className:"text-center mb-12",children:(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"Help & Support"}),(0,n.jsx)("p",{className:"text-xl text-gray-400 max-w-2xl mx-auto",children:"Find answers to common questions and learn how to make the most of ChewyAI"})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,n.jsxs)("div",{className:"lg:col-span-1",children:[(0,n.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Categories"}),(0,n.jsx)("nav",{className:"space-y-2",children:d.map(e=>{const t=e.icon,r=s===e.id;return(0,n.jsxs)("button",{onClick:()=>l(e.id),className:"\n                        w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left\n                        transition-all duration-200\n                        ".concat(r?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white","\n                      "),children:[(0,n.jsx)(t,{className:"w-5 h-5"}),(0,n.jsx)("span",{className:"font-medium",children:e.label})]},e.id)})})]}),(0,n.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary mt-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-white mb-3",children:"Need More Help?"}),(0,n.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Can't find what you're looking for? Contact our support team."}),(0,n.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors",children:[(0,n.jsx)(i.d8c,{className:"w-4 h-4"}),(0,n.jsx)("span",{children:"Contact Support"}),(0,n.jsx)(i.$xg,{className:"w-4 h-4"})]})]})]}),(0,n.jsx)("div",{className:"lg:col-span-3",children:(0,n.jsxs)(a.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg border border-border-primary",children:[(0,n.jsxs)("div",{className:"p-6 border-b border-border-primary",children:[(0,n.jsxs)("h2",{className:"text-2xl font-bold text-white",children:[null===(e=d.find(e=>e.id===s))||void 0===e?void 0:e.label," FAQ"]}),(0,n.jsxs)("p",{className:"text-gray-400 mt-2",children:["Frequently asked questions about ",null===(t=d.find(e=>e.id===s))||void 0===t?void 0:t.label.toLowerCase()]})]}),(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsx)("div",{className:"space-y-4",children:u.map((e,t)=>(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},className:"border border-border-primary rounded-lg overflow-hidden",children:[(0,n.jsxs)("button",{onClick:()=>{return t=e.id,void m(c===t?null:t);var t},className:"w-full flex items-center justify-between p-4 text-left hover:bg-background-tertiary transition-colors",children:[(0,n.jsx)("span",{className:"font-medium text-white pr-4",children:e.question}),c===e.id?(0,n.jsx)(i.bv7,{className:"w-5 h-5 text-gray-400 flex-shrink-0"}):(0,n.jsx)(i.huF,{className:"w-5 h-5 text-gray-400 flex-shrink-0"})]}),(0,n.jsx)(a.P.div,{initial:!1,animate:{height:c===e.id?"auto":0,opacity:c===e.id?1:0},transition:{duration:.3},className:"overflow-hidden",children:(0,n.jsx)("div",{className:"p-4 pt-0 border-t border-border-primary",children:(0,n.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e.answer})})})]},e.id))}),0===u.length&&(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)(i.xR_,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-400 mb-2",children:"No FAQs Available"}),(0,n.jsx)("p",{className:"text-gray-500",children:"We're working on adding more helpful content for this category."})]})]})]},s)})]}),(0,n.jsxs)("div",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary text-center",children:[(0,n.jsx)(i.pCw,{className:"w-12 h-12 text-primary-400 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Create Flashcards"}),(0,n.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Generate AI-powered flashcards from your documents"}),(0,n.jsx)("button",{className:"text-primary-400 hover:text-primary-300 font-medium",children:"Get Started \u2192"})]}),(0,n.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary text-center",children:[(0,n.jsx)(i.xR_,{className:"w-12 h-12 text-primary-400 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Create Quizzes"}),(0,n.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Build interactive quizzes with multiple question types"}),(0,n.jsx)("button",{className:"text-primary-400 hover:text-primary-300 font-medium",children:"Get Started \u2192"})]}),(0,n.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary text-center",children:[(0,n.jsx)(i.gpD,{className:"w-12 h-12 text-primary-400 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Upload Documents"}),(0,n.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Upload and manage your study documents"}),(0,n.jsx)("button",{className:"text-primary-400 hover:text-primary-300 font-medium",children:"Get Started \u2192"})]})]})]})})}}}]);
//# sourceMappingURL=59.77c94cf7.chunk.js.map