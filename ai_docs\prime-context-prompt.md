# ChewyAI Project Context Priming Prompt

## Current Project Status (Updated: 2025-01-26)
**✅ Phase 1 COMPLETE:** Foundation Setup - Monorepo, shared types, dev environment
**✅ Phase 2 COMPLETE:** Database Schema & Security - 7 tables, RLS policies, triggers, stored procedures
**🎯 Phase 3 NEXT:** Authentication System - Supabase auth integration, protected routes

## Instructions for AI Assistant
Use this prompt to quickly understand the ChewyAI project state and continue development work seamlessly.

## Step 1: Read Core Project Documentation
**REQUIRED**: Read these files to understand the project scope and requirements:

1. **`ChewyAI_Complete_PRD_v3.md`** - Complete Product Requirements Document
   - Contains comprehensive technical specifications
   - Section 2.5 has critical shared TypeScript types
   - Database schema, API specifications, and component requirements

2. **`ChewyAI_Functional_Spec.md`** - Functional Requirements
   - Business model and user workflows
   - Security and compliance requirements
   - Performance specifications

## Step 2: Review Implementation Progress
**REQUIRED**: Review all files in the `chewyai_spec/` folder to understand current implementation state:

### Task Files:
- `01_foundation_setup.md` - ✅ COMPLETE - Project structure and shared types
- `02_database_schema.md` - ✅ COMPLETE - Supabase database with RLS policies
- `03_authentication_system.md` - 🎯 NEXT PHASE - Backend auth with Supabase
- `04_frontend_auth_components.md` - ⏳ PENDING - React auth components with Zustand
- `05_document_management_backend.md` - ⏳ PENDING - File upload and processing
- `06_credit_system_backend.md` - ⏳ PENDING - Credit management with atomic operations
- `07_ai_integration_backend.md` - ⏳ PENDING - OpenRouter AI with Gemini model
- `08_study_set_management.md` - ⏳ PENDING - Study set CRUD and progress tracking
- `09_frontend_document_management.md` - ⏳ PENDING - Document upload UI components
- `10_ai_generation_frontend.md` - ⏳ PENDING - AI generation forms and workflows
- `11_study_interfaces.md` - ⏳ PENDING - Study interface components

### Check for Additional Files:
Look for any additional numbered files (11+) that may have been created since this prompt was written.

## Step 3: Understand Technical Stack
**Key Technologies:**
- **Frontend**: React 18 + TypeScript + TailwindCSS + Vite + Zustand + React Query
- **Backend**: Express.js + TypeScript + Supabase (PostgreSQL + Auth) + Stripe
- **AI**: OpenRouter API with Gemini-2.0-flash-exp model
- **Architecture**: Monorepo with frontend building into backend's public folder
- **Design**: Dark space theme (#0a0a0a) with purple accents (#8b5cf6, #a855f7)

**Credit System & Pricing Model:**
- **1 credit = 5 generated items** (flashcards, quiz questions, etc.)
- **10 credits = 50 generated items** (consistent 1:5 ratio across all AI generation types)
- **Default Flashcard Generation**: 50 flashcards per generation (10 credits)
- **Generation Approximation**: Number of flashcards generated is approximated since the LLM may generate a few more or less cards than requested
- **Generation Types**: Flashcards, quiz questions, document processing, custom prompts
- **Credit Packages**: Study Buddy (100 credits), Dean's List (500+50 bonus), Academic Legend (2,500+750 bonus)
- **Subscription Tiers**: Study Starter (500 generations/month free), Scholar Pro (unlimited), Academic Year Pass (unlimited)

## Step 4: Assess Current State
**Current Implementation Status:**

1. **✅ Phase 1 & 2 COMPLETE** - Foundation and Database are fully implemented
2. **🎯 Phase 3 NEXT** - Authentication System implementation required
3. **Database Ready** - 7 tables with RLS policies, triggers, stored procedures all working
4. **Backend Configured** - Supabase client integrated, health checks passing

**Next Action:** Begin Phase 3 (Authentication System) implementation

## Phase 2 Completion Details
**Database Schema & Security (COMPLETE):**
- ✅ 7 database tables: users, documents, study_sets, flashcards, quiz_questions, credit_transactions, ai_operation_costs
- ✅ Row Level Security (RLS) policies ensuring user data isolation
- ✅ Database triggers for automatic timestamp updates and item count maintenance
- ✅ Stored procedures for atomic credit operations with row locking
- ✅ Performance indexes for all query patterns
- ✅ Supabase client configured in backend with TypeScript types
- ✅ Health check endpoint testing database connectivity
- ✅ Environment configuration with .env files

## Step 5: Continue Development
**Immediate Next Steps:**
1. **Phase 3: Authentication System** - Implement Supabase auth integration
2. **Phase 4: Frontend Auth Components** - React auth components with Zustand
3. **Phase 5: Document Management Backend** - File upload and processing
4. **Phase 6: Credit System Backend** - Credit management with atomic operations
5. **Phase 7: AI Integration Backend** - OpenRouter AI with Gemini model

**Remaining phases after Phase 7:**
- Study set management and progress tracking
- Frontend document management components
- AI generation forms and workflows
- Study interface components (flashcard/quiz)
- Testing and deployment

## Development Standards
**Maintain these standards established in previous work:**

### Code Quality:
- Use shared TypeScript types from the PRD
- Implement proper error handling and validation
- Follow security best practices (RLS policies, input validation)
- Use atomic operations for credit management
- Implement responsive design patterns

### Credit System Implementation:
- **UPDATED PRICING MODEL**: 1 credit = 5 generated items (consistent across all generation types)
- **Generation Ratios**: All AI operations follow the 1:5 credit-to-generation ratio
- **Credit Validation**: Ensure all generation endpoints validate sufficient credits before processing
- **Transaction Tracking**: Record all credit usage with proper audit trails
- **Cost Calculations**: Update all pricing displays and calculations to reflect 1:5 ratio

### Documentation Maintenance:
- **CRITICAL**: Keep AI docs specifications synchronized with implementation
- **Update task files** when implementation deviates from original specs
- **Update PRD and Functional Spec** when adding new features or changing business logic
- **Update shared types documentation** when modifying TypeScript interfaces
- **Verify documentation accuracy** before marking phases complete

### File Organization:
- Each phase should have detailed task files with:
  - Complete code implementations
  - File structure specifications
  - Acceptance criteria
  - Dependency mappings
  - Estimated time requirements

### Technical Approach:
- Use package managers for dependency management
- Implement Row Level Security for data isolation
- Use stored procedures for atomic database operations
- Follow the established component patterns and naming conventions
- Maintain the dark theme design system

## Step 6: Git Workflow Setup
**REQUIRED**: Establish proper Git workflow before making any changes:

### 6.1 Check Current Git State
```bash
# Check current branch and status
git status
git branch -a
git log --oneline -5
```

### 6.2 Setup Branch Structure (First Time Only)
**Repository**: `Chewy42/chewy-ai-one-shot`

If staging branch doesn't exist, create it:
```bash
# Check if staging branch exists
git branch -r | grep staging

# If not exists, create staging branch from main
git checkout main
git pull origin main
git checkout -b staging
git push origin staging
```

### 6.3 Create Feature Branch
**Always work in feature branches, never directly on main or staging:**

```bash
# Create and switch to feature branch for current phase
git checkout staging
git pull origin staging
git checkout -b feature/phase-[NUMBER]-[DESCRIPTION]
# Example: git checkout -b feature/phase-11-study-interfaces
```

**Branch Naming Convention:**
- `feature/phase-[NUMBER]-[DESCRIPTION]` - For new phase implementations
- `feature/fix-[DESCRIPTION]` - For bug fixes
- `feature/enhance-[DESCRIPTION]` - For enhancements
- `feature/docs-[DESCRIPTION]` - For documentation updates

### 6.4 Setup Branch Protection (Recommended)
Use GitHub API to set up branch protection rules:
```bash
# Protect main branch - require PR reviews
# Protect staging branch - require PR reviews
# This ensures proper code review workflow
```

## Step 7: Implementation Workflow
**Follow this systematic approach for robust development:**

### 7.1 Plan Before Code
1. **Create/Update PROGRESS.md** with current phase details
2. **Review task file** for the current phase thoroughly
3. **Identify all files** that need to be created/modified
4. **Plan commit strategy** - logical, atomic commits
5. **Check documentation alignment** - verify specs match current implementation
6. **Update AI docs** if implementation requires changes to documented features

### 7.2 Implement with Git Discipline
```bash
# Make frequent, logical commits during implementation
git add [specific-files]
git commit -m "feat(phase-X): implement [specific-component]

- Add [specific functionality]
- Include [specific features]
- Follow [specific patterns]

Refs: #[issue-number] if applicable"
```

**Commit Message Format:**
- `feat(phase-X): description` - New features
- `fix(phase-X): description` - Bug fixes
- `docs(phase-X): description` - Documentation
- `test(phase-X): description` - Tests
- `refactor(phase-X): description` - Code refactoring

### 7.3 Testing and Validation
Before creating PR:
```bash
# Test implementations if applicable
npm test
npm run build
npm run lint

# Check for any issues
git status
git diff --staged
```

## Step 8: Pull Request Workflow
**Create systematic PRs for code review and integration:**

### 8.1 Push Feature Branch
```bash
# Push feature branch to remote
git push origin feature/phase-[NUMBER]-[DESCRIPTION]
```

### 8.2 Create Pull Request to Staging
**Use GitHub API or web interface:**
- **Target**: `staging` branch (never directly to main)
- **Title**: `Phase [NUMBER]: [Description]`
- **Description**: Include:
  - Summary of changes
  - Files created/modified
  - Testing performed
  - Acceptance criteria met
  - Any dependencies or notes

### 8.3 PR Review Process
- **Self-review**: Check all changes before requesting review
- **Automated checks**: Ensure CI/CD passes
- **Manual testing**: Verify functionality works as expected

## Step 9: Branch Management
**Maintain clean branch structure:**

### 9.1 Staging Branch
- **Purpose**: Integration testing and validation
- **Merges from**: Feature branches
- **Merges to**: Main branch (after validation)
- **Protection**: Require PR reviews, passing tests

### 9.2 Main Branch
- **Purpose**: Production-ready code
- **Merges from**: Staging branch only
- **Protection**: Require PR reviews, admin approval
- **Tags**: Version releases

### 9.3 Cleanup
```bash
# After successful merge to staging
git checkout staging
git pull origin staging
git branch -d feature/phase-[NUMBER]-[DESCRIPTION]
git push origin --delete feature/phase-[NUMBER]-[DESCRIPTION]
```

## Step 10: Practical Workflow Example
**Complete example of implementing a new phase:**

```bash
# 1. Start from staging branch
git checkout staging
git pull origin staging

# 2. Create feature branch for next phase
git checkout -b feature/phase-11-study-interfaces

# 3. Create/update PROGRESS.md
# (Document current phase work)

# 4. Implement the phase
# (Create task file, implement code, etc.)

# 5. Make atomic commits
git add chewyai_spec/11_study_interfaces.md
git commit -m "feat(phase-11): add study interfaces task specification

- Define flashcard study interface components
- Include quiz interface with multiple question types
- Add keyboard navigation and progress tracking
- Specify responsive design requirements

Refs: Phase 11 implementation"

# 6. Push feature branch
git push origin feature/phase-11-study-interfaces

# 7. Create PR to staging via GitHub API
# (Target: staging branch, include detailed description)

# 8. After PR approval and merge, cleanup
git checkout staging
git pull origin staging
git branch -d feature/phase-11-study-interfaces
git push origin --delete feature/phase-11-study-interfaces
```

## Step 11: Repository Status
**Current State of `Chewy42/chewy-ai-one-shot`:**
- ✅ Main branch (protected, requires PR reviews)
- ✅ Staging branch (protected, requires PR reviews)
- ✅ Branch protection rules configured
- ✅ Ready for feature branch workflow

## Step 12: Identify Next Actions
**After reviewing all documentation and setting up Git workflow:**

1. **Determine the current implementation phase**
2. **Create appropriate feature branch from staging**
3. **Update PROGRESS.md with current work**
4. **Identify any gaps or incomplete work**
5. **Continue with the next logical task file creation**
6. **Maintain the established patterns and quality standards**
7. **Follow Git workflow for all changes**
8. **Create PRs for code review and integration**

## Important Notes
- **Always read the actual files** - don't assume content based on filenames
- **Check for any PROGRESS.md** files that might indicate ongoing work
- **Verify Supabase and Stripe MCP servers are available** for testing
- **Follow the established task file format** for consistency
- **Each task file should be self-contained** with complete implementations
- **Never commit directly to main or staging** - always use feature branches
- **Create atomic, well-documented commits**
- **Use GitHub API tools** for branch management and PR creation
- **MAINTAIN AI DOCS ACCURACY** - Update specifications when implementation changes
- **SYNC TYPES AND FEATURES** - Keep TypeScript types and feature docs aligned with code

## Pricing Model Updates (CRITICAL)
**UPDATED CREDIT SYSTEM**: The pricing model has been updated to a consistent 1:5 credit-to-generation ratio:

### New Pricing Structure:
- **1 credit = 5 generated items** (flashcards, quiz questions, etc.)
- **10 credits = 50 generated items**
- **Default Flashcard Generation**: 50 flashcards per generation (10 credits)
- **Generation Approximation**: Number of flashcards generated is approximated since the LLM may generate a few more or less cards than requested
- **Consistent across all generation types**: Flashcards, quiz questions, document processing, custom prompts

### Implementation Requirements:
- **Update all credit cost calculations** in backend services
- **Modify frontend pricing displays** to show the 1:5 ratio clearly
- **Update database operation costs** in ai_operation_costs table
- **Ensure credit validation** uses the new ratios
- **Update user-facing documentation** and help text

### Files Requiring Updates:
- `backend/src/services/creditService.ts` - Update cost calculations
- `ai_docs/chewyai_spec/06_credit_system.md` - Update pricing specifications
- `ai_docs/chewyai_docs/ChewyAI_Complete_PRD_v3.md` - Update business model section
- Frontend components displaying credit costs and generation limits

## Context Verification Checklist
Before continuing development, confirm you understand:
- [ ] Project scope and business model from PRD
- [ ] Current implementation phase from task files
- [ ] Technical stack and architecture decisions
- [ ] Established coding patterns and standards
- [ ] **Updated pricing model**: 1 credit = 5 generated items (consistent 1:5 ratio)
- [ ] **Credit system requirements**: All generation types follow the same pricing structure
- [ ] Next logical development steps
- [ ] Available MCP servers (Supabase, Stripe) for testing
- [ ] Git workflow and branch structure
- [ ] PR process and review requirements
- [ ] **AI docs synchronization requirements** - specs must match implementation
- [ ] **Documentation update responsibilities** - maintain accuracy throughout development

## Ready to Continue
Once you've completed the context priming steps above, you should be fully prepared to continue ChewyAI development from exactly where the previous session left off, maintaining consistency, quality standards, and proper Git workflow practices.
