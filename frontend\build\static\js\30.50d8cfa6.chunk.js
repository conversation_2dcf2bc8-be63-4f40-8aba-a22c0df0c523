"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[30],{1759:(e,s,a)=>{a.d(s,{B:()=>i});a(9643);var t=a(9391),r=a(6507);const n=[{value:t.Cr.EASY,label:"Easy",description:"Basic facts and definitions"},{value:t.Cr.MEDIUM,label:"Medium",description:"Moderate understanding required"},{value:t.Cr.HARD,label:"Hard",description:"Deep analysis and critical thinking"},{value:t.Cr.COLLEGE,label:"College",description:"Undergraduate level complexity"},{value:t.Cr.GRADUATE,label:"Graduate",description:"Advanced graduate study"},{value:t.Cr.PHD,label:"PhD",description:"Research-level expertise"}],i=e=>{let{value:s,onChange:a,className:i="",disabled:l=!1,label:c="Difficulty Level"}=e;return(0,r.jsxs)("div",{className:"space-y-3 ".concat(i),children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-text-primary",children:c}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:n.map(e=>{const n=s===e.value,i=n?(e=>{switch(e){case t.Cr.EASY:return"bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20";case t.Cr.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case t.Cr.HARD:return"bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20";case t.Cr.COLLEGE:return"bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20";case t.Cr.GRADUATE:return"bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20";case t.Cr.PHD:return"bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}})(e.value):(e=>{switch(e){case t.Cr.EASY:return"bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50";case t.Cr.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case t.Cr.HARD:return"bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50";case t.Cr.COLLEGE:return"bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50";case t.Cr.GRADUATE:return"bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50";case t.Cr.PHD:return"bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}})(e.value);return(0,r.jsxs)("button",{type:"button",onClick:()=>!l&&a(e.value),disabled:l,className:"\n                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu\n                ".concat(i,"\n                ").concat(l?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105","\n                ").concat(n?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":"","\n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary\n              "),title:e.description,"aria-pressed":n,children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"font-semibold",children:e.label}),(0,r.jsx)("div",{className:"text-xs mt-1 ".concat(n?"text-white/90":"text-text-secondary"),children:e.description})]}),n&&(0,r.jsx)("div",{className:"absolute top-2 right-2",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},e.value)})}),(0,r.jsx)("p",{className:"text-xs text-text-muted",children:"Select the appropriate difficulty level for your flashcards. This affects the complexity of questions and answers generated."})]})}},5030:(e,s,a)=>{a.r(s),a.d(s,{SettingsPage:()=>N});var t=a(8957),r=a(9643),n=a(8002),i=a(344),l=a(4859),c=a(467),o=a(2086),d=a(6507);const m=[{id:"free",name:"Study Starter",price:0,interval:"month",features:["500 AI study generations per month","Basic flashcards and quizzes","Up to 5 document uploads","Basic study analytics","Perfect for trying out ChewyAI"]},{id:"pro_monthly",name:"Scholar Pro",price:9.99,interval:"month",popular:!0,features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Perfect for serious students"]},{id:"pro_yearly",name:"Academic Year Pass",price:99.99,interval:"year",features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Save $20 vs monthly (2 months free!)","Perfect for the full academic year"]}],x=()=>{var e,s;const[a,t]=(0,r.useState)(null),[c,o]=(0,r.useState)(!0),[x,u]=(0,r.useState)(!1),[h,p]=(0,r.useState)(null);(0,r.useEffect)(()=>{g()},[]);const g=async()=>{o(!0),p(null);try{const e=localStorage.getItem("auth_token"),s=await fetch("/api/subscription",{headers:{Authorization:"Bearer ".concat(e)}});if(!s.ok)throw new Error("Failed to fetch subscription data");const a=await s.json();if(!a.success)throw new Error(a.error);t(a.data)}catch(e){p(e instanceof Error?e.message:"Failed to load subscription data"),t({currentPlan:m[0],status:"active",nextBillingDate:new Date(Date.now()+2592e6).toISOString()})}finally{o(!1)}};return c?(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,d.jsx)("div",{className:"h-64 bg-gray-600 rounded-lg"},e))})]})}):(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Subscription Management"}),h&&(0,d.jsxs)("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.TMu,{className:"w-5 h-5 text-red-400"}),(0,d.jsx)("span",{className:"text-red-400 font-medium",children:"Error"})]}),(0,d.jsx)("p",{className:"text-red-300 mt-1",children:h})]}),a&&(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-medium text-white",children:"Current Plan"}),(0,d.jsx)("p",{className:"text-gray-400",children:(null===(e=a.currentPlan)||void 0===e?void 0:e.name)||"No active plan"})]}),(0,d.jsx)("div",{className:"text-right",children:(0,d.jsx)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat("active"===a.status?"bg-green-500/20 text-green-400":"canceled"===a.status?"bg-red-500/20 text-red-400":"past_due"===a.status?"bg-yellow-500/20 text-yellow-400":"bg-blue-500/20 text-blue-400"),children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})})]}),a.nextBillingDate&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400 text-sm",children:[(0,d.jsx)(i._AI,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:a.cancelAtPeriodEnd?"Access ends on ".concat(new Date(a.nextBillingDate).toLocaleDateString()):"Next billing date: ".concat(new Date(a.nextBillingDate).toLocaleDateString())})]}),"free"!==(null===(s=a.currentPlan)||void 0===s?void 0:s.id)&&(0,d.jsxs)("div",{className:"mt-4 flex space-x-3",children:[a.cancelAtPeriodEnd?(0,d.jsx)(l.$,{onClick:async()=>{u(!0),p(null);try{const e=localStorage.getItem("auth_token"),s=await fetch("/api/subscription/reactivate",{method:"POST",headers:{Authorization:"Bearer ".concat(e)}});if(!s.ok)throw new Error("Failed to reactivate subscription");const a=await s.json();if(!a.success)throw new Error(a.error);await g()}catch(e){p(e instanceof Error?e.message:"Failed to reactivate subscription")}finally{u(!1)}},isLoading:x,variant:"primary",size:"sm",children:"Reactivate Subscription"}):(0,d.jsx)(l.$,{onClick:async()=>{if(confirm("Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.")){u(!0),p(null);try{const e=localStorage.getItem("auth_token"),s=await fetch("/api/subscription/cancel",{method:"POST",headers:{Authorization:"Bearer ".concat(e)}});if(!s.ok)throw new Error("Failed to cancel subscription");const a=await s.json();if(!a.success)throw new Error(a.error);await g()}catch(e){p(e instanceof Error?e.message:"Failed to cancel subscription")}finally{u(!1)}}},isLoading:x,variant:"danger",size:"sm",children:"Cancel Subscription"}),(0,d.jsxs)(l.$,{onClick:g,variant:"secondary",size:"sm",children:[(0,d.jsx)(i.pMz,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-medium text-white mb-4",children:"Available Plans"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:m.map(e=>{var s;const t=(null===a||void 0===a||null===(s=a.currentPlan)||void 0===s?void 0:s.id)===e.id;return(0,d.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ".concat(e.popular?"border-primary-500 ring-2 ring-primary-500/20":t?"border-green-500 ring-2 ring-green-500/20":"border-border-primary hover:border-gray-500"),children:[e.popular&&(0,d.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:(0,d.jsxs)("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[(0,d.jsx)(i.K7t,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:"Most Popular"})]})}),t&&(0,d.jsx)("div",{className:"absolute -top-3 right-4",children:(0,d.jsxs)("div",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[(0,d.jsx)(i.q9z,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:"Current"})]})}),(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("h5",{className:"text-xl font-semibold text-white mb-2",children:e.name}),(0,d.jsxs)("div",{className:"text-3xl font-bold text-white",children:["$",e.price,(0,d.jsxs)("span",{className:"text-lg text-gray-400",children:["/",e.interval]})]})]}),(0,d.jsx)("ul",{className:"space-y-3 mb-6",children:e.features.map((e,s)=>(0,d.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.q9z,{className:"w-4 h-4 text-green-400 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-gray-300 text-sm",children:e})]},s))}),(0,d.jsx)(l.$,{onClick:()=>(async e=>{u(!0),p(null);try{const s=localStorage.getItem("auth_token"),a=await fetch("/api/subscription/change",{method:"POST",headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"},body:JSON.stringify({planId:e})});if(!a.ok)throw new Error("Failed to change subscription plan");const t=await a.json();if(!t.success)throw new Error(t.error);await g()}catch(s){p(s instanceof Error?s.message:"Failed to change subscription plan")}finally{u(!1)}})(e.id),disabled:t||x,isLoading:x,variant:e.popular?"primary":"secondary",className:"w-full",children:t?"Current Plan":"Switch to ".concat(e.name)})]},e.id)})})]})]})})},u=()=>{const[e,s]=(0,r.useState)(!1),[a,n]=(0,r.useState)(!1),[c,o]=(0,r.useState)(null),[m,x]=(0,r.useState)(null),[u,h]=(0,r.useState)({studySets:12,flashcards:245,quizzes:18,documents:8,totalSize:"2.4 MB"}),[p,g]=(0,r.useState)({studySets:!0,flashcards:!0,quizzes:!0,analytics:!0,preferences:!0}),y=async e=>{if(confirm("Are you sure you want to clear all ".concat(e,"? This action cannot be undone."))){n(!0),o(null),x(null);try{const s=localStorage.getItem("auth_token"),a=await fetch("/api/data/clear/".concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(s)}});if(!a.ok)throw new Error("Failed to clear ".concat(e));const t=await a.json();if(!t.success)throw new Error(t.error);x("".concat(e," cleared successfully!")),await b()}catch(s){o(s instanceof Error?s.message:"Failed to clear ".concat(e))}finally{n(!1)}}},b=async()=>{try{const e=localStorage.getItem("auth_token"),s=await fetch("/api/data/stats",{headers:{Authorization:"Bearer ".concat(e)}});if(s.ok){const e=await s.json();e.success&&h(e.data)}}catch(e){}};return(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Data Management"}),c&&(0,d.jsxs)("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.TMu,{className:"w-5 h-5 text-red-400"}),(0,d.jsx)("span",{className:"text-red-400 font-medium",children:"Error"})]}),(0,d.jsx)("p",{className:"text-red-300 mt-1",children:c})]}),m&&(0,d.jsxs)("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.OLr,{className:"w-5 h-5 text-green-400"}),(0,d.jsx)("span",{className:"text-green-400 font-medium",children:"Success"})]}),(0,d.jsx)("p",{className:"text-green-300 mt-1",children:m})]}),(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h4",{className:"text-lg font-medium text-white",children:"Your Data Overview"}),(0,d.jsxs)(l.$,{onClick:b,variant:"secondary",size:"sm",children:[(0,d.jsx)(i.pMz,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary-400",children:u.studySets}),(0,d.jsx)("div",{className:"text-sm text-gray-400",children:"Study Sets"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary-400",children:u.flashcards}),(0,d.jsx)("div",{className:"text-sm text-gray-400",children:"Flashcards"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary-400",children:u.quizzes}),(0,d.jsx)("div",{className:"text-sm text-gray-400",children:"Quizzes"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary-400",children:u.documents}),(0,d.jsx)("div",{className:"text-sm text-gray-400",children:"Documents"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary-400",children:u.totalSize}),(0,d.jsx)("div",{className:"text-sm text-gray-400",children:"Total Size"})]})]})]}),(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)(i.cIQ,{className:"w-6 h-6 text-blue-400"}),(0,d.jsx)("h4",{className:"text-lg font-medium text-white",children:"Export Your Data"})]}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Download a copy of your data in JSON format. You can use this to backup your data or import it into another account."}),(0,d.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,d.jsx)("h5",{className:"font-medium text-white",children:"Select data to export:"}),Object.entries(p).map(e=>{let[s,a]=e;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:"text-sm text-gray-300 capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),(0,d.jsx)("button",{onClick:()=>g((0,t.A)((0,t.A)({},p),{},{[s]:!a})),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(a?"bg-primary-500":"bg-gray-600"),children:(0,d.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(a?"translate-x-6":"translate-x-1")})})]},s)})]}),(0,d.jsxs)(l.$,{onClick:async()=>{s(!0),o(null),x(null);try{const e=localStorage.getItem("auth_token"),s=await fetch("/api/data/export",{method:"POST",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({exportData:p})});if(!s.ok)throw new Error("Failed to export data");const a=await s.blob(),t=window.URL.createObjectURL(a),r=document.createElement("a");r.href=t,r.download="chewyai-data-export-".concat((new Date).toISOString().split("T")[0],".json"),r.click(),window.URL.revokeObjectURL(t),x("Data exported successfully!")}catch(e){o(e instanceof Error?e.message:"Failed to export data")}finally{s(!1)}},isLoading:e,variant:"primary",children:[(0,d.jsx)(i.cIQ,{className:"w-4 h-4 mr-2"}),"Export Data"]})]}),(0,d.jsxs)("div",{className:"bg-red-500/10 rounded-lg p-6 border border-red-500/30",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)(i.WWR,{className:"w-6 h-6 text-red-400"}),(0,d.jsx)("h4",{className:"text-lg font-medium text-white",children:"Clear Data"})]}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-6",children:"Permanently delete specific types of data from your account. This action cannot be undone."}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(l.$,{onClick:()=>y("study-sets"),isLoading:a,variant:"danger",size:"sm",className:"w-full",children:"Clear All Study Sets"}),(0,d.jsx)(l.$,{onClick:()=>y("flashcards"),isLoading:a,variant:"danger",size:"sm",className:"w-full",children:"Clear All Flashcards"})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(l.$,{onClick:()=>y("quizzes"),isLoading:a,variant:"danger",size:"sm",className:"w-full",children:"Clear All Quizzes"}),(0,d.jsx)(l.$,{onClick:()=>y("analytics"),isLoading:a,variant:"danger",size:"sm",className:"w-full",children:"Clear Analytics Data"})]})]})]})]})})},h=()=>{const[e,s]=(0,r.useState)(null),[a,t]=(0,r.useState)(!0),[c,o]=(0,r.useState)(!1),[m,x]=(0,r.useState)(null);(0,r.useEffect)(()=>{u()},[]);const u=async()=>{t(!0),x(null);try{const e=localStorage.getItem("auth_token"),a=await fetch("/api/billing",{headers:{Authorization:"Bearer ".concat(e)}});if(!a.ok)throw new Error("Failed to fetch billing data");const t=await a.json();if(!t.success)throw new Error(t.error);s(t.data)}catch(e){x(e instanceof Error?e.message:"Failed to load billing data"),s({paymentMethods:[{id:"1",type:"card",last4:"4242",brand:"visa",expiryMonth:12,expiryYear:2025,isDefault:!0}],invoices:[{id:"1",number:"INV-001",amount:9.99,currency:"USD",status:"paid",date:new Date(Date.now()-2592e6).toISOString(),description:"Pro Plan - Monthly"},{id:"2",number:"INV-002",amount:9.99,currency:"USD",status:"pending",date:(new Date).toISOString(),dueDate:new Date(Date.now()+6048e5).toISOString(),description:"Pro Plan - Monthly"}],nextInvoice:{amount:9.99,currency:"USD",date:new Date(Date.now()+2592e6).toISOString()}})}finally{t(!1)}},h=e=>{switch(e){case"paid":return(0,d.jsx)(i.OLr,{className:"w-5 h-5 text-green-400"});case"pending":return(0,d.jsx)(i.mqD,{className:"w-5 h-5 text-yellow-400"});case"failed":return(0,d.jsx)(i.U_s,{className:"w-5 h-5 text-red-400"});default:return(0,d.jsx)(i.mqD,{className:"w-5 h-5 text-gray-400"})}},p=e=>{switch(e){case"paid":return"text-green-400";case"pending":return"text-yellow-400";case"failed":return"text-red-400";default:return"text-gray-400"}};return a?(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"h-32 bg-gray-600 rounded-lg"}),(0,d.jsx)("div",{className:"h-64 bg-gray-600 rounded-lg"})]})]})}):(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Billing & Invoices"}),(0,d.jsxs)(l.$,{onClick:u,variant:"secondary",size:"sm",children:[(0,d.jsx)(i.pMz,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),m&&(0,d.jsxs)("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.TMu,{className:"w-5 h-5 text-red-400"}),(0,d.jsx)("span",{className:"text-red-400 font-medium",children:"Error"})]}),(0,d.jsx)("p",{className:"text-red-300 mt-1",children:m})]}),(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h4",{className:"text-lg font-medium text-white",children:"Payment Methods"}),(0,d.jsx)(l.$,{onClick:async()=>{x("Payment method management coming soon")},variant:"secondary",size:"sm",children:"Add Payment Method"})]}),0===(null===e||void 0===e?void 0:e.paymentMethods.length)?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(i.XtC,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-400",children:"No payment methods added"})]}):(0,d.jsx)("div",{className:"space-y-3",children:null===e||void 0===e?void 0:e.paymentMethods.map(e=>{var s;return(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.XtC,{className:"w-6 h-6 text-gray-400"}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("span",{className:"text-white font-medium",children:[null===(s=e.brand)||void 0===s?void 0:s.toUpperCase()," \u2022\u2022\u2022\u2022 ",e.last4]}),e.isDefault&&(0,d.jsx)("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium",children:"Default"})]}),e.expiryMonth&&e.expiryYear&&(0,d.jsxs)("p",{className:"text-gray-400 text-sm",children:["Expires ",e.expiryMonth.toString().padStart(2,"0"),"/",e.expiryYear]})]})]}),!e.isDefault&&(0,d.jsx)(l.$,{onClick:()=>(async e=>{o(!0),x(null);try{const s=localStorage.getItem("auth_token"),a=await fetch("/api/billing/payment-methods/default",{method:"POST",headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:e})});if(!a.ok)throw new Error("Failed to update default payment method");const t=await a.json();if(!t.success)throw new Error(t.error);await u()}catch(s){x(s instanceof Error?s.message:"Failed to update payment method")}finally{o(!1)}})(e.id),isLoading:c,variant:"secondary",size:"sm",children:"Set as Default"})]},e.id)})})]}),(null===e||void 0===e?void 0:e.nextInvoice)&&(0,d.jsxs)("div",{className:"bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6",children:[(0,d.jsx)("h4",{className:"text-lg font-medium text-white mb-2",children:"Upcoming Invoice"}),(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"text-blue-300",children:["$",e.nextInvoice.amount," ",e.nextInvoice.currency.toUpperCase()]}),(0,d.jsxs)("p",{className:"text-blue-400 text-sm",children:["Due on ",new Date(e.nextInvoice.date).toLocaleDateString()]})]})})]}),(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[(0,d.jsx)("h4",{className:"text-lg font-medium text-white mb-4",children:"Invoice History"}),0===(null===e||void 0===e?void 0:e.invoices.length)?(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-gray-400",children:"No invoices found"})}):(0,d.jsx)("div",{className:"space-y-3",children:null===e||void 0===e?void 0:e.invoices.map(e=>(0,d.jsxs)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[h(e.status),(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-white font-medium",children:e.number}),(0,d.jsx)("span",{className:"text-sm font-medium ".concat(p(e.status)),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:e.description}),(0,d.jsxs)("p",{className:"text-gray-500 text-xs",children:[new Date(e.date).toLocaleDateString(),e.dueDate&&"pending"===e.status&&(0,d.jsxs)("span",{children:[" \u2022 Due ",new Date(e.dueDate).toLocaleDateString()]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("span",{className:"text-white font-medium",children:["$",e.amount," ",e.currency.toUpperCase()]}),"paid"===e.status&&(0,d.jsxs)(l.$,{onClick:()=>(async e=>{try{const s=localStorage.getItem("auth_token"),a=await fetch("/api/billing/invoices/".concat(e,"/download"),{headers:{Authorization:"Bearer ".concat(s)}});if(!a.ok)throw new Error("Failed to download invoice");const t=await a.blob(),r=window.URL.createObjectURL(t),n=document.createElement("a");n.href=r,n.download="invoice-".concat(e,".pdf"),n.click(),window.URL.revokeObjectURL(r)}catch(s){x(s instanceof Error?s.message:"Failed to download invoice")}})(e.id),variant:"secondary",size:"sm",children:[(0,d.jsx)(i.cIQ,{className:"w-4 h-4 mr-2"}),"Download"]})]})]},e.id))})]})]})})},p=e=>{let{enabled:s,onToggle:t}=e;const[n,o]=(0,r.useState)(!1),[m,x]=(0,r.useState)(!1),[u,h]=(0,r.useState)(null),[p,g]=(0,r.useState)(null),[y,b]=(0,r.useState)(null),[f,j]=(0,r.useState)(""),[v,N]=(0,r.useState)(!1),[w,S]=(0,r.useState)(!1),A=async()=>{if(f&&6===f.length){x(!0),h(null);try{const{createClient:e}=await a.e(383).then(a.bind(a,383)),s=e({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3001"}.REACT_APP_SUPABASE_URL,{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3001"}.REACT_APP_SUPABASE_ANON_KEY);if(!y)throw new Error("No setup data available");const{data:r,error:n}=await s.auth.mfa.challenge({factorId:y.factorId});if(n)throw new Error(n.message);const{error:i}=await s.auth.mfa.verify({factorId:y.factorId,challengeId:r.id,code:f});if(i)throw new Error(i.message);g("Two-factor authentication enabled successfully!"),o(!1),S(!0),t(!0)}catch(e){h(e instanceof Error?e.message:"Failed to verify 2FA code")}finally{x(!1)}}else h("Please enter a valid 6-digit code")},C=e=>{navigator.clipboard.writeText(e),g("Copied to clipboard!"),setTimeout(()=>g(null),2e3)},E=()=>{o(!1),b(null),j(""),h(null),g(null)};return n&&y?(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,d.jsx)(i.nfp,{className:"w-6 h-6 text-primary-400"}),(0,d.jsx)("h4",{className:"text-lg font-medium text-white",children:"Setup Two-Factor Authentication"})]}),u&&(0,d.jsx)("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.TMu,{className:"w-4 h-4 text-red-400"}),(0,d.jsx)("span",{className:"text-red-400 text-sm",children:u})]})}),p&&(0,d.jsx)("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.OLr,{className:"w-4 h-4 text-green-400"}),(0,d.jsx)("span",{className:"text-green-400 text-sm",children:p})]})}),w?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(i.OLr,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),(0,d.jsx)("h5",{className:"text-lg font-medium text-white mb-2",children:"2FA Enabled Successfully!"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:"Your account is now protected with two-factor authentication."})]}),(0,d.jsxs)("div",{className:"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4",children:[(0,d.jsx)("h5",{className:"font-medium text-yellow-400 mb-2",children:"Important: Save Your Backup Codes"}),(0,d.jsx)("p",{className:"text-yellow-300 text-sm mb-4",children:"Store these backup codes in a safe place. You can use them to access your account if you lose your authenticator device."}),(0,d.jsx)("div",{className:"grid grid-cols-2 gap-2 mb-4",children:y.backupCodes.map((e,s)=>(0,d.jsx)("div",{className:"bg-background-secondary border border-border-primary rounded p-2 text-center",children:(0,d.jsx)("code",{className:"text-primary-400 font-mono",children:e})},s))}),(0,d.jsxs)(l.$,{onClick:()=>C(y.backupCodes.join("\n")),variant:"secondary",size:"sm",children:[(0,d.jsx)(i.c$c,{className:"w-4 h-4 mr-2"}),"Copy All Codes"]})]}),(0,d.jsx)(l.$,{onClick:()=>S(!1),variant:"primary",className:"w-full",children:"I've Saved My Backup Codes"})]}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h5",{className:"font-medium text-white mb-2",children:"Step 1: Scan QR Code"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)"}),(0,d.jsx)("div",{className:"bg-white p-4 rounded-lg inline-block",children:(0,d.jsx)("img",{src:y.qrCode,alt:"2FA QR Code",className:"w-48 h-48"})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-white mb-2",children:"Step 2: Manual Entry (Alternative)"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-3",children:"If you can't scan the QR code, enter this secret key manually:"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"flex-1 bg-background-secondary border border-border-primary rounded-lg p-3",children:(0,d.jsx)("code",{className:"text-primary-400 font-mono",children:v?y.secret:"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"})}),(0,d.jsx)(l.$,{onClick:()=>N(!v),variant:"secondary",size:"sm",children:v?(0,d.jsx)(i.QLA,{className:"w-4 h-4"}):(0,d.jsx)(i.zeF,{className:"w-4 h-4"})}),(0,d.jsx)(l.$,{onClick:()=>C(y.secret),variant:"secondary",size:"sm",children:(0,d.jsx)(i.c$c,{className:"w-4 h-4"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-white mb-2",children:"Step 3: Verify Setup"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-3",children:"Enter the 6-digit code from your authenticator app:"}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)(c.p,{value:f,onChange:j,placeholder:"123456",className:"flex-1"}),(0,d.jsx)(l.$,{onClick:A,isLoading:m,disabled:6!==f.length,children:"Verify"})]})]}),(0,d.jsx)("div",{className:"flex space-x-3",children:(0,d.jsx)(l.$,{onClick:E,variant:"secondary",className:"flex-1",children:"Cancel"})})]})]}):(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.nfp,{className:"w-6 h-6 ".concat(s?"text-green-400":"text-gray-400")}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-medium text-white",children:"Two-Factor Authentication"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]})]}),(0,d.jsx)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(s?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"),children:s?"Enabled":"Disabled"})]}),u&&(0,d.jsx)("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.TMu,{className:"w-4 h-4 text-red-400"}),(0,d.jsx)("span",{className:"text-red-400 text-sm",children:u})]})}),p&&(0,d.jsx)("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.OLr,{className:"w-4 h-4 text-green-400"}),(0,d.jsx)("span",{className:"text-green-400 text-sm",children:p})]})}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-gray-300 text-sm",children:s?"Two-factor authentication is currently enabled for your account. You can disable it below if needed.":"Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy."}),(0,d.jsx)("div",{className:"flex space-x-3",children:s?(0,d.jsx)(l.$,{onClick:async()=>{if(confirm("Are you sure you want to disable two-factor authentication? This will make your account less secure.")){x(!0),h(null);try{const{createClient:e}=await a.e(383).then(a.bind(a,383)),s=e({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3001"}.REACT_APP_SUPABASE_URL,{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3001"}.REACT_APP_SUPABASE_ANON_KEY),{data:r,error:n}=await s.auth.mfa.listFactors();if(n)throw new Error(n.message);for(const a of r.totp){const{error:e}=await s.auth.mfa.unenroll({factorId:a.id});e&&console.error("Failed to unenroll factor:",e)}g("Two-factor authentication disabled successfully"),t(!1)}catch(e){h(e instanceof Error?e.message:"Failed to disable 2FA")}finally{x(!1)}}},isLoading:m,variant:"danger",children:"Disable 2FA"}):(0,d.jsxs)(l.$,{onClick:async()=>{x(!0),h(null),g(null);try{const{createClient:e}=await a.e(383).then(a.bind(a,383)),s=e({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3001"}.REACT_APP_SUPABASE_URL,{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:3001"}.REACT_APP_SUPABASE_ANON_KEY),{data:t,error:r}=await s.auth.mfa.enroll({factorType:"totp",friendlyName:"ChewyAI Authenticator"});if(r)throw new Error(r.message);b({qrCode:t.totp.qr_code,secret:t.totp.secret,backupCodes:[],factorId:t.id}),o(!0)}catch(e){h(e instanceof Error?e.message:"Failed to setup 2FA"),b({qrCode:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",secret:"JBSWY3DPEHPK3PXP",factorId:"mock-factor-id",backupCodes:["12345678","87654321","11111111","22222222","33333333","44444444","55555555","66666666"]}),o(!0)}finally{x(!1)}},isLoading:m,variant:"primary",children:[(0,d.jsx)(i.tWn,{className:"w-4 h-4 mr-2"}),"Enable 2FA"]})})]})]})},g=e=>{let{isOpen:s,onClose:a,onSuccess:n}=e;const[o,m]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[x,u]=(0,r.useState)({current:!1,new:!1,confirm:!1}),[h,p]=(0,r.useState)(!1),[g,y]=(0,r.useState)(null),[b,f]=(0,r.useState)(null),j=e=>s=>{((e,s)=>{m(a=>(0,t.A)((0,t.A)({},a),{},{[e]:s})),y(null)})(e,s)},v=e=>{u(s=>(0,t.A)((0,t.A)({},s),{},{[e]:!s[e]}))},N=()=>{m({currentPassword:"",newPassword:"",confirmPassword:""}),u({current:!1,new:!1,confirm:!1}),y(null),f(null)},w=()=>{N(),a()};return s?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-background-secondary rounded-lg border border-border-primary w-full max-w-md",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-border-primary",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.tWn,{className:"w-5 h-5 text-primary-400"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Change Password"})]}),(0,d.jsx)("button",{onClick:w,className:"text-gray-400 hover:text-white transition-colors",children:(0,d.jsx)(i.U_s,{className:"w-5 h-5"})})]}),(0,d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),o.currentPassword?o.newPassword?o.newPassword.length<8?(y("New password must be at least 8 characters long"),0):o.newPassword!==o.confirmPassword?(y("New passwords do not match"),0):o.currentPassword!==o.newPassword||(y("New password must be different from current password"),0):(y("New password is required"),0):(y("Current password is required"),0)){p(!0),y(null),f(null);try{const e=localStorage.getItem("auth_token"),s=await fetch("/api/auth/change-password",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({currentPassword:o.currentPassword,newPassword:o.newPassword})}),t=await s.json();if(!s.ok)throw new Error(t.error||"Failed to change password");f("Password changed successfully!"),setTimeout(()=>{n(),a(),N()},1500)}catch(s){y(s instanceof Error?s.message:"Failed to change password")}finally{p(!1)}}},className:"p-6 space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Current Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(c.p,{type:x.current?"text":"password",value:o.currentPassword,onChange:j("currentPassword"),placeholder:"Enter your current password",className:"pr-10",disabled:h}),(0,d.jsx)("button",{type:"button",onClick:()=>v("current"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:x.current?(0,d.jsx)(i.QLA,{className:"w-4 h-4"}):(0,d.jsx)(i.zeF,{className:"w-4 h-4"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(c.p,{type:x.new?"text":"password",value:o.newPassword,onChange:j("newPassword"),placeholder:"Enter your new password",className:"pr-10",disabled:h}),(0,d.jsx)("button",{type:"button",onClick:()=>v("new"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:x.new?(0,d.jsx)(i.QLA,{className:"w-4 h-4"}):(0,d.jsx)(i.zeF,{className:"w-4 h-4"})})]}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Password must be at least 8 characters long"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Confirm New Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(c.p,{type:x.confirm?"text":"password",value:o.confirmPassword,onChange:j("confirmPassword"),placeholder:"Confirm your new password",className:"pr-10",disabled:h}),(0,d.jsx)("button",{type:"button",onClick:()=>v("confirm"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:x.confirm?(0,d.jsx)(i.QLA,{className:"w-4 h-4"}):(0,d.jsx)(i.zeF,{className:"w-4 h-4"})})]})]}),g&&(0,d.jsx)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:(0,d.jsx)("p",{className:"text-red-400 text-sm",children:g})}),b&&(0,d.jsx)("div",{className:"bg-green-500/10 border border-green-500/30 rounded-lg p-3",children:(0,d.jsx)("p",{className:"text-green-400 text-sm",children:b})}),(0,d.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,d.jsx)(l.$,{type:"button",variant:"secondary",onClick:w,disabled:h,className:"flex-1",children:"Cancel"}),(0,d.jsx)(l.$,{type:"submit",variant:"primary",isLoading:h,disabled:h,className:"flex-1",children:h?"Changing...":"Change Password"})]})]})]})}):null},y=e=>{let{isOpen:s,onClose:a,action:t}=e;const[n,o]=(0,r.useState)(""),[m,x]=(0,r.useState)(!1),[u,h]=(0,r.useState)(null),[p,g]=(0,r.useState)("confirm"),y={deactivate:{title:"Deactivate Account",description:"Your account will be temporarily disabled. You can reactivate it by logging in again.",confirmText:"DEACTIVATE",buttonText:"Deactivate Account",warningText:"This will temporarily disable your account and log you out.",endpoint:"/api/auth/deactivate-account"},delete:{title:"Delete Account",description:"This will permanently delete your account and all associated data. This action cannot be undone.",confirmText:"DELETE FOREVER",buttonText:"Delete Account Forever",warningText:"This will permanently delete all your data including study sets, flashcards, progress, and subscription information.",endpoint:"/api/auth/delete-account"}}[t],b=n===y.confirmText,f=async()=>{if("confirm"!==p)if(b){x(!0),h(null);try{const e=localStorage.getItem("auth_token"),s=await fetch(y.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({confirmation:n})}),a=await s.json();if(!s.ok)throw new Error(a.error||"Failed to ".concat(t," account"));localStorage.removeItem("auth_token"),localStorage.removeItem("user_data"),window.location.href="/login"}catch(e){h(e instanceof Error?e.message:"Failed to ".concat(t," account"))}finally{x(!1)}}else h('Please type "'.concat(y.confirmText,'" to confirm'));else g("final")},j=()=>{o(""),h(null),g("confirm"),a()};return s?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-background-secondary rounded-lg border border-red-500/30 w-full max-w-md",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-red-500/30",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.TMu,{className:"w-5 h-5 text-red-400"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-white",children:y.title})]}),(0,d.jsx)("button",{onClick:j,className:"text-gray-400 hover:text-white transition-colors",children:(0,d.jsx)(i.U_s,{className:"w-5 h-5"})})]}),(0,d.jsx)("div",{className:"p-6",children:"confirm"===p?(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(i.TMu,{className:"w-5 h-5 text-red-400 mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-red-400 mb-2",children:"Warning"}),(0,d.jsx)("p",{className:"text-gray-300 text-sm",children:y.warningText})]})]})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-gray-300 text-sm mb-4",children:y.description}),"delete"===t&&(0,d.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,d.jsx)("p",{children:"This will delete:"}),(0,d.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,d.jsx)("li",{children:"All study sets and flashcards"}),(0,d.jsx)("li",{children:"Quiz history and progress"}),(0,d.jsx)("li",{children:"Account settings and preferences"}),(0,d.jsx)("li",{children:"Subscription and billing information"}),(0,d.jsx)("li",{children:"All uploaded documents"})]})]})]}),(0,d.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,d.jsx)(l.$,{variant:"secondary",onClick:j,className:"flex-1",children:"Cancel"}),(0,d.jsx)(l.$,{variant:"danger",onClick:f,className:"flex-1",children:"Continue"})]})]}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.WWR,{className:"w-5 h-5 text-red-400"}),(0,d.jsx)("h4",{className:"font-medium text-red-400",children:"Final Confirmation"})]})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"text-gray-300 text-sm mb-4",children:["To confirm this action, please type ",(0,d.jsx)("span",{className:"font-mono font-bold text-red-400",children:y.confirmText})," in the box below:"]}),(0,d.jsx)(c.p,{type:"text",value:n,onChange:e=>{o(e),h(null)},placeholder:y.confirmText,className:"font-mono",disabled:m})]}),u&&(0,d.jsx)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:(0,d.jsx)("p",{className:"text-red-400 text-sm",children:u})}),(0,d.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,d.jsx)(l.$,{variant:"secondary",onClick:()=>{g("confirm"),h(null)},disabled:m,className:"flex-1",children:"Back"}),(0,d.jsx)(l.$,{variant:"danger",onClick:f,isLoading:m,disabled:m||!b,className:"flex-1",children:m?"Processing...":y.buttonText})]})]})})]})}):null};var b=a(9391),f=a(1759),j=a(7192);const v=[{id:"profile",label:"Profile",icon:i.XPP,description:"Manage your account information"},{id:"preferences",label:"Preferences",icon:i.FMC,description:"Customize your experience"},{id:"notifications",label:"Notifications",icon:i.b2w,description:"Control notification settings"},{id:"security",label:"Security",icon:i.nfp,description:"Password and security settings"},{id:"subscription",label:"Subscription",icon:i.XtC,description:"Manage your subscription plan"},{id:"billing",label:"Billing",icon:i.XtC,description:"Payment history and invoices"},{id:"data",label:"Data Management",icon:i.X2V,description:"Export, import, and manage your data"}],N=()=>{const[e,s]=(0,r.useState)("profile"),[a,m]=(0,r.useState)(!1),[N,w]=(0,r.useState)(null),[S,A]=(0,r.useState)(null),{user:C,logout:E}=(0,o.n)(),k=(0,j.Zp)(),[P,D]=(0,r.useState)({name:(null===C||void 0===C?void 0:C.name)||"",email:(null===C||void 0===C?void 0:C.email)||"",bio:"",avatar:null}),[T,_]=(0,r.useState)({theme:"dark",language:"en",studyReminders:!0,autoSave:!0,defaultStudyMode:"flashcards",sessionDuration:30,difficultyLevel:b.Cr.MEDIUM}),[R,O]=(0,r.useState)({emailNotifications:!0,studyReminders:!0,weeklyProgress:!1,marketingEmails:!1,achievementNotifications:!0,streakReminders:!0}),[L,I]=(0,r.useState)({twoFactorEnabled:!1,loginNotifications:!0,sessionTimeout:30}),[U,F]=(0,r.useState)(!1),[z,M]=(0,r.useState)(!1),[B,$]=(0,r.useState)("deactivate");(0,r.useEffect)(()=>{(async()=>{try{m(!0);const e=localStorage.getItem("auth_token");if(!e)return void w("Authentication required");const s=await fetch("/api/user/preferences",{headers:{Authorization:"Bearer ".concat(e)}});if(!s.ok)throw new Error("Failed to load user preferences");const a=await s.json();if(a.success&&a.data){const e=a.data;_({theme:e.theme,language:e.language,studyReminders:e.study_reminders,autoSave:e.auto_save,defaultStudyMode:e.default_study_mode,sessionDuration:e.session_duration,difficultyLevel:e.difficulty_level})}}catch(e){w("Failed to load user settings"),console.error("Load user settings error:",e)}finally{m(!1)}})()},[]);const H=async()=>{m(!0),w(null),A(null);try{const e=new FormData;e.append("name",P.name),e.append("bio",P.bio),P.avatar&&e.append("avatar",P.avatar);const s=localStorage.getItem("auth_token"),a=await fetch("/api/user/profile",{method:"PUT",headers:{Authorization:"Bearer ".concat(s)},body:e});if(!a.ok)throw new Error("Failed to update profile");const t=await a.json();if(!t.success)throw new Error(t.error);A("Profile updated successfully!")}catch(e){w(e instanceof Error?e.message:"Failed to update profile")}finally{m(!1)}},Y=async()=>{if(!a){m(!0),w(null),A(null);try{const e=localStorage.getItem("auth_token");if(!e)return w("Authentication required"),void m(!1);const s={theme:T.theme,language:T.language,study_reminders:T.studyReminders,auto_save:T.autoSave,default_study_mode:T.defaultStudyMode,session_duration:T.sessionDuration,difficulty_level:T.difficultyLevel},a=await fetch("/api/user/preferences",{method:"PUT",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok)throw new Error("Failed to update preferences");const t=await a.json();if(!t.success)throw new Error(t.error);A("Preferences updated successfully!")}catch(e){w(e instanceof Error?e.message:"Failed to update preferences")}finally{m(!1)}}},W=async()=>{m(!0),w(null),A(null);try{const e=localStorage.getItem("auth_token"),s=await fetch("/api/user/notifications",{method:"PUT",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(R)});if(!s.ok)throw new Error("Failed to update notification settings");const a=await s.json();if(!a.success)throw new Error(a.error);A("Notification settings updated successfully!")}catch(e){w(e instanceof Error?e.message:"Failed to update notification settings")}finally{m(!1)}},G=async()=>{try{m(!0),await E(),k("/login")}catch(N){w("Failed to logout. Please try again."),m(!1)}},K=()=>{var e,s;return(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Information"}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Profile Picture"}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:(null===C||void 0===C||null===(e=C.name)||void 0===e||null===(s=e.charAt(0))||void 0===s?void 0:s.toUpperCase())||"U"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(l.$,{variant:"secondary",size:"sm",onClick:()=>{const e=document.createElement("input");e.type="file",e.accept="image/*",e.onchange=e=>{var s;const a=null===(s=e.target.files)||void 0===s?void 0:s[0];a&&D((0,t.A)((0,t.A)({},P),{},{avatar:a}))},e.click()},children:[(0,d.jsx)(i.pT4,{className:"w-4 h-4 mr-2"}),"Upload Photo"]}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"JPG, PNG up to 5MB"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(c.p,{label:"Full Name",value:P.name,onChange:e=>D((0,t.A)((0,t.A)({},P),{},{name:e})),placeholder:"Enter your full name"}),(0,d.jsx)(c.p,{label:"Email Address",type:"email",value:P.email,onChange:e=>D((0,t.A)((0,t.A)({},P),{},{email:e})),placeholder:"Enter your email",disabled:!0}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio (Optional)"}),(0,d.jsx)("textarea",{value:P.bio,onChange:e=>D((0,t.A)((0,t.A)({},P),{},{bio:e.target.value})),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)(l.$,{onClick:H,isLoading:a,children:"Save Profile"})})]})})};return(0,d.jsxs)("div",{className:"min-h-screen bg-background-primary text-white",children:[(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Settings"}),(0,d.jsx)("p",{className:"text-gray-400",children:"Manage your account and preferences"})]}),N&&(0,d.jsxs)("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.TMu,{className:"w-5 h-5 text-red-400"}),(0,d.jsx)("span",{className:"text-red-400 font-medium",children:"Error"})]}),(0,d.jsx)("p",{className:"text-red-300 mt-1",children:N}),(0,d.jsx)(l.$,{onClick:()=>w(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),S&&(0,d.jsxs)("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.OLr,{className:"w-5 h-5 text-green-400"}),(0,d.jsx)("span",{className:"text-green-400 font-medium",children:"Success"})]}),(0,d.jsx)("p",{className:"text-green-300 mt-1",children:S}),(0,d.jsx)(l.$,{onClick:()=>A(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:(0,d.jsx)("nav",{className:"space-y-2",children:v.map(a=>{const t=a.icon,r=e===a.id;return(0,d.jsxs)("button",{onClick:()=>s(a.id),className:"\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\n                        transition-all duration-200\n                        ".concat(r?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white","\n                      "),children:[(0,d.jsx)(t,{className:"w-5 h-5"}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("span",{className:"font-medium block",children:a.label}),(0,d.jsx)("span",{className:"text-xs text-gray-500 block truncate",children:a.description})]})]},a.id)})})})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsx)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:(()=>{switch(e){case"profile":default:return K();case"preferences":return(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"App Preferences"}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)("button",{onClick:()=>_((0,t.A)((0,t.A)({},T),{},{theme:"dark"})),className:"flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ".concat("dark"===T.theme?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"),children:[(0,d.jsx)(i.Zt5,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Dark"})]}),(0,d.jsxs)("button",{onClick:()=>_((0,t.A)((0,t.A)({},T),{},{theme:"light"})),className:"flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ".concat("light"===T.theme?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"),disabled:!0,children:[(0,d.jsx)(i.Q3K,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Light (Coming Soon)"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Language"}),(0,d.jsxs)("select",{value:T.language,onChange:e=>_((0,t.A)((0,t.A)({},T),{},{language:e.target.value})),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"en",children:"English"}),(0,d.jsx)("option",{value:"es",disabled:!0,children:"Spanish (Coming Soon)"}),(0,d.jsx)("option",{value:"fr",disabled:!0,children:"French (Coming Soon)"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Study Mode"}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("button",{onClick:()=>_((0,t.A)((0,t.A)({},T),{},{defaultStudyMode:"flashcards"})),className:"flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ".concat("flashcards"===T.defaultStudyMode?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"),children:(0,d.jsx)("span",{children:"Flashcards"})}),(0,d.jsx)("button",{onClick:()=>_((0,t.A)((0,t.A)({},T),{},{defaultStudyMode:"quiz"})),className:"flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ".concat("quiz"===T.defaultStudyMode?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"),children:(0,d.jsx)("span",{children:"Quiz"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Session Duration (minutes)"}),(0,d.jsxs)("select",{value:T.sessionDuration,onChange:e=>_((0,t.A)((0,t.A)({},T),{},{sessionDuration:parseInt(e.target.value)})),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:15,children:"15 minutes"}),(0,d.jsx)("option",{value:30,children:"30 minutes"}),(0,d.jsx)("option",{value:45,children:"45 minutes"}),(0,d.jsx)("option",{value:60,children:"1 hour"}),(0,d.jsx)("option",{value:90,children:"1.5 hours"})]})]}),(0,d.jsx)(f.B,{value:T.difficultyLevel,onChange:e=>_((0,t.A)((0,t.A)({},T),{},{difficultyLevel:e})),label:"Default Difficulty Level",className:"bg-background-secondary rounded-lg p-4"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Study Reminders"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Get reminded to study regularly"})]}),(0,d.jsx)("button",{onClick:()=>_((0,t.A)((0,t.A)({},T),{},{studyReminders:!T.studyReminders})),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(T.studyReminders?"bg-primary-500":"bg-gray-600"),children:(0,d.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(T.studyReminders?"translate-x-6":"translate-x-1")})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Auto-save"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Automatically save your progress"})]}),(0,d.jsx)("button",{onClick:()=>_((0,t.A)((0,t.A)({},T),{},{autoSave:!T.autoSave})),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(T.autoSave?"bg-primary-500":"bg-gray-600"),children:(0,d.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(T.autoSave?"translate-x-6":"translate-x-1")})})]})]})]}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)(l.$,{onClick:Y,isLoading:a,children:"Save Preferences"})})]})});case"notifications":return(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Notification Settings"}),(0,d.jsx)("div",{className:"space-y-4",children:Object.entries(R).map(e=>{let[s,a]=e;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-300 capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["emailNotifications"===s&&"Receive important updates via email","studyReminders"===s&&"Get reminded when it's time to study","weeklyProgress"===s&&"Weekly summary of your study progress","marketingEmails"===s&&"Product updates and tips","achievementNotifications"===s&&"Get notified when you unlock achievements","streakReminders"===s&&"Reminders to maintain your study streak"]})]}),(0,d.jsx)("button",{onClick:()=>O((0,t.A)((0,t.A)({},R),{},{[s]:!a})),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(a?"bg-primary-500":"bg-gray-600"),children:(0,d.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(a?"translate-x-6":"translate-x-1")})})]},s)})}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)(l.$,{onClick:W,isLoading:a,children:"Save Notification Settings"})})]})});case"security":return(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Security Settings"}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(p,{enabled:L.twoFactorEnabled,onToggle:e=>I((0,t.A)((0,t.A)({},L),{},{twoFactorEnabled:e}))}),(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[(0,d.jsx)("h4",{className:"font-medium text-white mb-4",children:"Session Management"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Login Notifications"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Get notified when someone logs into your account"})]}),(0,d.jsx)("button",{onClick:()=>I((0,t.A)((0,t.A)({},L),{},{loginNotifications:!L.loginNotifications})),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(L.loginNotifications?"bg-primary-500":"bg-gray-600"),children:(0,d.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(L.loginNotifications?"translate-x-6":"translate-x-1")})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),(0,d.jsxs)("select",{value:L.sessionTimeout,onChange:e=>I((0,t.A)((0,t.A)({},L),{},{sessionTimeout:parseInt(e.target.value)})),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:15,children:"15 minutes"}),(0,d.jsx)("option",{value:30,children:"30 minutes"}),(0,d.jsx)("option",{value:60,children:"1 hour"}),(0,d.jsx)("option",{value:120,children:"2 hours"}),(0,d.jsx)("option",{value:480,children:"8 hours"}),(0,d.jsx)("option",{value:1440,children:"1 day"}),(0,d.jsx)("option",{value:10080,children:"1 week"}),(0,d.jsx)("option",{value:20160,children:"2 weeks"}),(0,d.jsx)("option",{value:30240,children:"3 weeks"}),(0,d.jsx)("option",{value:40320,children:"4 weeks"}),(0,d.jsx)("option",{value:50400,children:"5 weeks"}),(0,d.jsx)("option",{value:60480,children:"6 weeks"}),(0,d.jsx)("option",{value:70560,children:"7 weeks"}),(0,d.jsx)("option",{value:80640,children:"8 weeks"}),(0,d.jsx)("option",{value:0,children:"Never expire"})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,d.jsx)(i.tWn,{className:"w-5 h-5 text-primary-400"}),(0,d.jsx)("h4",{className:"font-medium text-white",children:"Change Password"})]}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Update your password to keep your account secure"}),(0,d.jsx)(l.$,{variant:"secondary",onClick:()=>F(!0),children:"Change Password"})]}),(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,d.jsx)(i.QJT,{className:"w-5 h-5 text-orange-400"}),(0,d.jsx)("h4",{className:"font-medium text-white",children:"Sign Out"})]}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Sign out of your account on this device"}),(0,d.jsx)(l.$,{variant:"secondary",onClick:G,disabled:a,children:a?"Signing Out...":"Sign Out"})]}),(0,d.jsxs)("div",{className:"bg-red-500/10 rounded-lg p-4 border border-red-500/30",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,d.jsx)(i.TMu,{className:"w-5 h-5 text-red-400"}),(0,d.jsx)("h4",{className:"font-medium text-white",children:"Danger Zone"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deactivation"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-3",children:"Temporarily deactivate your account. You can reactivate it later."}),(0,d.jsx)(l.$,{variant:"secondary",size:"sm",onClick:()=>{$("deactivate"),M(!0)},children:"Deactivate Account"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deletion"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mb-3",children:"Permanently delete your account and all associated data. This action cannot be undone."}),(0,d.jsx)(l.$,{variant:"danger",size:"sm",onClick:()=>{$("delete"),M(!0)},children:"Delete Account"})]})]})]})]})]})});case"subscription":return(0,d.jsx)(x,{});case"billing":return(0,d.jsx)(h,{});case"data":return(0,d.jsx)(u,{})}})()},e)})]})]}),(0,d.jsx)(g,{isOpen:U,onClose:()=>F(!1),onSuccess:()=>{console.log("Password changed successfully")}}),(0,d.jsx)(y,{isOpen:z,onClose:()=>M(!1),action:B})]})}},9391:(e,s,a)=>{a.d(s,{Cr:()=>t,KF:()=>l,eX:()=>n,qt:()=>r,sQ:()=>i});let t=function(e){return e.EASY="easy",e.MEDIUM="medium",e.HARD="hard",e.COLLEGE="college",e.GRADUATE="graduate",e.PHD="phd",e}({}),r=function(e){return e.SHORT="short",e.MEDIUM="medium",e.LONG="long",e}({});const n=e=>({[t.EASY]:"Easy",[t.MEDIUM]:"Medium",[t.HARD]:"Hard",[t.COLLEGE]:"College",[t.GRADUATE]:"Graduate",[t.PHD]:"PhD"}[e]),i=e=>({[t.EASY]:1,[t.MEDIUM]:3,[t.HARD]:4,[t.COLLEGE]:5,[t.GRADUATE]:6,[t.PHD]:7}[e]),l=e=>{switch(e){case 1:case 2:return t.EASY;case 3:default:return t.MEDIUM;case 4:return t.HARD;case 5:return t.COLLEGE;case 6:return t.GRADUATE;case 7:return t.PHD}}}}]);
//# sourceMappingURL=30.50d8cfa6.chunk.js.map