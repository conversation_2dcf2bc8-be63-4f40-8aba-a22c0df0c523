import React, { useId } from "react";
import { InputProps } from "../../../../shared/types";

interface EnhancedInputProps extends InputProps {
  'aria-describedby'?: string;
  'aria-invalid'?: boolean;
  description?: string;
}

export const Input: React.FC<EnhancedInputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  type = "text",
  error,
  required = false,
  disabled = false,
  className = "",
  'aria-describedby': ariaDescribedBy,
  'aria-invalid': ariaInvalid,
  description,
  ...props
}) => {
  const id = useId();
  const errorId = `${id}-error`;
  const descriptionId = `${id}-description`;
  const inputClasses = `w-full px-3 py-2 border-2 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${
    error ? "border-red-500" : "border-gray-600 focus:border-primary-500"
  } ${className}`;

  const describedBy = [
    error ? errorId : null,
    description ? descriptionId : null,
    ariaDescribedBy
  ].filter(Boolean).join(' ') || undefined;

  return (
    <div className="w-full">
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-300 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
        </label>
      )}
      {description && (
        <p id={descriptionId} className="text-sm text-gray-400 mb-1">
          {description}
        </p>
      )}
      <input
        id={id}
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        aria-invalid={ariaInvalid || !!error}
        aria-describedby={describedBy}
        className={inputClasses}
        {...props}
      />
      {error && (
        <p id={errorId} className="mt-1 text-sm text-red-500" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};
