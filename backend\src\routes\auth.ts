import { Router, Request, Response } from "express";
import { supabase, supabaseService } from "../services/supabaseService";
import { authenticateToken } from "../middleware/auth";
import { AuthResult } from "../../../shared/types";

const router = Router();

router.post("/signup", async (req: Request, res: Response) => {
  try {
    const { email, password, name } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ success: false, error: "Email and password are required" });
    }

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: { data: { name } },
    });

    if (authError) {
      return res.status(400).json({ success: false, error: authError.message });
    }

    if (!authData.user) {
      return res
        .status(400)
        .json({ success: false, error: "Failed to create user account" });
    }

    const profile = await supabaseService.createUserProfile(
      authData.user.id,
      email,
      name
    );

    const result: AuthResult = {
      success: true,
      user: profile,
      token: authData.session?.access_token,
    };

    res.status(201).json(result);
  } catch (error) {
    console.error("Signup error:", error);
    res.status(500).json({ success: false, error: "Internal server error" });
  }
});

router.post("/login", async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ success: false, error: "Email and password are required" });
    }

    const { data: authData, error: authError } =
      await supabase.auth.signInWithPassword({
        email,
        password,
      });

    if (authError) {
      return res.status(401).json({ success: false, error: authError.message });
    }

    if (!authData.user || !authData.session) {
      return res
        .status(401)
        .json({ success: false, error: "Authentication failed" });
    }

    const profile = await supabaseService.getUserProfile(authData.user.id);

    if (!profile) {
      return res
        .status(404)
        .json({ success: false, error: "User profile not found" });
    }

    await supabaseService.updateUserProfile(authData.user.id, {
      last_login: new Date().toISOString(),
    });

    const result: AuthResult = {
      success: true,
      user: profile,
      token: authData.session.access_token,
    };

    res.json(result);
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ success: false, error: "Internal server error" });
  }
});

router.get("/user", authenticateToken, async (req: Request, res: Response) => {
  try {
    const profile = await supabaseService.getUserProfile(req.user!.id);
    res.json({ success: true, data: profile });
  } catch (error) {
    console.error("Get user error:", error);
    res
      .status(500)
      .json({ success: false, error: "Failed to get user profile" });
  }
});

// Google OAuth initiation
router.post("/google", async (req: Request, res: Response) => {
  try {
    const { redirectTo, flowType = "implicit" } = req.body;

    console.log("Google OAuth request received:", { redirectTo, flowType });

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: redirectTo || `${process.env.FRONTEND_URL}/auth/callback`,
        queryParams: {
          access_type: "offline",
          prompt: "consent",
        },
        scopes: "openid email profile",
      },
    });

    if (error) {
      console.error("Supabase OAuth error:", error);
      return res.status(400).json({
        success: false,
        error: error.message,
        details:
          "Google OAuth may not be configured in Supabase. Please configure Google OAuth in your Supabase dashboard under Authentication > Providers.",
      });
    }

    console.log("Google OAuth URL generated successfully:", data.url);

    res.json({
      success: true,
      url: data.url,
      flowType: flowType,
      message:
        flowType === "pkce"
          ? "Using PKCE flow - expecting authorization code in callback"
          : "Using implicit flow - expecting tokens in URL fragment",
    });
  } catch (error) {
    console.error("Google OAuth initiation error:", error);
    res
      .status(500)
      .json({ success: false, error: "Failed to initiate Google OAuth" });
  }
});

// OAuth callback handler
router.post("/oauth-callback", async (req: Request, res: Response) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res
        .status(400)
        .json({ success: false, error: "Authorization code required" });
    }

    // Exchange code for session
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);

    if (error || !data.session) {
      return res.status(400).json({
        success: false,
        error: error?.message || "Failed to exchange code for session",
      });
    }

    const user = data.session.user;
    if (!user) {
      return res
        .status(400)
        .json({ success: false, error: "No user data received" });
    }

    // Check if user profile already exists
    let profile = await supabaseService.getUserProfile(user.id);

    if (!profile) {
      // Create new user profile for OAuth user
      profile = await supabaseService.createUserProfile(
        user.id,
        user.email!,
        user.user_metadata?.full_name ||
          user.user_metadata?.name ||
          user.email!.split("@")[0]
      );
    } else {
      // Update last login for existing user
      profile = await supabaseService.updateUserProfile(user.id, {
        last_login: new Date().toISOString(),
      });
    }

    const result: AuthResult = {
      success: true,
      user: profile,
      token: data.session.access_token,
    };

    res.json(result);
  } catch (error) {
    console.error("OAuth callback error:", error);
    res
      .status(500)
      .json({ success: false, error: "Failed to process OAuth callback" });
  }
});

router.post("/oauth-user", async (req: Request, res: Response) => {
  try {
    // Handle both old format (id, email, name) and new format (access_token)
    const { id, name, access_token } = req.body;
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    // Use token from header or body
    const accessToken = token || access_token;

    if (!accessToken) {
      return res
        .status(401)
        .json({ success: false, error: "No access token provided" });
    }

    // Verify the token with Supabase
    const {
      data: { user },
      error: verifyError,
    } = await supabaseService.verifyToken(accessToken);

    if (verifyError || !user) {
      return res.status(401).json({
        success: false,
        error: "Invalid or expired token",
      });
    }

    // For the old format, verify user ID matches
    if (id && user.id !== id) {
      return res.status(401).json({
        success: false,
        error: "Token user ID mismatch",
      });
    }

    // Check if user profile already exists
    let profile = await supabaseService.getUserProfile(user.id);

    if (!profile) {
      // Create new user profile for OAuth user
      const userName =
        user.user_metadata?.full_name ||
        user.user_metadata?.name ||
        name ||
        user.email?.split("@")[0] ||
        "User";

      profile = await supabaseService.createUserProfile(
        user.id,
        user.email!,
        userName
      );
    } else {
      // Update last login for existing user
      profile = await supabaseService.updateUserProfile(user.id, {
        last_login: new Date().toISOString(),
      });
    }

    const result: AuthResult = {
      success: true,
      user: profile,
      token: accessToken,
    };

    res.json(result);
  } catch (error) {
    console.error("OAuth user error:", error);
    res.status(500).json({ success: false, error: "Internal server error" });
  }
});

// Debug endpoint to check token
router.post("/debug-token", async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    console.log("Debug - Auth header:", authHeader);
    console.log(
      "Debug - Token:",
      token ? `${token.substring(0, 20)}...` : "null"
    );

    if (!token) {
      return res.json({
        success: false,
        error: "No token provided",
        authHeader: authHeader || "missing",
      });
    }

    const {
      data: { user },
      error,
    } = await supabaseService.verifyToken(token);

    console.log("Debug - Supabase verification error:", error);
    console.log("Debug - Supabase user:", user ? user.id : "null");

    if (error || !user) {
      return res.json({
        success: false,
        error: "Token verification failed",
        supabaseError: error?.message || "Unknown error",
      });
    }

    const profile = await supabaseService.getUserProfile(user.id);

    console.log("Debug - Profile found:", !!profile);
    console.log("Debug - Profile active:", profile?.is_active);

    res.json({
      success: true,
      data: {
        userId: user.id,
        email: user.email,
        profileExists: !!profile,
        profileActive: profile?.is_active,
      },
    });
  } catch (error: any) {
    console.log("Debug - Exception:", error.message);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post(
  "/logout",
  authenticateToken,
  async (_req: Request, res: Response) => {
    try {
      // Supabase handles token invalidation on client side
      res.json({ success: true, message: "Logged out successfully" });
    } catch (error) {
      console.error("Logout error:", error);
      res.status(500).json({
        success: false,
        error: "Logout failed",
      });
    }
  }
);

// Change password
router.post(
  "/change-password",
  authenticateToken,
  async (req: Request, res: Response) => {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = (req as any).user.id;

      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          error: "Current password and new password are required",
        });
      }

      if (newPassword.length < 8) {
        return res.status(400).json({
          success: false,
          error: "New password must be at least 8 characters long",
        });
      }

      // Get user's current email for verification
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("email")
        .eq("id", userId)
        .single();

      if (userError || !userData) {
        return res.status(404).json({
          success: false,
          error: "User not found",
        });
      }

      // Verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: userData.email,
        password: currentPassword,
      });

      if (signInError) {
        return res.status(400).json({
          success: false,
          error: "Current password is incorrect",
        });
      }

      // Update password using Supabase Admin API
      const { error: updateError } = await supabase.auth.admin.updateUserById(
        userId,
        { password: newPassword }
      );

      if (updateError) {
        return res.status(400).json({
          success: false,
          error: updateError.message,
        });
      }

      res.json({
        success: true,
        message: "Password updated successfully",
      });
    } catch (error) {
      console.error("Change password error:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  }
);

// Deactivate account
router.post(
  "/deactivate-account",
  authenticateToken,
  async (req: Request, res: Response) => {
    try {
      const userId = (req as any).user.id;
      const { confirmation } = req.body;

      if (confirmation !== "DEACTIVATE") {
        return res.status(400).json({
          success: false,
          error: "Invalid confirmation",
        });
      }

      // Update user status to deactivated
      const { error: updateError } = await supabase
        .from("users")
        .update({
          is_active: false,
          deactivated_at: new Date().toISOString(),
        })
        .eq("id", userId);

      if (updateError) {
        return res.status(400).json({
          success: false,
          error: "Failed to deactivate account",
        });
      }

      res.json({
        success: true,
        message: "Account deactivated successfully",
      });
    } catch (error) {
      console.error("Deactivate account error:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  }
);

// Delete account
router.post(
  "/delete-account",
  authenticateToken,
  async (req: Request, res: Response) => {
    try {
      const userId = (req as any).user.id;
      const { confirmation } = req.body;

      if (confirmation !== "DELETE FOREVER") {
        return res.status(400).json({
          success: false,
          error: "Invalid confirmation",
        });
      }

      // Delete user data in order (due to foreign key constraints)
      const deleteOperations = [
        // Delete study sessions first
        supabase.from("study_sessions").delete().eq("user_id", userId),
        // Delete quiz questions
        supabase.from("quiz_questions").delete().eq("user_id", userId),
        // Delete flashcards
        supabase.from("flashcards").delete().eq("user_id", userId),
        // Delete study sets
        supabase.from("study_sets").delete().eq("user_id", userId),
        // Delete documents
        supabase.from("documents").delete().eq("user_id", userId),
        // Delete credit transactions
        supabase.from("credit_transactions").delete().eq("user_id", userId),
        // Finally delete user
        supabase.from("users").delete().eq("id", userId),
      ];

      for (const operation of deleteOperations) {
        const { error } = await operation;
        if (error) {
          console.error("Delete operation error:", error);
          // Continue with other deletions even if one fails
        }
      }

      // Delete from Supabase Auth
      const { error: authDeleteError } = await supabase.auth.admin.deleteUser(
        userId
      );
      if (authDeleteError) {
        console.error("Auth delete error:", authDeleteError);
      }

      res.json({
        success: true,
        message: "Account deleted successfully",
      });
    } catch (error) {
      console.error("Delete account error:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  }
);

export default router;
