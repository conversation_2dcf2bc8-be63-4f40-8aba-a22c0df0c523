import React from 'react';
import { NeverAskAgainCheckboxProps } from '../../types/userSettings';

export const NeverAskAgainCheckbox: React.FC<NeverAskAgainCheckboxProps> = ({
  checked,
  onChange,
  disabled = false,
  className = ''
}) => {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <input
        type="checkbox"
        id="never-ask-again"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
        className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed"
      />
      <label
        htmlFor="never-ask-again"
        className={`text-sm text-gray-300 select-none ${
          disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        }`}
      >
        Never ask again
      </label>
    </div>
  );
};
