"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[754],{5754:(e,t,s)=>{s.r(t),s.d(t,{StudyPage:()=>x});var n=s(9643),r=s(7192),a=s(9855),o=s(7240),i=s(4859),c=s(6507);const d=e=>{let{message:t,priority:s="polite",clearAfter:r=3e3}=e;const[a,o]=(0,n.useState)("");return(0,n.useEffect)(()=>{if(t&&(o(t),r>0)){const e=setTimeout(()=>{o("")},r);return()=>clearTimeout(e)}},[t,r]),(0,c.jsx)("div",{"aria-live":s,"aria-atomic":"true",className:"sr-only",role:"status",children:a})};var l=s(1721);const u=(0,n.memo)(()=>{var e;const t=(0,r.Zp)(),{alert:s}=(0,l.s)(),{currentSession:o,studySetContent:u,nextItem:m,previousItem:x,toggleFlag:h,markReviewed:f,endStudySession:y,updateTimeSpent:g,undo:p,redo:v,canUndo:w,canRedo:S}=(0,a.useStudyStore)(),[b,I]=(0,n.useState)(!1),[j,k]=(0,n.useState)(Date.now()),{announce:N,AnnouncementComponent:C}=(()=>{const[e,t]=(0,n.useState)(""),[s,r]=(0,n.useState)("polite");return{announce:function(e){r(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"polite"),t(e)},AnnouncementComponent:()=>(0,c.jsx)(d,{message:e,priority:s})}})();if((0,n.useEffect)(()=>{const e=setInterval(()=>{g(1)},1e3);return()=>clearInterval(e)},[g]),(0,n.useEffect)(()=>{I(!1),k(Date.now())},[null===o||void 0===o?void 0:o.currentIndex]),(0,n.useEffect)(()=>{const e=e=>{var t;if(!(e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement||"true"===(null===(t=e.target)||void 0===t?void 0:t.contentEditable)))if("ArrowLeft"===e.key){if(e.preventDefault(),o){x();const e=0===o.currentIndex?o.totalItems:o.currentIndex;N("Card ".concat(e," of ").concat(o.totalItems))}}else if("ArrowRight"===e.key){if(e.preventDefault(),o){m();const e=o.currentIndex===o.totalItems-1?1:o.currentIndex+2;N("Card ".concat(e," of ").concat(o.totalItems))}}else if(" "===e.key||"ArrowUp"===e.key||"ArrowDown"===e.key)e.preventDefault(),I(!b);else if("f"===e.key||"F"===e.key){if(e.preventDefault(),o&&null!==u&&void 0!==u&&u.flashcards){const e=u.flashcards[o.currentIndex];if(e){h(e.id);const t=o.flaggedItems.includes(e.id);N(t?"Card unflagged":"Card flagged for review")}}}else!e.ctrlKey&&!e.metaKey||"z"!==e.key||e.shiftKey?(e.ctrlKey||e.metaKey)&&("y"===e.key||"z"===e.key&&e.shiftKey)&&(e.preventDefault(),S&&v()):(e.preventDefault(),w&&p())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o,u,b,w,S,x,m,h,p,v,N]),!o||null===u||void 0===u||!u.flashcards)return(0,c.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,c.jsxs)("div",{className:"text-center py-12",children:[(0,c.jsx)("div",{className:"text-gray-400 mb-4",children:"No flashcard session found"}),(0,c.jsx)(i.$,{onClick:()=>t("/dashboard"),variant:"secondary",children:"Back to Study Sets"})]})});const A=u.flashcards[o.currentIndex],E=(o.currentIndex+1)/o.totalItems*100,T=0===o.currentIndex,_=o.currentIndex===o.totalItems-1,F=o.flaggedItems.includes(A.id),q=()=>{I(!b),N(b?"Showing front of card":"Showing back of card")};return(0,c.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,c.jsx)("button",{onClick:()=>t("/study-sets/".concat(o.studySetId)),className:"text-gray-400 hover:text-white flex items-center",children:"\u2190 Back to Study Set"}),(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("h1",{className:"text-xl font-semibold text-white",children:null===(e=u.studySet)||void 0===e?void 0:e.name}),(0,c.jsxs)("p",{className:"text-sm text-gray-400",children:["Card ",o.currentIndex+1," of ",o.totalItems]})]}),(0,c.jsx)(i.$,{onClick:async()=>{const e=Math.floor((Date.now()-j)/1e3),n=o.reviewedItems.length,r=o.flaggedItems.length;y(),await s({title:"Study Session Complete!",message:"Reviewed: ".concat(n,"/").concat(o.totalItems," cards\nFlagged: ").concat(r," cards\nTime spent: ").concat(Math.floor(e/60),"m ").concat(e%60,"s"),variant:"success",confirmText:"Continue"}),t("/study-sets/".concat(o.studySetId))},variant:"secondary",size:"sm",children:"Finish"})]}),(0,c.jsxs)("div",{className:"mb-6",children:[(0,c.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,c.jsx)("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(E,"%")}})}),(0,c.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,c.jsxs)("span",{children:["Progress: ",Math.round(E),"%"]}),(0,c.jsxs)("span",{children:["Time: ",Math.floor(o.timeSpent/60),":",(o.timeSpent%60).toString().padStart(2,"0")]})]})]}),(0,c.jsx)("div",{className:"mb-8 flashcard-container",children:(0,c.jsxs)("div",{className:"\n            relative w-full min-h-[24rem] max-h-[32rem] cursor-pointer transition-transform duration-500 transform-style-preserve-3d\n            ".concat(b?"rotate-y-180":"","\n          "),onClick:q,role:"button",tabIndex:0,"aria-label":"Flashcard ".concat(o.currentIndex+1," of ").concat(o.totalItems,". ").concat(b?"Showing back":"Showing front",". Click or press space to flip."),onKeyDown:e=>{" "!==e.key&&"Enter"!==e.key||(e.preventDefault(),q())},children:[(0,c.jsx)("div",{className:"\n            absolute inset-0 w-full h-full backface-hidden\n            bg-background-secondary border border-gray-600 rounded-lg p-6 sm:p-8\n            flex flex-col justify-center text-center overflow-y-auto\n          ",children:(0,c.jsxs)("div",{className:"flex-1 flex flex-col justify-center min-h-0",children:[(0,c.jsx)("div",{className:"text-sm text-gray-400 mb-4 flex-shrink-0",children:"FRONT"}),(0,c.jsx)("div",{className:"text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center",children:(0,c.jsx)("div",{className:"max-w-full",children:A.front})}),!b&&(0,c.jsx)("div",{className:"text-sm text-gray-500 mt-6 flex-shrink-0",children:"Click to reveal answer"})]})}),(0,c.jsx)("div",{className:"\n            absolute inset-0 w-full h-full backface-hidden rotate-y-180\n            bg-primary-500/10 border border-primary-500/30 rounded-lg p-6 sm:p-8\n            flex flex-col justify-center text-center overflow-y-auto\n          ",children:(0,c.jsxs)("div",{className:"flex-1 flex flex-col justify-center min-h-0",children:[(0,c.jsx)("div",{className:"text-sm text-primary-400 mb-4 flex-shrink-0",children:"BACK"}),(0,c.jsx)("div",{className:"text-lg sm:text-xl text-white leading-relaxed break-words overflow-y-auto flex-1 flex items-center justify-center",children:(0,c.jsx)("div",{className:"max-w-full",children:A.back})}),b&&(0,c.jsx)("div",{className:"text-sm text-gray-500 mt-6 flex-shrink-0",children:"Click to flip back"})]})})]})}),(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsx)(i.$,{onClick:()=>{x();const e=T?o.totalItems:o.currentIndex;N("Card ".concat(e," of ").concat(o.totalItems))},variant:"secondary",children:"\u2190 Previous"}),(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsx)(i.$,{onClick:()=>{h(A.id);N("Card ".concat(F?"unflagged":"flagged"))},variant:F?"primary":"secondary",size:"sm",children:F?"\ud83d\udea9 Flagged":"\ud83c\udff3\ufe0f Flag"}),(0,c.jsx)(i.$,{onClick:()=>I(!b),variant:"secondary",children:b?"Show Front":"Show Back"})]}),(0,c.jsx)(i.$,{onClick:()=>{b&&f(A.id),m();const e=_?1:o.currentIndex+2;N("Card ".concat(e," of ").concat(o.totalItems))},variant:"primary",children:"Next \u2192"})]}),(0,c.jsx)("div",{className:"mt-8 text-center text-sm text-gray-500",children:(0,c.jsx)("p",{children:"Keyboard shortcuts: \u2190 \u2192 (navigate) \u2022 Space (flip) \u2022 F (flag)"})}),(0,c.jsx)(C,{})]})}),m=()=>{var e;const t=(0,r.Zp)(),{alert:s}=(0,l.s)(),{currentSession:o,studySetContent:d,nextItem:u,submitQuizAnswer:m,endStudySession:x,updateTimeSpent:h}=(0,a.useStudyStore)(),[f,y]=(0,n.useState)([]),[g,p]=(0,n.useState)(!1),[v,w]=(0,n.useState)(!1);if((0,n.useEffect)(()=>{const e=setInterval(()=>{h(1)},1e3);return()=>clearInterval(e)},[h]),(0,n.useEffect)(()=>{y([]),p(!1),w(!1)},[null===o||void 0===o?void 0:o.currentIndex]),!o||null===d||void 0===d||!d.questions)return(0,c.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,c.jsxs)("div",{className:"text-center py-12",children:[(0,c.jsx)("div",{className:"text-gray-400 mb-4",children:"No quiz session found"}),(0,c.jsx)(i.$,{onClick:()=>t("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const S=d.questions[o.currentIndex],b=(o.currentIndex+1)/o.totalItems*100,I=o.currentIndex===o.totalItems-1,j=e=>{g||("multiple_choice"===S.question_type||"true_false"===S.question_type?y([e]):"select_all"===S.question_type&&y(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e]))},k=()=>{const e=S.correct_answers;if("short_answer"===S.question_type){var t;const s=(null===(t=f[0])||void 0===t?void 0:t.toLowerCase().trim())||"";return e.some(e=>s.includes(e.toLowerCase().trim())||e.toLowerCase().trim().includes(s))}return f.length===e.length&&f.every(t=>e.includes(t))},N=async()=>{const e=o.totalItems,n=o.correctAnswers||0,r=Math.round(n/e*100),a=o.timeSpent;x(),await s({title:"Quiz Complete!",message:"Score: ".concat(n,"/").concat(e," (").concat(r,"%)\nTime spent: ").concat(Math.floor(a/60),"m ").concat(a%60,"s"),variant:"success",confirmText:"Continue"}),t("/study-sets/".concat(o.studySetId))};return(0,c.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,c.jsx)("button",{onClick:()=>t("/study-sets/".concat(o.studySetId)),className:"text-gray-400 hover:text-white flex items-center",children:"\u2190 Back to Study Set"}),(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("h1",{className:"text-xl font-semibold text-white",children:null===(e=d.studySet)||void 0===e?void 0:e.name}),(0,c.jsxs)("p",{className:"text-sm text-gray-400",children:["Question ",o.currentIndex+1," of ",o.totalItems]})]}),(0,c.jsx)(i.$,{onClick:N,variant:"secondary",size:"sm",children:"Finish Quiz"})]}),(0,c.jsxs)("div",{className:"mb-6",children:[(0,c.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,c.jsx)("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(b,"%")}})}),(0,c.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,c.jsxs)("span",{children:["Progress: ",Math.round(b),"%"]}),(0,c.jsxs)("span",{children:["Score: ",o.correctAnswers||0,"/",o.currentIndex+(g?1:0)]}),(0,c.jsxs)("span",{children:["Time: ",Math.floor(o.timeSpent/60),":",(o.timeSpent%60).toString().padStart(2,"0")]})]})]}),(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[(0,c.jsx)("div",{className:"mb-4",children:(0,c.jsx)("span",{className:"text-sm text-gray-400 uppercase tracking-wide",children:S.question_type.replace("_"," ")})}),(0,c.jsx)("h2",{className:"text-xl text-white mb-6 leading-relaxed",children:S.question_text}),(0,c.jsx)("div",{className:"space-y-3",children:"short_answer"===S.question_type?(0,c.jsx)("textarea",{value:f[0]||"",onChange:e=>{return t=e.target.value,void(g||y([t]));var t},disabled:g,placeholder:"Type your answer here...",rows:3,className:"w-full px-4 py-3 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"}):"true_false"===S.question_type?["True","False"].map(e=>{const t=f.includes(e),s=S.correct_answers.includes(e);let n="w-full text-left p-4 rounded-lg border transition-all ";return n+=g?s?"border-green-500 bg-green-500/20 text-green-400":t&&!s?"border-red-500 bg-red-500/20 text-red-400":"border-gray-600 bg-background-secondary text-gray-400":t?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 bg-background-secondary text-white hover:border-gray-500",(0,c.jsx)("button",{onClick:()=>j(e),disabled:g,className:n,children:(0,c.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,c.jsx)("div",{className:"\n              w-5 h-5 rounded-full border-2 flex items-center justify-center\n              ".concat(t?"border-current":"border-gray-500","\n            "),children:t&&(0,c.jsx)("div",{className:"w-3 h-3 rounded-full bg-current"})}),(0,c.jsx)("span",{className:"text-lg font-medium",children:e})]})},e)}):S.options?S.options.map((e,t)=>{const s=f.includes(e),n=S.correct_answers.includes(e);let r="w-full text-left p-4 rounded-lg border transition-all ";return r+=g?n?"border-green-500 bg-green-500/20 text-green-400":s&&!n?"border-red-500 bg-red-500/20 text-red-400":"border-gray-600 bg-background-secondary text-gray-400":s?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 bg-background-secondary text-white hover:border-gray-500",(0,c.jsx)("button",{onClick:()=>j(e),disabled:g,className:r,children:(0,c.jsxs)("div",{className:"flex items-center space-x-3",children:["multiple_choice"===S.question_type?(0,c.jsx)("div",{className:"\n                w-5 h-5 rounded-full border-2 flex items-center justify-center\n                ".concat(s?"border-current":"border-gray-500","\n              "),children:s&&(0,c.jsx)("div",{className:"w-3 h-3 rounded-full bg-current"})}):(0,c.jsx)("div",{className:"\n                w-5 h-5 rounded border-2 flex items-center justify-center\n                ".concat(s?"border-current":"border-gray-500","\n              "),children:s&&(0,c.jsx)("div",{className:"w-2 h-2 rounded bg-current"})}),(0,c.jsx)("span",{children:e})]})},t)}):null}),!g&&(0,c.jsx)("div",{className:"mt-6",children:(0,c.jsx)(i.$,{onClick:()=>{if(g||0===f.length)return;const e=k();m(S.id,f,e),p(!0),w(!0)},disabled:0===f.length,className:"w-full",children:"Submit Answer"})}),g&&v&&S.explanation&&(0,c.jsxs)("div",{className:"mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg",children:[(0,c.jsx)("h4",{className:"text-blue-400 font-medium mb-2",children:"Explanation"}),(0,c.jsx)("p",{className:"text-gray-300",children:S.explanation})]})]}),g&&(0,c.jsx)("div",{className:"flex justify-center",children:(0,c.jsx)(i.$,{onClick:()=>{I?N():u()},variant:"primary",size:"lg",children:I?"Finish Quiz":"Next Question \u2192"})})]})},x=()=>{const{id:e,mode:t}=(0,r.g)(),s=(0,r.Zp)(),{currentSession:i,startStudySession:d}=(0,a.useStudyStore)(),{settings:l}=(0,o.Q)();return(0,n.useEffect)(()=>{if(!i||i.studySetId!==e||i.type!==t)if(e&&t&&("flashcards"===t||"quiz"===t)){const n=(null===l||void 0===l?void 0:l.shuffle_flashcards)||!1;d(e,t,n).catch(t=>{console.error("Failed to start study session:",t),alert(t.message||"Failed to start study session"),s("/study-sets/".concat(e))})}else s("/dashboard")},[e,t,i,d,s,l]),i?"flashcards"===t?(0,c.jsx)(u,{}):"quiz"===t?(0,c.jsx)(m,{}):(s("/dashboard"),null):(0,c.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,c.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,c.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),(0,c.jsx)("span",{className:"ml-3 text-gray-400",children:"Starting study session..."})]})})}},7240:(e,t,s)=>{s.d(t,{Q:()=>r});var n=s(9643);const r=()=>{const[e,t]=(0,n.useState)(null),[s,r]=(0,n.useState)(!0),[a,o]=(0,n.useState)(null),i=(0,n.useCallback)(async()=>{try{r(!0),o(null);const e=localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token");if(!e)throw new Error("No authentication token found");const s=await fetch("/api/user/settings",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok){const e=await s.json();throw new Error(e.error||"Failed to fetch user settings")}const n=await s.json();t(n.data)}catch(e){o(e.message),console.error("Error fetching user settings:",e)}finally{r(!1)}},[]),c=(0,n.useCallback)(async e=>{try{o(null);const s=localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token");if(!s)throw new Error("No authentication token found");const n=await fetch("/api/user/settings",{method:"PATCH",headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"},body:JSON.stringify(e)});if(!n.ok){const e=await n.json();throw new Error(e.error||"Failed to update user settings")}const r=await n.json();t(r.data)}catch(s){throw o(s.message),console.error("Error updating user settings:",s),s}},[]),d=(0,n.useCallback)(async()=>{await i()},[i]);return(0,n.useEffect)(()=>{i()},[i]),{settings:e,loading:s,error:a,updateSettings:c,refetch:d}}},9855:(e,t,s)=>{s.d(t,{useStudyStore:()=>o});var n=s(8957),r=s(5914);const a=e=>{const t=[...e];for(let s=t.length-1;s>0;s--){const e=Math.floor(Math.random()*(s+1));[t[s],t[e]]=[t[e],t[s]]}return t},o=(0,r.vt)((e,t)=>({currentSession:null,studySetContent:null,studySets:[],sessions:[],isLoading:!1,error:null,actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1,fetchStudySetContent:async function(s){var n;let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{studySetContent:a}=t();if(r||!a||(null===(n=a.studySet)||void 0===n?void 0:n.id)!==s){e({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),n=await fetch("/api/study-sets/".concat(s,"/content"),{headers:{Authorization:"Bearer ".concat(t)}});if(!n.ok){const e=await n.json();throw new Error(e.error||"Failed to fetch study set content")}const r=await n.json();if(!r.success)throw new Error(r.error);e({studySetContent:{studySet:r.data.studySet,flashcards:r.data.flashcards||[],questions:r.data.questions||[]},isLoading:!1})}catch(o){throw e({error:o.message||"Failed to fetch study set content",isLoading:!1}),o}}},startStudySession:async function(s,r){var o,i,c;let d=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const{studySetContent:l,fetchStudySetContent:u}=t();l&&(null===(o=l.studySet)||void 0===o?void 0:o.id)===s||await u(s);const m=t().studySetContent;if(!m)throw new Error("Failed to load study set content");const x="flashcards"===r?(null===(i=m.flashcards)||void 0===i?void 0:i.length)||0:(null===(c=m.questions)||void 0===c?void 0:c.length)||0;if(0===x)throw new Error("No study materials found in this set");let h;if(d)if(h=Array.from({length:x},(e,t)=>t),"flashcards"===r&&m.flashcards){const t=a(m.flashcards);e(e=>({studySetContent:(0,n.A)((0,n.A)({},e.studySetContent),{},{flashcards:t})}))}else if("quiz"===r&&m.questions){const t=a(m.questions);e(e=>({studySetContent:(0,n.A)((0,n.A)({},e.studySetContent),{},{questions:t})}))}e({currentSession:{studySetId:s,type:r,startTime:new Date,currentIndex:0,totalItems:x,reviewedItems:[],flaggedItems:[],correctAnswers:"quiz"===r?0:void 0,timeSpent:0,isShuffled:d,originalOrder:h}})},endStudySession:()=>{e({currentSession:null})},nextItem:()=>{const{currentSession:s,addToHistory:r}=t();if(!s)return;const a=s.currentIndex===s.totalItems-1?0:s.currentIndex+1;r({type:"NEXT_ITEM",payload:{fromIndex:s.currentIndex,toIndex:a},previousState:{currentIndex:s.currentIndex},timestamp:Date.now()}),e({currentSession:(0,n.A)((0,n.A)({},s),{},{currentIndex:a})})},previousItem:()=>{const{currentSession:s,addToHistory:r}=t();if(!s)return;const a=0===s.currentIndex?s.totalItems-1:s.currentIndex-1;r({type:"PREVIOUS_ITEM",payload:{fromIndex:s.currentIndex,toIndex:a},previousState:{currentIndex:s.currentIndex},timestamp:Date.now()}),e({currentSession:(0,n.A)((0,n.A)({},s),{},{currentIndex:a})})},goToItem:s=>{const{currentSession:r}=t();if(!r)return;const a=Math.max(0,Math.min(s,r.totalItems-1));e({currentSession:(0,n.A)((0,n.A)({},r),{},{currentIndex:a})})},toggleFlag:s=>{const{currentSession:r,addToHistory:a}=t();if(!r)return;const o=r.flaggedItems.includes(s),i=o?r.flaggedItems.filter(e=>e!==s):[...r.flaggedItems,s];a({type:"TOGGLE_FLAG",payload:{itemId:s,wasFlagged:o},previousState:{flaggedItems:r.flaggedItems},timestamp:Date.now()}),e({currentSession:(0,n.A)((0,n.A)({},r),{},{flaggedItems:i})})},markReviewed:s=>{const{currentSession:r}=t();r&&(r.reviewedItems.includes(r.currentIndex)||e({currentSession:(0,n.A)((0,n.A)({},r),{},{reviewedItems:[...r.reviewedItems,r.currentIndex]})}))},submitQuizAnswer:(s,r,a)=>{const{currentSession:o,markReviewed:i}=t();o&&"quiz"===o.type&&(i(s),a&&e({currentSession:(0,n.A)((0,n.A)({},o),{},{correctAnswers:(o.correctAnswers||0)+1})}))},updateTimeSpent:s=>{const{currentSession:r}=t();r&&e({currentSession:(0,n.A)((0,n.A)({},r),{},{timeSpent:r.timeSpent+s})})},addToHistory:s=>{const{actionHistory:n,currentActionIndex:r}=t(),a=n.slice(0,r+1);a.push(s);const o=a.slice(-50);e({actionHistory:o,currentActionIndex:o.length-1,canUndo:o.length>0,canRedo:!1})},undo:()=>{const{actionHistory:s,currentActionIndex:r,currentSession:a}=t();if(r<0||!a)return;const o=s[r];e({currentSession:(0,n.A)((0,n.A)({},a),o.previousState),currentActionIndex:r-1,canUndo:r>0,canRedo:!0})},redo:()=>{const{actionHistory:s,currentActionIndex:n,currentSession:r}=t();if(n>=s.length-1||!r)return;const a=n+1,o=s[a];switch(o.type){case"NEXT_ITEM":t().nextItem();break;case"PREVIOUS_ITEM":t().previousItem();break;case"TOGGLE_FLAG":t().toggleFlag(o.payload.itemId);break;case"MARK_REVIEWED":t().markReviewed(o.payload.itemId)}e({currentActionIndex:a,canUndo:!0,canRedo:a<s.length-1})},clearHistory:()=>{e({actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1})},fetchStudySets:async()=>{e({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/study-sets",{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok){const e=await s.json();throw new Error(e.error||"Failed to fetch study sets")}const n=await s.json();if(!n.success)throw new Error(n.error);e({studySets:n.data,isLoading:!1})}catch(t){throw e({error:t.message||"Failed to fetch study sets",isLoading:!1}),t}},fetchStudySessions:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"30d";e({isLoading:!0,error:null});try{const s=localStorage.getItem("auth_token"),r=await fetch("/api/study-sessions?timeRange=".concat(t),{headers:{Authorization:"Bearer ".concat(s)}});if(!r.ok){const e=await r.json();throw new Error(e.error||"Failed to fetch study sessions")}const a=await r.json();if(!a.success)throw new Error(a.error);{const t=a.data.map(e=>(0,n.A)((0,n.A)({},e),{},{startTime:new Date(e.startTime),endTime:e.endTime?new Date(e.endTime):void 0}));e({sessions:t,isLoading:!1})}}catch(s){throw e({error:s.message||"Failed to fetch study sessions",isLoading:!1}),s}},invalidateStudySetContent:s=>{const{studySetContent:n}=t();var r;s?(null===n||void 0===n||null===(r=n.studySet)||void 0===r?void 0:r.id)===s&&e({studySetContent:null}):e({studySetContent:null})},refreshStudySetContent:async e=>{await t().fetchStudySetContent(e,!0)},invalidateStudySets:()=>{e({studySets:[]})}}))}}]);
//# sourceMappingURL=754.ec00b189.chunk.js.map