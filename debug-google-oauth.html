<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Google OAuth</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .error { background-color: #ffe6e6; border-color: #ff0000; }
        .success { background-color: #e6ffe6; border-color: #00ff00; }
        button { padding: 10px 20px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Google OAuth Debug Tool</h1>
    
    <button onclick="testGoogleOAuth()">Test Google OAuth Endpoint</button>
    <button onclick="testHealthCheck()">Test Backend Health</button>
    
    <div id="results"></div>

    <script>
        async function testHealthCheck() {
            const resultsDiv = document.getElementById('results');
            
            try {
                const response = await fetch('http://localhost:3001/api/health');
                const result = await response.json();
                
                resultsDiv.innerHTML += `
                    <div class="result success">
                        <h3>✅ Health Check Success</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Response:</strong> ${JSON.stringify(result, null, 2)}</p>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="result error">
                        <h3>❌ Health Check Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testGoogleOAuth() {
            const resultsDiv = document.getElementById('results');
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/google', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        redirectTo: 'http://localhost:3000/auth/callback'
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML += `
                        <div class="result success">
                            <h3>✅ Google OAuth Success</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Response:</strong> ${JSON.stringify(result, null, 2)}</p>
                            ${result.url ? `<p><a href="${result.url}" target="_blank">Test OAuth URL</a></p>` : ''}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML += `
                        <div class="result error">
                            <h3>❌ Google OAuth Failed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Error:</strong> ${JSON.stringify(result, null, 2)}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="result error">
                        <h3>❌ Google OAuth Request Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Make sure the backend server is running on port 3001</p>
                    </div>
                `;
            }
        }
        
        // Auto-test on page load
        window.onload = function() {
            testHealthCheck();
        };
    </script>
</body>
</html>
