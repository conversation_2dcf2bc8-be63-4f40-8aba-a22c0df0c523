import { User<PERSON>rofile, AuthR<PERSON><PERSON> } from "../shared/types";
import { safeFetch } from "./connectionService";

class AuthService {
  // Helper methods for token storage
  private setToken(token: string, rememberMe: boolean = true): void {
    // Clear any existing tokens from both storages
    localStorage.removeItem("auth_token");
    sessionStorage.removeItem("auth_token");

    // Store in appropriate storage based on user preference
    if (rememberMe) {
      localStorage.setItem("auth_token", token);
    } else {
      sessionStorage.setItem("auth_token", token);
    }
  }

  private getToken(): string | null {
    // Check localStorage first, then sessionStorage
    return (
      localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token")
    );
  }

  private clearToken(): void {
    localStorage.removeItem("auth_token");
    sessionStorage.removeItem("auth_token");
  }

  async signUp(
    email: string,
    password: string,
    name?: string
  ): Promise<AuthResult> {
    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password, name }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        // Default to persistent storage for signup
        this.setToken(result.token, true);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during signup" };
    }
  }

  async signIn(
    email: string,
    password: string,
    rememberMe: boolean = true
  ): Promise<AuthResult> {
    try {
      const response = await safeFetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        this.setToken(result.token, rememberMe);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during login" };
    }
  }

  async signOut(): Promise<void> {
    try {
      const token = this.getToken();
      if (token) {
        await safeFetch("/api/auth/logout", {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
        });
      }
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const token = this.getToken();
      if (!token) {
        return null;
      }

      const response = await safeFetch("/api/auth/user", {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log("Token expired or invalid, clearing...");
          this.clearToken();
        }
        return null;
      }

      const result = await response.json();

      if (result.success && result.data) {
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("getCurrentUser error:", error);
      return null;
    }
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  async signInWithGoogle(): Promise<{ error?: string }> {
    try {
      const response = await safeFetch("/api/auth/google", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          redirectTo: `${window.location.origin}/auth/callback`,
        }),
      });

      const result = await response.json();

      if (!response.ok || result.error) {
        return { error: result.error || "Failed to initiate Google sign-in" };
      }

      // Redirect to Google OAuth URL provided by backend
      if (result.url) {
        window.location.href = result.url;
        return {};
      }

      return { error: "No OAuth URL provided" };
    } catch (error) {
      return { error: "Failed to sign in with Google" };
    }
  }

  async handleOAuthCallback(): Promise<AuthResult> {
    try {
      // First, try to get authorization code from URL parameters (PKCE flow)
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get("code");
      const state = urlParams.get("state");

      if (code) {
        // PKCE flow - send code to backend for processing
        const response = await safeFetch("/api/auth/oauth-callback", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ code, state }),
        });

        const result = await response.json();

        if (result.success && result.token && result.user) {
          this.setToken(result.token, true);
          return { success: true, user: result.user };
        }

        return {
          success: false,
          error: result.error || "Failed to process OAuth callback",
        };
      }

      // If no code, try to get tokens from URL fragment (implicit flow)
      const fragment = window.location.hash.substring(1);
      const fragmentParams = new URLSearchParams(fragment);

      const accessToken = fragmentParams.get("access_token");
      const refreshToken = fragmentParams.get("refresh_token");
      const expiresIn = fragmentParams.get("expires_in");

      if (!accessToken) {
        return {
          success: false,
          error: "No authorization code or access token received",
        };
      }

      // For implicit flow, send the access token to backend for validation and user profile handling
      const response = await safeFetch("/api/auth/oauth-user", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: expiresIn,
        }),
      });

      const result = await response.json();

      if (result.success && result.user) {
        // Store the token
        this.setToken(accessToken, true);
        return { success: true, user: result.user };
      }

      return {
        success: false,
        error: result.error || "Failed to process OAuth tokens",
      };
    } catch (error) {
      return { success: false, error: "Failed to handle OAuth callback" };
    }
  }
}

export const authService = new AuthService();
