# Phase 11: Study Interfaces
**Priority**: CRITICAL - Core study functionality for flashcards and quizzes
**Dependencies**: Phase 1 (Foundation), Phase 4 (Auth Components), Phase 8 (Study Set Management), Phase 10 (AI Generation)
**Estimated Time**: 5-6 hours

## Overview
Create interactive study interfaces for flashcard review and quiz taking with progress tracking, keyboard navigation, and responsive design.

## Tasks

### 11.1 Study Store (Zustand)
**File**: `frontend/src/stores/studyStore.ts`

```typescript
import { create } from 'zustand';
import { StudySet, Flashcard, QuizQuestion } from '../../../shared/types';

interface StudySession {
  studySetId: string;
  type: 'flashcards' | 'quiz';
  startTime: Date;
  currentIndex: number;
  totalItems: number;
  reviewedItems: number[];
  flaggedItems: string[];
  correctAnswers?: number; // For quizzes
  timeSpent: number; // in seconds
}

interface StudyState {
  currentSession: StudySession | null;
  studySetContent: {
    studySet?: StudySet;
    flashcards?: Flashcard[];
    questions?: QuizQuestion[];
  } | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  startStudySession: (studySetId: string, type: 'flashcards' | 'quiz') => Promise<void>;
  endStudySession: () => void;
  nextItem: () => void;
  previousItem: () => void;
  goToItem: (index: number) => void;
  toggleFlag: (itemId: string) => void;
  markReviewed: (itemId: string) => void;
  submitQuizAnswer: (questionId: string, answer: string[], isCorrect: boolean) => void;
  updateTimeSpent: (seconds: number) => void;
  fetchStudySetContent: (studySetId: string) => Promise<void>;
}

export const useStudyStore = create<StudyState>((set, get) => ({
  currentSession: null,
  studySetContent: null,
  isLoading: false,
  error: null,

  fetchStudySetContent: async (studySetId: string) => {
    set({ isLoading: true, error: null });

    try {
      const token = localStorage.getItem('auth_token');

      const response = await fetch(`/api/study-sets/${studySetId}/content`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.error || 'Failed to fetch study set content');
      }

      const result = await response.json();

      if (result.success) {
        set({
          studySetContent: {
            studySet: result.data.studySet,
            flashcards: result.data.flashcards || [],
            questions: result.data.questions || [],
          },
          isLoading: false
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch study set content',
        isLoading: false
      });
      throw error;
    }
  },

  startStudySession: async (studySetId: string, type: 'flashcards' | 'quiz') => {
    const { studySetContent, fetchStudySetContent } = get();

    // Fetch content if not already loaded
    if (!studySetContent || studySetContent.studySet?.id !== studySetId) {
      await fetchStudySetContent(studySetId);
    }

    const content = get().studySetContent;
    if (!content) {
      throw new Error('Failed to load study set content');
    }

    const totalItems = type === 'flashcards'
      ? content.flashcards?.length || 0
      : content.questions?.length || 0;

    if (totalItems === 0) {
      throw new Error('No study materials found in this set');
    }

    set({
      currentSession: {
        studySetId,
        type,
        startTime: new Date(),
        currentIndex: 0,
        totalItems,
        reviewedItems: [],
        flaggedItems: [],
        correctAnswers: type === 'quiz' ? 0 : undefined,
        timeSpent: 0
      }
    });
  },

  endStudySession: () => {
    set({ currentSession: null });
  },

  nextItem: () => {
    const { currentSession } = get();
    if (!currentSession) return;

    const nextIndex = Math.min(currentSession.currentIndex + 1, currentSession.totalItems - 1);
    set({
      currentSession: {
        ...currentSession,
        currentIndex: nextIndex
      }
    });
  },

  previousItem: () => {
    const { currentSession } = get();
    if (!currentSession) return;

    const prevIndex = Math.max(currentSession.currentIndex - 1, 0);
    set({
      currentSession: {
        ...currentSession,
        currentIndex: prevIndex
      }
    });
  },

  goToItem: (index: number) => {
    const { currentSession } = get();
    if (!currentSession) return;

    const clampedIndex = Math.max(0, Math.min(index, currentSession.totalItems - 1));
    set({
      currentSession: {
        ...currentSession,
        currentIndex: clampedIndex
      }
    });
  },

  toggleFlag: (itemId: string) => {
    const { currentSession } = get();
    if (!currentSession) return;

    const flaggedItems = currentSession.flaggedItems.includes(itemId)
      ? currentSession.flaggedItems.filter(id => id !== itemId)
      : [...currentSession.flaggedItems, itemId];

    set({
      currentSession: {
        ...currentSession,
        flaggedItems
      }
    });
  },

  markReviewed: (itemId: string) => {
    const { currentSession } = get();
    if (!currentSession) return;

    if (!currentSession.reviewedItems.includes(currentSession.currentIndex)) {
      set({
        currentSession: {
          ...currentSession,
          reviewedItems: [...currentSession.reviewedItems, currentSession.currentIndex]
        }
      });
    }
  },

  submitQuizAnswer: (questionId: string, answer: string[], isCorrect: boolean) => {
    const { currentSession, markReviewed } = get();
    if (!currentSession || currentSession.type !== 'quiz') return;

    markReviewed(questionId);

    if (isCorrect) {
      set({
        currentSession: {
          ...currentSession,
          correctAnswers: (currentSession.correctAnswers || 0) + 1
        }
      });
    }
  },

  updateTimeSpent: (seconds: number) => {
    const { currentSession } = get();
    if (!currentSession) return;

    set({
      currentSession: {
        ...currentSession,
        timeSpent: currentSession.timeSpent + seconds
      }
    });
  }
}));
```

### 11.2 Study Set Detail Page
**File**: `frontend/src/pages/StudySetPage.tsx`

```typescript
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useStudyStore } from '../stores/studyStore';
import { Button } from '../components/common/Button';

export const StudySetPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { studySetContent, isLoading, error, fetchStudySetContent } = useStudyStore();
  const [selectedMode, setSelectedMode] = useState<'flashcards' | 'quiz' | null>(null);

  useEffect(() => {
    if (id) {
      fetchStudySetContent(id).catch(console.error);
    }
  }, [id, fetchStudySetContent]);

  const handleStartStudy = async () => {
    if (!id || !selectedMode) return;

    try {
      await useStudyStore.getState().startStudySession(id, selectedMode);
      navigate(`/study/${id}/${selectedMode}`);
    } catch (error: any) {
      alert(error.message || 'Failed to start study session');
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          <span className="ml-3 text-gray-400">Loading study set...</span>
        </div>
      </div>
    );
  }

  if (error || !studySetContent?.studySet) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-red-400 mb-4">
            {error || 'Study set not found'}
          </div>
          <Button onClick={() => navigate('/dashboard')} variant="secondary">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const { studySet, flashcards, questions } = studySetContent;
  const hasFlashcards = flashcards && flashcards.length > 0;
  const hasQuestions = questions && questions.length > 0;

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/dashboard')}
          className="text-gray-400 hover:text-white mb-4 flex items-center"
        >
          ← Back to Dashboard
        </button>

        <h1 className="text-3xl font-bold text-white mb-2">{studySet.name}</h1>

        <div className="flex items-center space-x-4 text-sm text-gray-400">
          <span className="capitalize">{studySet.type}</span>
          {studySet.is_ai_generated && (
            <span className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
              AI Generated
            </span>
          )}
          <span>
            Created {new Date(studySet.created_at).toLocaleDateString()}
          </span>
        </div>
      </div>

      {/* Study Mode Selection */}
      <div className="bg-background-secondary rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold text-white mb-4">Choose Study Mode</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* Flashcard Mode */}
          {hasFlashcards && (
            <div
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${selectedMode === 'flashcards'
                  ? 'border-primary-500 bg-primary-500/10'
                  : 'border-gray-600 hover:border-gray-500'
                }
              `}
              onClick={() => setSelectedMode('flashcards')}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">🃏</div>
                <div>
                  <h3 className="font-medium text-white">Flashcard Review</h3>
                  <p className="text-sm text-gray-400">
                    {flashcards?.length} flashcards • Interactive review
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Quiz Mode */}
          {hasQuestions && (
            <div
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${selectedMode === 'quiz'
                  ? 'border-primary-500 bg-primary-500/10'
                  : 'border-gray-600 hover:border-gray-500'
                }
              `}
              onClick={() => setSelectedMode('quiz')}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">📝</div>
                <div>
                  <h3 className="font-medium text-white">Quiz Practice</h3>
                  <p className="text-sm text-gray-400">
                    {questions?.length} questions • Test your knowledge
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Start Button */}
        <Button
          onClick={handleStartStudy}
          disabled={!selectedMode}
          className="w-full"
          size="lg"
        >
          {selectedMode
            ? `Start ${selectedMode === 'flashcards' ? 'Flashcard Review' : 'Quiz Practice'}`
            : 'Select a study mode'
          }
        </Button>
      </div>

      {/* Study Set Info */}
      <div className="bg-background-secondary rounded-lg p-6">
        <h3 className="text-lg font-medium text-white mb-4">Study Set Details</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-300 mb-2">Content</h4>
            <div className="space-y-1 text-sm text-gray-400">
              {hasFlashcards && (
                <div>{flashcards?.length} flashcards</div>
              )}
              {hasQuestions && (
                <div>{questions?.length} quiz questions</div>
              )}
            </div>
          </div>

          {studySet.source_documents && studySet.source_documents.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">Source Documents</h4>
              <div className="space-y-1 text-sm text-gray-400">
                {studySet.source_documents.map((doc, index) => (
                  <div key={index}>{doc.filename}</div>
                ))}
              </div>
            </div>
          )}

          {studySet.custom_prompt && (
            <div className="md:col-span-2">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Custom Instructions</h4>
              <p className="text-sm text-gray-400">{studySet.custom_prompt}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
```

### 11.3 Flashcard Study Interface
**File**: `frontend/src/components/study/FlashcardInterface.tsx`

```typescript
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudyStore } from '../../stores/studyStore';
import { Button } from '../common/Button';

export const FlashcardInterface: React.FC = () => {
  const navigate = useNavigate();
  const {
    currentSession,
    studySetContent,
    nextItem,
    previousItem,
    goToItem,
    toggleFlag,
    markReviewed,
    endStudySession,
    updateTimeSpent
  } = useStudyStore();

  const [isFlipped, setIsFlipped] = useState(false);
  const [startTime, setStartTime] = useState(Date.now());

  useEffect(() => {
    const interval = setInterval(() => {
      updateTimeSpent(1);
    }, 1000);

    return () => clearInterval(interval);
  }, [updateTimeSpent]);

  useEffect(() => {
    // Reset flip state when moving to new card
    setIsFlipped(false);
    setStartTime(Date.now());
  }, [currentSession?.currentIndex]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        handlePrevious();
      } else if (e.key === 'ArrowRight') {
        handleNext();
      } else if (e.key === ' ') {
        e.preventDefault();
        setIsFlipped(!isFlipped);
      } else if (e.key === 'f') {
        handleToggleFlag();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isFlipped]);

  if (!currentSession || !studySetContent?.flashcards) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">No flashcard session found</div>
          <Button onClick={() => navigate('/dashboard')} variant="secondary">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const flashcards = studySetContent.flashcards;
  const currentFlashcard = flashcards[currentSession.currentIndex];
  const progress = ((currentSession.currentIndex + 1) / currentSession.totalItems) * 100;
  const isFirstCard = currentSession.currentIndex === 0;
  const isLastCard = currentSession.currentIndex === currentSession.totalItems - 1;
  const isFlagged = currentSession.flaggedItems.includes(currentFlashcard.id);

  const handleNext = () => {
    if (isFlipped) {
      markReviewed(currentFlashcard.id);
    }

    if (isLastCard) {
      handleFinishStudy();
    } else {
      nextItem();
    }
  };

  const handlePrevious = () => {
    if (!isFirstCard) {
      previousItem();
    }
  };

  const handleToggleFlag = () => {
    toggleFlag(currentFlashcard.id);
  };

  const handleFinishStudy = () => {
    const timeSpent = Math.floor((Date.now() - startTime) / 1000);
    const reviewedCount = currentSession.reviewedItems.length;
    const flaggedCount = currentSession.flaggedItems.length;

    endStudySession();

    // Show completion modal or navigate with results
    alert(`Study session complete!\n\nReviewed: ${reviewedCount}/${currentSession.totalItems} cards\nFlagged: ${flaggedCount} cards\nTime spent: ${Math.floor(timeSpent / 60)}m ${timeSpent % 60}s`);

    navigate(`/study-sets/${currentSession.studySetId}`);
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigate(`/study-sets/${currentSession.studySetId}`)}
          className="text-gray-400 hover:text-white flex items-center"
        >
          ← Back to Study Set
        </button>

        <div className="text-center">
          <h1 className="text-xl font-semibold text-white">
            {studySetContent.studySet?.name}
          </h1>
          <p className="text-sm text-gray-400">
            Card {currentSession.currentIndex + 1} of {currentSession.totalItems}
          </p>
        </div>

        <Button
          onClick={handleFinishStudy}
          variant="secondary"
          size="sm"
        >
          Finish
        </Button>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>Progress: {Math.round(progress)}%</span>
          <span>Time: {Math.floor(currentSession.timeSpent / 60)}:{(currentSession.timeSpent % 60).toString().padStart(2, '0')}</span>
        </div>
      </div>

      {/* Flashcard */}
      <div className="mb-8">
        <div
          className={`
            relative w-full h-96 cursor-pointer transition-transform duration-500 transform-style-preserve-3d
            ${isFlipped ? 'rotate-y-180' : ''}
          `}
          onClick={() => setIsFlipped(!isFlipped)}
        >
          {/* Front of card */}
          <div className={`
            absolute inset-0 w-full h-full backface-hidden
            bg-background-secondary border border-gray-600 rounded-lg p-8
            flex items-center justify-center text-center
            ${isFlipped ? 'rotate-y-180' : ''}
          `}>
            <div>
              <div className="text-sm text-gray-400 mb-4">FRONT</div>
              <div className="text-xl text-white leading-relaxed">
                {currentFlashcard.front}
              </div>
              {!isFlipped && (
                <div className="text-sm text-gray-500 mt-6">
                  Click to reveal answer
                </div>
              )}
            </div>
          </div>

          {/* Back of card */}
          <div className={`
            absolute inset-0 w-full h-full backface-hidden rotate-y-180
            bg-primary-500/10 border border-primary-500/30 rounded-lg p-8
            flex items-center justify-center text-center
            ${isFlipped ? '' : 'rotate-y-180'}
          `}>
            <div>
              <div className="text-sm text-primary-400 mb-4">BACK</div>
              <div className="text-xl text-white leading-relaxed">
                {currentFlashcard.back}
              </div>
              {isFlipped && (
                <div className="text-sm text-gray-500 mt-6">
                  Click to flip back
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <Button
          onClick={handlePrevious}
          disabled={isFirstCard}
          variant="secondary"
        >
          ← Previous
        </Button>

        <div className="flex items-center space-x-4">
          <Button
            onClick={handleToggleFlag}
            variant={isFlagged ? "primary" : "secondary"}
            size="sm"
          >
            {isFlagged ? '🚩 Flagged' : '🏳️ Flag'}
          </Button>

          <Button
            onClick={() => setIsFlipped(!isFlipped)}
            variant="secondary"
          >
            {isFlipped ? 'Show Front' : 'Show Back'}
          </Button>
        </div>

        <Button
          onClick={handleNext}
          variant="primary"
        >
          {isLastCard ? 'Finish' : 'Next →'}
        </Button>
      </div>

      {/* Keyboard shortcuts */}
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>Keyboard shortcuts: ← → (navigate) • Space (flip) • F (flag)</p>
      </div>
    </div>
  );
};
```

### 11.4 Quiz Study Interface
**File**: `frontend/src/components/study/QuizInterface.tsx`

```typescript
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudyStore } from '../../stores/studyStore';
import { QuizQuestion, QuestionType } from '../../../../shared/types';
import { Button } from '../common/Button';

interface QuestionAnswer {
  questionId: string;
  selectedAnswers: string[];
  isCorrect: boolean;
  timeSpent: number;
}

export const QuizInterface: React.FC = () => {
  const navigate = useNavigate();
  const {
    currentSession,
    studySetContent,
    nextItem,
    previousItem,
    submitQuizAnswer,
    endStudySession,
    updateTimeSpent
  } = useStudyStore();

  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [answers, setAnswers] = useState<QuestionAnswer[]>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      updateTimeSpent(1);
    }, 1000);

    return () => clearInterval(interval);
  }, [updateTimeSpent]);

  useEffect(() => {
    // Reset state when moving to new question
    setSelectedAnswers([]);
    setHasAnswered(false);
    setShowExplanation(false);
    setQuestionStartTime(Date.now());
  }, [currentSession?.currentIndex]);

  if (!currentSession || !studySetContent?.questions) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">No quiz session found</div>
          <Button onClick={() => navigate('/dashboard')} variant="secondary">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const questions = studySetContent.questions;
  const currentQuestion = questions[currentSession.currentIndex];
  const progress = ((currentSession.currentIndex + 1) / currentSession.totalItems) * 100;
  const isLastQuestion = currentSession.currentIndex === currentSession.totalItems - 1;

  const handleAnswerSelect = (answer: string) => {
    if (hasAnswered) return;

    if (currentQuestion.question_type === 'multiple_choice' || currentQuestion.question_type === 'true_false') {
      setSelectedAnswers([answer]);
    } else if (currentQuestion.question_type === 'select_all') {
      setSelectedAnswers(prev =>
        prev.includes(answer)
          ? prev.filter(a => a !== answer)
          : [...prev, answer]
      );
    }
  };

  const handleShortAnswerChange = (value: string) => {
    if (hasAnswered) return;
    setSelectedAnswers([value]);
  };

  const handleSubmitAnswer = () => {
    if (hasAnswered || selectedAnswers.length === 0) return;

    const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
    const isCorrect = checkAnswer();

    const questionAnswer: QuestionAnswer = {
      questionId: currentQuestion.id,
      selectedAnswers: [...selectedAnswers],
      isCorrect,
      timeSpent
    };

    setAnswers(prev => [...prev, questionAnswer]);
    submitQuizAnswer(currentQuestion.id, selectedAnswers, isCorrect);
    setHasAnswered(true);
    setShowExplanation(true);
  };

  const checkAnswer = (): boolean => {
    const correctAnswers = currentQuestion.correct_answers;

    if (currentQuestion.question_type === 'short_answer') {
      // For short answer, check if any correct answer is contained in the user's answer
      const userAnswer = selectedAnswers[0]?.toLowerCase().trim() || '';
      return correctAnswers.some(correct =>
        userAnswer.includes(correct.toLowerCase().trim()) ||
        correct.toLowerCase().trim().includes(userAnswer)
      );
    } else {
      // For other types, exact match required
      return selectedAnswers.length === correctAnswers.length &&
             selectedAnswers.every(answer => correctAnswers.includes(answer));
    }
  };

  const handleNext = () => {
    if (isLastQuestion) {
      handleFinishQuiz();
    } else {
      nextItem();
    }
  };

  const handleFinishQuiz = () => {
    const totalQuestions = currentSession.totalItems;
    const correctAnswers = currentSession.correctAnswers || 0;
    const percentage = Math.round((correctAnswers / totalQuestions) * 100);
    const timeSpent = currentSession.timeSpent;

    endStudySession();

    // Show results modal or navigate with results
    alert(`Quiz complete!\n\nScore: ${correctAnswers}/${totalQuestions} (${percentage}%)\nTime spent: ${Math.floor(timeSpent / 60)}m ${timeSpent % 60}s`);

    navigate(`/study-sets/${currentSession.studySetId}`);
  };

  const renderQuestionOptions = () => {
    if (!currentQuestion.options) return null;

    return currentQuestion.options.map((option, index) => {
      const isSelected = selectedAnswers.includes(option);
      const isCorrect = currentQuestion.correct_answers.includes(option);

      let buttonClass = 'w-full text-left p-4 rounded-lg border transition-all ';

      if (hasAnswered) {
        if (isCorrect) {
          buttonClass += 'border-green-500 bg-green-500/20 text-green-400';
        } else if (isSelected && !isCorrect) {
          buttonClass += 'border-red-500 bg-red-500/20 text-red-400';
        } else {
          buttonClass += 'border-gray-600 bg-background-secondary text-gray-400';
        }
      } else {
        if (isSelected) {
          buttonClass += 'border-primary-500 bg-primary-500/20 text-primary-400';
        } else {
          buttonClass += 'border-gray-600 bg-background-secondary text-white hover:border-gray-500';
        }
      }

      return (
        <button
          key={index}
          onClick={() => handleAnswerSelect(option)}
          disabled={hasAnswered}
          className={buttonClass}
        >
          <div className="flex items-center space-x-3">
            <div className={`
              w-5 h-5 rounded border-2 flex items-center justify-center
              ${isSelected ? 'border-current' : 'border-gray-500'}
            `}>
              {isSelected && (
                <div className="w-2 h-2 rounded bg-current"></div>
              )}
            </div>
            <span>{option}</span>
          </div>
        </button>
      );
    });
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigate(`/study-sets/${currentSession.studySetId}`)}
          className="text-gray-400 hover:text-white flex items-center"
        >
          ← Back to Study Set
        </button>

        <div className="text-center">
          <h1 className="text-xl font-semibold text-white">
            {studySetContent.studySet?.name}
          </h1>
          <p className="text-sm text-gray-400">
            Question {currentSession.currentIndex + 1} of {currentSession.totalItems}
          </p>
        </div>

        <Button
          onClick={handleFinishQuiz}
          variant="secondary"
          size="sm"
        >
          Finish Quiz
        </Button>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>Progress: {Math.round(progress)}%</span>
          <span>Score: {currentSession.correctAnswers || 0}/{currentSession.currentIndex + (hasAnswered ? 1 : 0)}</span>
          <span>Time: {Math.floor(currentSession.timeSpent / 60)}:{(currentSession.timeSpent % 60).toString().padStart(2, '0')}</span>
        </div>
      </div>

      {/* Question */}
      <div className="bg-background-secondary rounded-lg p-6 mb-6">
        <div className="mb-4">
          <span className="text-sm text-gray-400 uppercase tracking-wide">
            {currentQuestion.question_type.replace('_', ' ')}
          </span>
        </div>

        <h2 className="text-xl text-white mb-6 leading-relaxed">
          {currentQuestion.question_text}
        </h2>

        {/* Answer Options */}
        <div className="space-y-3">
          {currentQuestion.question_type === 'short_answer' ? (
            <textarea
              value={selectedAnswers[0] || ''}
              onChange={(e) => handleShortAnswerChange(e.target.value)}
              disabled={hasAnswered}
              placeholder="Type your answer here..."
              rows={3}
              className="w-full px-4 py-3 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
            />
          ) : (
            renderQuestionOptions()
          )}
        </div>

        {/* Submit Button */}
        {!hasAnswered && (
          <div className="mt-6">
            <Button
              onClick={handleSubmitAnswer}
              disabled={selectedAnswers.length === 0}
              className="w-full"
            >
              Submit Answer
            </Button>
          </div>
        )}

        {/* Explanation */}
        {hasAnswered && showExplanation && currentQuestion.explanation && (
          <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
            <h4 className="text-blue-400 font-medium mb-2">Explanation</h4>
            <p className="text-gray-300">{currentQuestion.explanation}</p>
          </div>
        )}
      </div>

      {/* Navigation */}
      {hasAnswered && (
        <div className="flex justify-center">
          <Button
            onClick={handleNext}
            variant="primary"
            size="lg"
          >
            {isLastQuestion ? 'Finish Quiz' : 'Next Question →'}
          </Button>
        </div>
      )}
    </div>
  );
};
```

### 11.5 Study Interface Router Page
**File**: `frontend/src/pages/StudyPage.tsx`

```typescript
import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useStudyStore } from '../stores/studyStore';
import { FlashcardInterface } from '../components/study/FlashcardInterface';
import { QuizInterface } from '../components/study/QuizInterface';

export const StudyPage: React.FC = () => {
  const { id, mode } = useParams<{ id: string; mode: 'flashcards' | 'quiz' }>();
  const navigate = useNavigate();
  const { currentSession, startStudySession } = useStudyStore();

  useEffect(() => {
    // If no active session or session doesn't match URL, start new session
    if (!currentSession ||
        currentSession.studySetId !== id ||
        currentSession.type !== mode) {

      if (id && mode && (mode === 'flashcards' || mode === 'quiz')) {
        startStudySession(id, mode).catch((error) => {
          console.error('Failed to start study session:', error);
          alert(error.message || 'Failed to start study session');
          navigate(`/study-sets/${id}`);
        });
      } else {
        navigate('/dashboard');
      }
    }
  }, [id, mode, currentSession, startStudySession, navigate]);

  if (!currentSession) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          <span className="ml-3 text-gray-400">Starting study session...</span>
        </div>
      </div>
    );
  }

  if (mode === 'flashcards') {
    return <FlashcardInterface />;
  } else if (mode === 'quiz') {
    return <QuizInterface />;
  } else {
    navigate('/dashboard');
    return null;
  }
};
```

### 11.6 CSS Additions for Flashcard Flip Animation
**File**: `frontend/src/index.css` (Add these styles)

```css
/* Flashcard flip animation styles */
.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Ensure proper 3D perspective for parent container */
.flashcard-container {
  perspective: 1000px;
}
```

## Acceptance Criteria
- [ ] Study store manages study sessions and progress tracking
- [ ] Study set detail page shows content overview and mode selection
- [ ] Flashcard interface with flip animations and keyboard navigation
- [ ] Quiz interface supporting all question types (multiple choice, select all, true/false, short answer)
- [ ] Progress tracking with time spent and completion statistics
- [ ] Flagging system for flashcards
- [ ] Immediate feedback for quiz answers with explanations
- [ ] Keyboard shortcuts for flashcard navigation (arrows, space, f)
- [ ] Responsive design for all screen sizes
- [ ] Session completion with results summary
- [ ] Navigation between study modes and back to study set
- [ ] Error handling for missing content or failed sessions

## Routes to Add to App.tsx
```typescript
// Add these routes to the existing Routes component:
<Route
  path="/study-sets/:id"
  element={
    <ProtectedRoute>
      <StudySetPage />
    </ProtectedRoute>
  }
/>
<Route
  path="/study/:id/:mode"
  element={
    <ProtectedRoute>
      <StudyPage />
    </ProtectedRoute>
  }
/>
```

## Next Phase Dependencies
- Phase 12 (Study Set Management Frontend) requires study set listing and management
- Phase 13 (Dashboard Enhancements) requires study progress integration
- Phase 14 (Testing & Deployment) requires comprehensive testing of study flows