import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useStudyStore } from '../stores/studyStore';
import { useUserSettings } from '../hooks/useUserSettings';
import { FlashcardInterface } from '../components/study/FlashcardInterface';
import { QuizInterface } from '../components/study/QuizInterface';

export const StudyPage: React.FC = () => {
  const { id, mode } = useParams<{ id: string; mode: 'flashcards' | 'quiz' }>();
  const navigate = useNavigate();
  const { currentSession, startStudySession } = useStudyStore();
  const { settings: userSettings } = useUserSettings();

  useEffect(() => {
    // If no active session or session doesn't match URL, start new session
    if (!currentSession || 
        currentSession.studySetId !== id || 
        currentSession.type !== mode) {
      
      if (id && mode && (mode === 'flashcards' || mode === 'quiz')) {
        const shuffleEnabled = userSettings?.shuffle_flashcards || false;
        startStudySession(id, mode, shuffleEnabled).catch((error) => {
          console.error('Failed to start study session:', error);
          alert(error.message || 'Failed to start study session');
          navigate(`/study-sets/${id}`);
        });
      } else {
        navigate('/dashboard');
      }
    }
  }, [id, mode, currentSession, startStudySession, navigate, userSettings]);

  if (!currentSession) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          <span className="ml-3 text-gray-400">Starting study session...</span>
        </div>
      </div>
    );
  }

  if (mode === 'flashcards') {
    return <FlashcardInterface />;
  } else if (mode === 'quiz') {
    return <QuizInterface />;
  } else {
    navigate('/dashboard');
    return null;
  }
};
