import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eld<PERSON>he<PERSON>,
  HiCreditCard,
  HiMoon,
  HiSun,
  HiKey,
  HiUpload,
  HiDatabase,
  HiExclamationCircle,
  HiCheckCircle,
  HiLogout,
} from "react-icons/hi";
import { Button } from "../components/common/Button";
import { Input } from "../components/common/Input";
import { useAuthStore } from "../stores/authStore";
import { SubscriptionManagement } from "../components/settings/SubscriptionManagement";
import { DataManagement } from "../components/settings/DataManagement";
import { EnhancedBilling } from "../components/settings/EnhancedBilling";
import { TwoFactorAuth } from "../components/settings/TwoFactorAuth";
import { ChangePasswordModal } from "../components/settings/ChangePasswordModal";
import { DangerZoneModal } from "../components/settings/DangerZoneModal";
import { DifficultyLevel } from "../shared/types";
import { DifficultySelector } from "../components/common/DifficultySelector";
import { useNavigate } from "react-router-dom";

interface SettingsSection {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const settingsSections: SettingsSection[] = [
  {
    id: "profile",
    label: "Profile",
    icon: HiUser,
    description: "Manage your account information",
  },
  {
    id: "preferences",
    label: "Preferences",
    icon: HiCog,
    description: "Customize your experience",
  },
  {
    id: "notifications",
    label: "Notifications",
    icon: HiBell,
    description: "Control notification settings",
  },
  {
    id: "security",
    label: "Security",
    icon: HiShieldCheck,
    description: "Password and security settings",
  },
  {
    id: "subscription",
    label: "Subscription",
    icon: HiCreditCard,
    description: "Manage your subscription plan",
  },
  {
    id: "billing",
    label: "Billing",
    icon: HiCreditCard,
    description: "Payment history and invoices",
  },
  {
    id: "data",
    label: "Data Management",
    icon: HiDatabase,
    description: "Export, import, and manage your data",
  },
];

export const SettingsPage: React.FC = () => {
  const [activeSection, setActiveSection] = useState("profile");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();

  // Form states
  const [profileData, setProfileData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    bio: "",
    avatar: null as File | null,
  });

  const [preferences, setPreferences] = useState({
    theme: "dark",
    language: "en",
    studyReminders: true,
    autoSave: true,
    defaultStudyMode: "flashcards" as "flashcards" | "quiz",
    sessionDuration: 30,
    difficultyLevel: DifficultyLevel.MEDIUM,
  });

  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    studyReminders: true,
    weeklyProgress: false,
    marketingEmails: false,
    achievementNotifications: true,
    streakReminders: true,
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    loginNotifications: true,
    sessionTimeout: 30,
  });

  // Modal states
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =
    useState(false);
  const [isDangerZoneModalOpen, setIsDangerZoneModalOpen] = useState(false);
  const [dangerZoneAction, setDangerZoneAction] = useState<
    "deactivate" | "delete"
  >("deactivate");

  // Load user settings on component mount
  useEffect(() => {
    const loadUserSettings = async () => {
      try {
        setIsLoading(true);

        const token = localStorage.getItem("auth_token");
        if (!token) {
          setError("Authentication required");
          return;
        }

        // Fetch user preferences from API
        const response = await fetch("/api/user/preferences", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to load user preferences");
        }

        const result = await response.json();
        if (result.success && result.data) {
          // Convert snake_case from backend to camelCase for frontend
          const backendPrefs = result.data;
          setPreferences({
            theme: backendPrefs.theme,
            language: backendPrefs.language,
            studyReminders: backendPrefs.study_reminders,
            autoSave: backendPrefs.auto_save,
            defaultStudyMode: backendPrefs.default_study_mode,
            sessionDuration: backendPrefs.session_duration,
            difficultyLevel: backendPrefs.difficulty_level,
          });
        }
      } catch (err) {
        setError("Failed to load user settings");
        console.error("Load user settings error:", err);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserSettings();
  }, []);

  const handleSaveProfile = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const formData = new FormData();
      formData.append("name", profileData.name);
      formData.append("bio", profileData.bio);
      if (profileData.avatar) {
        formData.append("avatar", profileData.avatar);
      }

      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/user/profile", {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to update profile");
      }

      const result = await response.json();
      if (result.success) {
        setSuccess("Profile updated successfully!");
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSavePreferences = async () => {
    // Guard against duplicate submissions
    if (isLoading) return;
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem("auth_token");
      // Abort if user is not authenticated to avoid unauthenticated rate-limited requests
      if (!token) {
        setError("Authentication required");
        setIsLoading(false);
        return;
      }

      // Convert camelCase to snake_case for backend API
      const backendPreferences = {
        theme: preferences.theme,
        language: preferences.language,
        study_reminders: preferences.studyReminders,
        auto_save: preferences.autoSave,
        default_study_mode: preferences.defaultStudyMode,
        session_duration: preferences.sessionDuration,
        difficulty_level: preferences.difficultyLevel,
      };

      const response = await fetch("/api/user/preferences", {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(backendPreferences),
      });

      if (!response.ok) {
        throw new Error("Failed to update preferences");
      }

      const result = await response.json();
      if (result.success) {
        setSuccess("Preferences updated successfully!");
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to update preferences"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveNotifications = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/user/notifications", {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(notifications),
      });

      if (!response.ok) {
        throw new Error("Failed to update notification settings");
      }

      const result = await response.json();
      if (result.success) {
        setSuccess("Notification settings updated successfully!");
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to update notification settings"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await logout();
      navigate("/login");
    } catch (error) {
      setError("Failed to logout. Please try again.");
      setIsLoading(false);
    }
  };

  const renderProfileSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">
          Profile Information
        </h3>

        {/* Avatar Upload */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-3">
            Profile Picture
          </label>
          <div className="flex items-center space-x-4">
            <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
              {user?.name?.charAt(0)?.toUpperCase() || "U"}
            </div>
            <div className="space-y-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => {
                  const input = document.createElement("input");
                  input.type = "file";
                  input.accept = "image/*";
                  input.onchange = (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0];
                    if (file) {
                      setProfileData({ ...profileData, avatar: file });
                    }
                  };
                  input.click();
                }}
              >
                <HiUpload className="w-4 h-4 mr-2" />
                Upload Photo
              </Button>
              <p className="text-xs text-gray-500">JPG, PNG up to 5MB</p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <Input
            label="Full Name"
            value={profileData.name}
            onChange={(value) =>
              setProfileData({ ...profileData, name: value })
            }
            placeholder="Enter your full name"
          />
          <Input
            label="Email Address"
            type="email"
            value={profileData.email}
            onChange={(value) =>
              setProfileData({ ...profileData, email: value })
            }
            placeholder="Enter your email"
            disabled
          />
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Bio (Optional)
            </label>
            <textarea
              value={profileData.bio}
              onChange={(e) =>
                setProfileData({ ...profileData, bio: e.target.value })
              }
              placeholder="Tell us about yourself..."
              rows={4}
              className="w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>
        <div className="mt-6">
          <Button onClick={handleSaveProfile} isLoading={isLoading}>
            Save Profile
          </Button>
        </div>
      </div>
    </div>
  );

  const renderPreferencesSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">
          App Preferences
        </h3>
        <div className="space-y-6">
          {/* Theme Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Theme
            </label>
            <div className="flex space-x-4">
              <button
                onClick={() =>
                  setPreferences({ ...preferences, theme: "dark" })
                }
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  preferences.theme === "dark"
                    ? "border-primary-500 bg-primary-500/20 text-primary-400"
                    : "border-gray-600 text-gray-300 hover:border-gray-500"
                }`}
              >
                <HiMoon className="w-4 h-4" />
                <span>Dark</span>
              </button>
              <button
                onClick={() =>
                  setPreferences({ ...preferences, theme: "light" })
                }
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  preferences.theme === "light"
                    ? "border-primary-500 bg-primary-500/20 text-primary-400"
                    : "border-gray-600 text-gray-300 hover:border-gray-500"
                }`}
                disabled
              >
                <HiSun className="w-4 h-4" />
                <span>Light (Coming Soon)</span>
              </button>
            </div>
          </div>

          {/* Language Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Language
            </label>
            <select
              value={preferences.language}
              onChange={(e) =>
                setPreferences({ ...preferences, language: e.target.value })
              }
              className="w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="en">English</option>
              <option value="es" disabled>
                Spanish (Coming Soon)
              </option>
              <option value="fr" disabled>
                French (Coming Soon)
              </option>
            </select>
          </div>

          {/* Study Preferences */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Default Study Mode
            </label>
            <div className="flex space-x-4">
              <button
                onClick={() =>
                  setPreferences({
                    ...preferences,
                    defaultStudyMode: "flashcards",
                  })
                }
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  preferences.defaultStudyMode === "flashcards"
                    ? "border-primary-500 bg-primary-500/20 text-primary-400"
                    : "border-gray-600 text-gray-300 hover:border-gray-500"
                }`}
              >
                <span>Flashcards</span>
              </button>
              <button
                onClick={() =>
                  setPreferences({ ...preferences, defaultStudyMode: "quiz" })
                }
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  preferences.defaultStudyMode === "quiz"
                    ? "border-primary-500 bg-primary-500/20 text-primary-400"
                    : "border-gray-600 text-gray-300 hover:border-gray-500"
                }`}
              >
                <span>Quiz</span>
              </button>
            </div>
          </div>

          {/* Session Duration */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Default Session Duration (minutes)
            </label>
            <select
              value={preferences.sessionDuration}
              onChange={(e) =>
                setPreferences({
                  ...preferences,
                  sessionDuration: parseInt(e.target.value),
                })
              }
              className="w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value={15}>15 minutes</option>
              <option value={30}>30 minutes</option>
              <option value={45}>45 minutes</option>
              <option value={60}>1 hour</option>
              <option value={90}>1.5 hours</option>
            </select>
          </div>

          {/* Difficulty Level */}
          <DifficultySelector
            value={preferences.difficultyLevel}
            onChange={(level) =>
              setPreferences({ ...preferences, difficultyLevel: level })
            }
            label="Default Difficulty Level"
            className="bg-background-secondary rounded-lg p-4"
          />

          {/* Toggle Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">
                  Study Reminders
                </label>
                <p className="text-xs text-gray-500">
                  Get reminded to study regularly
                </p>
              </div>
              <button
                onClick={() =>
                  setPreferences({
                    ...preferences,
                    studyReminders: !preferences.studyReminders,
                  })
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  preferences.studyReminders ? "bg-primary-500" : "bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    preferences.studyReminders
                      ? "translate-x-6"
                      : "translate-x-1"
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300">
                  Auto-save
                </label>
                <p className="text-xs text-gray-500">
                  Automatically save your progress
                </p>
              </div>
              <button
                onClick={() =>
                  setPreferences({
                    ...preferences,
                    autoSave: !preferences.autoSave,
                  })
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  preferences.autoSave ? "bg-primary-500" : "bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    preferences.autoSave ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
        <div className="mt-6">
          <Button onClick={handleSavePreferences} isLoading={isLoading}>
            Save Preferences
          </Button>
        </div>
      </div>
    </div>
  );

  const renderNotificationsSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">
          Notification Settings
        </h3>
        <div className="space-y-4">
          {Object.entries(notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-300 capitalize">
                  {key.replace(/([A-Z])/g, " $1").trim()}
                </label>
                <p className="text-xs text-gray-500">
                  {key === "emailNotifications" &&
                    "Receive important updates via email"}
                  {key === "studyReminders" &&
                    "Get reminded when it's time to study"}
                  {key === "weeklyProgress" &&
                    "Weekly summary of your study progress"}
                  {key === "marketingEmails" && "Product updates and tips"}
                  {key === "achievementNotifications" &&
                    "Get notified when you unlock achievements"}
                  {key === "streakReminders" &&
                    "Reminders to maintain your study streak"}
                </p>
              </div>
              <button
                onClick={() =>
                  setNotifications({ ...notifications, [key]: !value })
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  value ? "bg-primary-500" : "bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    value ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
        <div className="mt-6">
          <Button onClick={handleSaveNotifications} isLoading={isLoading}>
            Save Notification Settings
          </Button>
        </div>
      </div>
    </div>
  );

  const renderSecuritySection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">
          Security Settings
        </h3>
        <div className="space-y-6">
          {/* Two-Factor Authentication */}
          <TwoFactorAuth
            enabled={securitySettings.twoFactorEnabled}
            onToggle={(enabled) =>
              setSecuritySettings({
                ...securitySettings,
                twoFactorEnabled: enabled,
              })
            }
          />

          {/* Session Settings */}
          <div className="bg-background-tertiary rounded-lg p-4 border border-border-primary">
            <h4 className="font-medium text-white mb-4">Session Management</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-300">
                    Login Notifications
                  </label>
                  <p className="text-xs text-gray-500">
                    Get notified when someone logs into your account
                  </p>
                </div>
                <button
                  onClick={() =>
                    setSecuritySettings({
                      ...securitySettings,
                      loginNotifications: !securitySettings.loginNotifications,
                    })
                  }
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    securitySettings.loginNotifications
                      ? "bg-primary-500"
                      : "bg-gray-600"
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      securitySettings.loginNotifications
                        ? "translate-x-6"
                        : "translate-x-1"
                    }`}
                  />
                </button>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Session Timeout (minutes)
                </label>
                <select
                  value={securitySettings.sessionTimeout}
                  onChange={(e) =>
                    setSecuritySettings({
                      ...securitySettings,
                      sessionTimeout: parseInt(e.target.value),
                    })
                  }
                  className="w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value={15}>15 minutes</option>
                  <option value={30}>30 minutes</option>
                  <option value={60}>1 hour</option>
                  <option value={120}>2 hours</option>
                  <option value={480}>8 hours</option>
                  <option value={1440}>1 day</option>
                  <option value={10080}>1 week</option>
                  <option value={20160}>2 weeks</option>
                  <option value={30240}>3 weeks</option>
                  <option value={40320}>4 weeks</option>
                  <option value={50400}>5 weeks</option>
                  <option value={60480}>6 weeks</option>
                  <option value={70560}>7 weeks</option>
                  <option value={80640}>8 weeks</option>
                  <option value={0}>Never expire</option>
                </select>
              </div>
            </div>
          </div>

          {/* Password Change */}
          <div className="bg-background-tertiary rounded-lg p-4 border border-border-primary">
            <div className="flex items-center space-x-3 mb-3">
              <HiKey className="w-5 h-5 text-primary-400" />
              <h4 className="font-medium text-white">Change Password</h4>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Update your password to keep your account secure
            </p>
            <Button
              variant="secondary"
              onClick={() => setIsChangePasswordModalOpen(true)}
            >
              Change Password
            </Button>
          </div>

          {/* Logout Section */}
          <div className="bg-background-tertiary rounded-lg p-4 border border-border-primary">
            <div className="flex items-center space-x-3 mb-3">
              <HiLogout className="w-5 h-5 text-orange-400" />
              <h4 className="font-medium text-white">Sign Out</h4>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Sign out of your account on this device
            </p>
            <Button
              variant="secondary"
              onClick={handleLogout}
              disabled={isLoading}
            >
              {isLoading ? "Signing Out..." : "Sign Out"}
            </Button>
          </div>

          {/* Account Deletion */}
          <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/30">
            <div className="flex items-center space-x-3 mb-3">
              <HiExclamationCircle className="w-5 h-5 text-red-400" />
              <h4 className="font-medium text-white">Danger Zone</h4>
            </div>
            <div className="space-y-4">
              <div>
                <h5 className="font-medium text-red-400 mb-2">
                  Account Deactivation
                </h5>
                <p className="text-gray-400 text-sm mb-3">
                  Temporarily deactivate your account. You can reactivate it
                  later.
                </p>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    setDangerZoneAction("deactivate");
                    setIsDangerZoneModalOpen(true);
                  }}
                >
                  Deactivate Account
                </Button>
              </div>

              <div>
                <h5 className="font-medium text-red-400 mb-2">
                  Account Deletion
                </h5>
                <p className="text-gray-400 text-sm mb-3">
                  Permanently delete your account and all associated data. This
                  action cannot be undone.
                </p>
                <Button
                  variant="danger"
                  size="sm"
                  onClick={() => {
                    setDangerZoneAction("delete");
                    setIsDangerZoneModalOpen(true);
                  }}
                >
                  Delete Account
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSubscriptionSection = () => <SubscriptionManagement />;

  const renderBillingSection = () => <EnhancedBilling />;

  const renderDataSection = () => <DataManagement />;

  const renderContent = () => {
    switch (activeSection) {
      case "profile":
        return renderProfileSection();
      case "preferences":
        return renderPreferencesSection();
      case "notifications":
        return renderNotificationsSection();
      case "security":
        return renderSecuritySection();
      case "subscription":
        return renderSubscriptionSection();
      case "billing":
        return renderBillingSection();
      case "data":
        return renderDataSection();
      default:
        return renderProfileSection();
    }
  };

  return (
    <div className="min-h-screen bg-background-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Settings</h1>
          <p className="text-gray-400">Manage your account and preferences</p>
        </div>

        {/* Error and Success Messages */}
        {error && (
          <div className="mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiExclamationCircle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">Error</span>
            </div>
            <p className="text-red-300 mt-1">{error}</p>
            <Button
              onClick={() => setError(null)}
              variant="secondary"
              size="sm"
              className="mt-2"
            >
              Dismiss
            </Button>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <HiCheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-medium">Success</span>
            </div>
            <p className="text-green-300 mt-1">{success}</p>
            <Button
              onClick={() => setSuccess(null)}
              variant="secondary"
              size="sm"
              className="mt-2"
            >
              Dismiss
            </Button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Settings Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
              <nav className="space-y-2">
                {settingsSections.map((section) => {
                  const Icon = section.icon;
                  const isActive = activeSection === section.id;

                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${
                          isActive
                            ? "bg-primary-500/20 text-primary-400 border border-primary-500/30"
                            : "text-gray-300 hover:bg-background-tertiary hover:text-white"
                        }
                      `}
                    >
                      <Icon className="w-5 h-5" />
                      <div className="flex-1 min-w-0">
                        <span className="font-medium block">
                          {section.label}
                        </span>
                        <span className="text-xs text-gray-500 block truncate">
                          {section.description}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Settings Content */}
          <div className="lg:col-span-3">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-background-secondary rounded-lg p-6 border border-border-primary"
            >
              {renderContent()}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <ChangePasswordModal
        isOpen={isChangePasswordModalOpen}
        onClose={() => setIsChangePasswordModalOpen(false)}
        onSuccess={() => {
          // Could show a success toast here
          console.log("Password changed successfully");
        }}
      />

      <DangerZoneModal
        isOpen={isDangerZoneModalOpen}
        onClose={() => setIsDangerZoneModalOpen(false)}
        action={dangerZoneAction}
      />
    </div>
  );
};
