"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[843],{2171:(e,t,s)=>{s.d(t,{q:()=>r});var a=s(8957);const r=(0,s(5914).vt)(e=>({documents:[],selectedDocuments:new Set,isLoading:!1,uploadProgress:{},fetchDocuments:async()=>{e({isLoading:!0});try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents",{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)throw new Error("Failed to fetch documents");const a=await s.json();if(!a.success)throw new Error(a.error);e({documents:a.data,isLoading:!1})}catch(t){throw console.error("Fetch documents error:",t),e({isLoading:!1}),t}},uploadDocument:async t=>{const s=new FormData;s.append("document",t);try{const r=localStorage.getItem("auth_token"),n=await fetch("/api/documents/upload",{method:"POST",headers:{Authorization:"Bearer ".concat(r)},body:s});if(!n.ok){const e=await n.json();throw new Error(e.error||"Upload failed")}const o=await n.json();if(o.success)return e(e=>({documents:[o.data,...e.documents],uploadProgress:(0,a.A)((0,a.A)({},e.uploadProgress),{},{[t.name]:100})})),o.data;throw new Error(o.error)}catch(r){throw console.error("Upload document error:",r),r}},deleteDocument:async t=>{try{const s=localStorage.getItem("auth_token"),a=await fetch("/api/documents/".concat(t),{method:"DELETE",headers:{Authorization:"Bearer ".concat(s)}});if(!a.ok){const e=await a.json();throw new Error(e.error||"Delete failed")}e(e=>({documents:e.documents.filter(e=>e.id!==t),selectedDocuments:new Set([...e.selectedDocuments].filter(e=>e!==t))}))}catch(s){throw console.error("Delete document error:",s),s}},searchDocuments:async e=>{try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents/search?q=".concat(encodeURIComponent(e)),{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)throw new Error("Search failed");const a=await s.json();return a.success?a.data:[]}catch(t){return console.error("Search documents error:",t),[]}},getDocument:async e=>{try{const t=localStorage.getItem("auth_token"),s=await fetch("/api/documents/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)return null;const a=await s.json();return a.success?a.data:null}catch(t){return console.error("Get document error:",t),null}},toggleDocumentSelection:t=>{e(e=>{const s=new Set(e.selectedDocuments);return s.has(t)?s.delete(t):s.add(t),{selectedDocuments:s}})},clearSelection:()=>{e({selectedDocuments:new Set})},selectAll:()=>{e(e=>({selectedDocuments:new Set(e.documents.map(e=>e.id))}))},setUploadProgress:(t,s)=>{e(e=>({uploadProgress:(0,a.A)((0,a.A)({},e.uploadProgress),{},{[t]:s})}))}}))},9843:(e,t,s)=>{s.r(t),s.d(t,{DocumentsPage:()=>f});var a=s(9643),r=s(8957),n=s(4471),o=s(2171),c=s(6507);const l=()=>{const[e,t]=(0,a.useState)(!1),[s,l]=(0,a.useState)([]),{uploadDocument:i,setUploadProgress:d}=(0,o.q)(),m=(0,a.useCallback)(async e=>{t(!0),l([]);const s=[];for(const t of e)try{if(t.size>52428800){s.push("".concat(t.name,": File size exceeds 50MB limit"));continue}if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.openxmlformats-officedocument.presentationml.presentation"].includes(t.type)){s.push("".concat(t.name,": Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files."));continue}d(t.name,0),await i(t),d(t.name,100)}catch(a){s.push("".concat(t.name,": ").concat(a instanceof Error?a.message:"Unknown error"))}l(s),t(!1)},[i,d]),{getRootProps:u,getInputProps:h,isDragActive:x}=(0,n.VB)({onDrop:m,accept:{"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},multiple:!0,maxFiles:10,disabled:e});return(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",(0,r.A)((0,r.A)({},u()),{},{className:"\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n          ".concat(x?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-primary-500 hover:bg-primary-500/5","\n          ").concat(e?"opacity-50 cursor-not-allowed":"","\n        "),children:[(0,c.jsx)("input",(0,r.A)({},h())),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,c.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),x?(0,c.jsx)("p",{className:"text-primary-400",children:"Drop the files here..."}):(0,c.jsxs)("div",{children:[(0,c.jsxs)("p",{className:"text-gray-300",children:["Drag & drop files here, or"," ",(0,c.jsx)("span",{className:"text-primary-500 font-medium",children:"browse"})]}),(0,c.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Supports PDF, DOCX, TXT, PPTX (max 50MB each)"})]})]})]})),e&&(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-4",children:[(0,c.jsx)("p",{className:"text-sm text-gray-300 mb-2",children:"Uploading files..."}),(0,c.jsx)("div",{className:"space-y-2"})]}),s.length>0&&(0,c.jsxs)("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4",children:[(0,c.jsx)("h4",{className:"text-red-400 font-medium mb-2",children:"Upload Errors:"}),(0,c.jsx)("ul",{className:"text-sm text-red-300 space-y-1",children:s.map((e,t)=>(0,c.jsxs)("li",{children:["\u2022 ",e]},t))})]})]})};var i=s(467),d=s(4859),m=s(4216),u=s(1721);const h=e=>{let{document:t}=e;const{selectedDocuments:s,toggleDocumentSelection:r,deleteDocument:n}=(0,o.q)(),{confirm:l,alert:i}=(0,u.s)(),[m,h]=(0,a.useState)(!1),x=s.has(t.id);return(0,c.jsxs)("div",{className:"\n        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer\n        ".concat(x?"border-primary-500 bg-primary-500/10":"border-gray-700 hover:border-gray-600","\n      "),onClick:()=>r(t.id),children:[(0,c.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[(0,c.jsx)("span",{className:"text-2xl",children:(g=t.file_type,{pdf:"\ud83d\udcc4",docx:"\ud83d\udcdd",txt:"\ud83d\udcc3",pptx:"\ud83d\udcca"}[g]||"\ud83d\udcc4")}),(0,c.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,c.jsx)("h3",{className:"text-white font-medium truncate",title:t.filename,children:t.filename}),(0,c.jsxs)("p",{className:"text-sm text-gray-400",children:[t.file_type.toUpperCase()," \u2022 ",(e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]})(t.file_size)]})]})]}),(0,c.jsx)("div",{className:"\n            w-5 h-5 rounded border-2 flex items-center justify-center\n            ".concat(x?"bg-primary-500 border-primary-500":"border-gray-500","\n          "),children:x&&(0,c.jsx)("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,c.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),!t.is_processed&&(0,c.jsx)("div",{className:"mb-3",children:(0,c.jsxs)("div",{className:"flex items-center space-x-2 text-yellow-400",children:[(0,c.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"}),(0,c.jsx)("span",{className:"text-sm",children:"Processing..."})]})}),t.processing_error&&(0,c.jsx)("div",{className:"mb-3",children:(0,c.jsxs)("div",{className:"text-red-400 text-sm",children:["\u26a0\ufe0f Processing failed: ",t.processing_error]})}),(0,c.jsxs)("div",{className:"text-xs text-gray-500 mb-3",children:["Uploaded ",(p=t.uploaded_at,new Date(p).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))]}),(0,c.jsx)("div",{className:"flex justify-end space-x-2",onClick:e=>e.stopPropagation(),children:(0,c.jsx)(d.$,{onClick:async()=>{if(await l({title:"Delete Document",message:'Are you sure you want to delete "'.concat(t.filename,'"? This action cannot be undone.'),confirmText:"Delete",cancelText:"Cancel",variant:"danger"})){h(!0);try{await n(t.id)}catch(e){console.error("Delete error:",e),await i({title:"Delete Error",message:"Failed to delete document. Please try again.",variant:"error"})}finally{h(!1)}}},variant:"danger",size:"sm",isLoading:m,children:"Delete"})})]});var p,g},x=(0,a.memo)(e=>{let{index:t,style:s,data:a}=e;const{documents:r}=a,n=r[t];return(0,c.jsx)("div",{style:s,children:(0,c.jsx)("div",{className:"px-2 py-1",children:(0,c.jsx)(h,{document:n})})})});x.displayName="DocumentItem";const p=(0,a.memo)(e=>{let{documents:t,height:s=400,itemHeight:r=120}=e;const n=(0,a.useMemo)(()=>({documents:t}),[t]);return 0===t.length?(0,c.jsxs)("div",{className:"text-center py-12",children:[(0,c.jsx)("div",{className:"text-gray-400 mb-4",children:"No documents uploaded yet"}),(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Upload your first document to get started with AI-powered study materials."})]}):t.length<=10?(0,c.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,c.jsx)(h,{document:e},e.id))}):(0,c.jsx)("div",{className:"border border-gray-600 rounded-lg overflow-hidden",children:(0,c.jsx)(m.Y1,{height:s,width:"100%",itemCount:t.length,itemSize:r,itemData:n,overscanCount:5,children:x})})});p.displayName="VirtualizedDocumentList";const g=(0,a.memo)(()=>{const{documents:e,selectedDocuments:t,isLoading:s,fetchDocuments:r,searchDocuments:n,clearSelection:l,selectAll:m,deleteDocument:h}=(0,o.q)(),{confirm:x,alert:g}=(0,u.s)(),[f,y]=(0,a.useState)(""),[j,w]=(0,a.useState)(null),[v,D]=(0,a.useState)(!1);(0,a.useEffect)(()=>{r()},[r]);const N=j||e,b=t.size>0;return s&&0===e.length?(0,c.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,c.jsx)("div",{className:"text-gray-400",children:"Loading documents..."})}):(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,c.jsx)("div",{className:"flex-1",children:(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)(i.p,{placeholder:"Search documents...",value:f,onChange:y}),(0,c.jsx)(d.$,{onClick:async()=>{if(f.trim().length<2)w(null);else{D(!0);try{const e=await n(f.trim());w(e)}catch(e){console.error("Search error:",e)}finally{D(!1)}}},isLoading:v,disabled:f.trim().length<2,children:"Search"}),j&&(0,c.jsx)(d.$,{onClick:()=>{y(""),w(null)},variant:"secondary",children:"Clear"})]})}),b&&(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)(d.$,{onClick:m,variant:"secondary",size:"sm",children:"Select All"}),(0,c.jsxs)(d.$,{onClick:l,variant:"secondary",size:"sm",children:["Clear (",t.size,")"]}),(0,c.jsx)(d.$,{onClick:async()=>{if(0===t.size)return;if(await x({title:"Delete Documents",message:"Are you sure you want to delete ".concat(t.size," document(s)? This action cannot be undone."),confirmText:"Delete",cancelText:"Cancel",variant:"danger"}))try{const e=Array.from(t).map(e=>h(e));await Promise.all(e),l()}catch(e){console.error("Bulk delete error:",e),await g({title:"Delete Error",message:"Some documents could not be deleted. Please try again.",variant:"error"})}},variant:"danger",size:"sm",children:"Delete Selected"})]})]}),j&&(0,c.jsxs)("div",{className:"text-sm text-gray-400",children:["Found ",j.length,' document(s) matching "',f,'"']}),(0,c.jsx)(p,{documents:N,height:600})]})}),f=()=>(0,c.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,c.jsxs)("div",{className:"space-y-8",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Documents"}),(0,c.jsx)("p",{className:"mt-2 text-gray-400",children:"Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX"})]}),(0,c.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6",children:[(0,c.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"Upload Documents"}),(0,c.jsx)(l,{})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Documents"}),(0,c.jsx)(g,{})]})]})})}}]);
//# sourceMappingURL=843.0acb241d.chunk.js.map