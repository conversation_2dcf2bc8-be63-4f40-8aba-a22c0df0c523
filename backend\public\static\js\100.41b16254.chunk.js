"use strict";(self.webpackChunkchewy_ai_frontend=self.webpackChunkchewy_ai_frontend||[]).push([[100],{6100:(e,s,t)=>{t.r(s),t.d(s,{CreditsPage:()=>y});var r=t(9643),a=t(8002),i=t(344);const n=(0,t(5914).vt)((e,s)=>({balance:0,transactions:[],operationCosts:[],stats:null,isLoading:!1,error:null,fetchBalance:async()=>{e({isLoading:!0,error:null});try{const s=await fetch("/api/credits/balance",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token"))}});if(!s.ok)throw new Error("Failed to fetch credit balance");const t=await s.json();if(!t.success)throw new Error(t.error||"Failed to fetch balance");e({balance:t.data.credits,isLoading:!1})}catch(s){e({error:s instanceof Error?s.message:"Unknown error",isLoading:!1})}},fetchTransactions:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;e({isLoading:!0,error:null});try{const a=await fetch("/api/credits/history?limit=".concat(t,"&offset=").concat(r),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token"))}});if(!a.ok)throw new Error("Failed to fetch credit history");const i=await a.json();if(!i.success)throw new Error(i.error||"Failed to fetch transactions");e({transactions:0===r?i.data:[...s().transactions,...i.data],isLoading:!1})}catch(a){e({error:a instanceof Error?a.message:"Unknown error",isLoading:!1})}},fetchOperationCosts:async()=>{e({isLoading:!0,error:null});try{const s=await fetch("/api/credits/pricing",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token"))}});if(!s.ok)throw new Error("Failed to fetch operation costs");const t=await s.json();if(!t.success)throw new Error(t.error||"Failed to fetch operation costs");e({operationCosts:t.data,isLoading:!1})}catch(s){e({error:s instanceof Error?s.message:"Unknown error",isLoading:!1})}},fetchStats:async function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30;e({isLoading:!0,error:null});try{const t=await fetch("/api/credits/stats?days=".concat(s),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token"))}});if(!t.ok)throw new Error("Failed to fetch credit stats");const r=await t.json();if(!r.success)throw new Error(r.error||"Failed to fetch stats");e({stats:r.data,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"Unknown error",isLoading:!1})}},purchaseCredits:async s=>{e({isLoading:!0,error:null});try{const t=await fetch("/api/credits/purchase",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token"))},body:JSON.stringify(s)});if(!t.ok)throw new Error("Failed to initiate credit purchase");const r=await t.json();if(r.success)return e({isLoading:!1}),{success:!0,clientSecret:r.data.clientSecret,paymentIntentId:r.data.paymentIntentId};throw new Error(r.error||"Failed to purchase credits")}catch(t){const s=t instanceof Error?t.message:"Unknown error";return e({error:s,isLoading:!1}),{success:!1,error:s}}},clearError:()=>e({error:null}),refreshAfterPurchase:async()=>{try{await Promise.all([s().fetchBalance(),s().fetchTransactions()])}catch(e){console.error("Failed to refresh data after purchase:",e)}}}));var c=t(2086),l=t(4859),d=t(6507);const o=e=>{let{balance:s,userTier:t,isLoading:r}=e;const n=e=>{switch(e.toLowerCase()){case"pro":return"text-purple-400";case"basic":return"text-blue-400";default:return"text-gray-400"}},c=(e=>e>=100?{color:"text-green-400",status:"Excellent"}:e>=50?{color:"text-yellow-400",status:"Good"}:e>=10?{color:"text-orange-400",status:"Low"}:{color:"text-red-400",status:"Critical"})(s);return(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"md:col-span-2 bg-gradient-to-br from-primary-500/20 to-purple-600/20 rounded-lg p-6 border border-primary-500/30",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:(0,d.jsx)(i.XtC,{className:"w-6 h-6 text-primary-400"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Credit Balance"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:"Available for AI generation"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 ".concat(n(t)),children:[(e=>{switch(e.toLowerCase()){case"pro":return(0,d.jsx)(i.pCw,{className:"w-5 h-5"});case"basic":return(0,d.jsx)(i.IG3,{className:"w-5 h-5"});default:return(0,d.jsx)(i.XtC,{className:"w-5 h-5"})}})(t),(0,d.jsxs)("span",{className:"font-medium",children:[t," Plan"]})]})]}),(0,d.jsxs)("div",{className:"flex items-end space-x-4",children:[(0,d.jsx)("div",{children:r?(0,d.jsx)("div",{className:"animate-pulse",children:(0,d.jsx)("div",{className:"h-12 w-32 bg-gray-600 rounded"})}):(0,d.jsxs)(a.P.div,{initial:{scale:.8},animate:{scale:1},transition:{duration:.3,delay:.2},children:[(0,d.jsx)("span",{className:"text-4xl font-bold text-white",children:s.toLocaleString()}),(0,d.jsx)("span",{className:"text-xl text-gray-400 ml-2",children:"credits"})]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-1 ".concat(c.color," mb-2"),children:[(0,d.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(c.color.replace("text-","bg-"))}),(0,d.jsx)("span",{className:"text-sm font-medium",children:c.status})]})]}),s<10&&(0,d.jsxs)(a.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.mqD,{className:"w-4 h-4 text-red-400"}),(0,d.jsx)("span",{className:"text-red-400 text-sm font-medium",children:"Low Balance Warning"})]}),(0,d.jsx)("p",{className:"text-red-300 text-sm mt-1",children:"Consider purchasing more credits to continue using AI features."})]})]}),(0,d.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,d.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Quick Stats"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-400 text-sm",children:"Plan Type"}),(0,d.jsx)("span",{className:"font-medium ".concat(n(t)),children:t})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-400 text-sm",children:"Status"}),(0,d.jsx)("span",{className:"font-medium ".concat(c.color),children:c.status})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-400 text-sm",children:"Credits Available"}),(0,d.jsx)("span",{className:"text-white font-medium",children:s})]}),"free"===t.toLowerCase()&&(0,d.jsx)("div",{className:"pt-3 border-t border-border-secondary",children:(0,d.jsx)("p",{className:"text-gray-400 text-xs",children:"Upgrade to Basic or Pro for more credits and features"})})]})]})]})},m=e=>{let{transactions:s,isLoading:t,onLoadMore:n}=e;const[c,o]=(0,r.useState)("all"),[m,x]=(0,r.useState)("date"),h=e=>e>0?"text-red-400":"text-green-400",u=s.filter(e=>"used"===c?e.credits_used>0:"purchased"!==c||e.credits_used<0),p=[...u].sort((e,s)=>"date"===m?new Date(s.created_at).getTime()-new Date(e.created_at).getTime():Math.abs(s.credits_used)-Math.abs(e.credits_used));return(0,d.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Transaction History"}),(0,d.jsxs)("p",{className:"text-gray-400 text-sm",children:[u.length," of ",s.length," transactions"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("select",{value:c,onChange:e=>o(e.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[(0,d.jsx)("option",{value:"all",children:"All Transactions"}),(0,d.jsx)("option",{value:"used",children:"Credits Used"}),(0,d.jsx)("option",{value:"purchased",children:"Credits Purchased"})]}),(0,d.jsx)(i.bv7,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("select",{value:m,onChange:e=>x(e.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[(0,d.jsx)("option",{value:"date",children:"Sort by Date"}),(0,d.jsx)("option",{value:"amount",children:"Sort by Amount"})]}),(0,d.jsx)(i.bv7,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),(0,d.jsxs)(l.$,{onClick:()=>{const e=[["Date","Type","Credits","Operation","Description"].join(","),...p.map(e=>[new Date(e.created_at).toLocaleDateString(),e.credits_used>0?"Used":"Purchased",Math.abs(e.credits_used),e.operation_type,'"'.concat(e.description,'"')].join(","))].join("\n"),s=new Blob([e],{type:"text/csv"}),t=window.URL.createObjectURL(s),r=document.createElement("a");r.href=t,r.download="credit-history-".concat((new Date).toISOString().split("T")[0],".csv"),r.click(),window.URL.revokeObjectURL(t)},variant:"secondary",size:"sm",disabled:0===s.length,children:[(0,d.jsx)(i.cIQ,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),(0,d.jsx)("div",{className:"space-y-3",children:t&&0===s.length?(0,d.jsx)("div",{className:"space-y-3",children:[...Array(5)].map((e,s)=>(0,d.jsx)("div",{className:"animate-pulse",children:(0,d.jsx)("div",{className:"bg-background-tertiary rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-600 rounded-full"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-600 rounded w-1/3 mb-2"}),(0,d.jsx)("div",{className:"h-3 bg-gray-600 rounded w-1/2"})]}),(0,d.jsx)("div",{className:"h-6 bg-gray-600 rounded w-16"})]})})},s))}):0===p.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(i.mqD,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),(0,d.jsx)("h4",{className:"text-lg font-medium text-gray-400 mb-2",children:"No Transactions Found"}),(0,d.jsx)("p",{className:"text-gray-500",children:"all"===c?"You haven't made any credit transactions yet.":"No ".concat(c," transactions found.")})]}):p.map((e,s)=>{const{date:t,time:r}=(e=>{const s=new Date(e);return{date:s.toLocaleDateString(),time:s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}})(e.created_at),n=e.credits_used<0;return(0,d.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*s},className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary hover:border-border-primary transition-colors",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"p-2 rounded-full ".concat(n?"bg-green-500/20":"bg-red-500/20"),children:(c=e.credits_used,c>0?(0,d.jsx)(i.GRP,{className:"w-4 h-4 text-red-400"}):(0,d.jsx)(i.s00,{className:"w-4 h-4 text-green-400"}))}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-white",children:e.operation_type.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())}),(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:e.description}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,d.jsx)("span",{className:"text-gray-500 text-xs",children:t}),(0,d.jsx)("span",{className:"text-gray-600",children:"\u2022"}),(0,d.jsx)("span",{className:"text-gray-500 text-xs",children:r})]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("span",{className:"font-semibold ".concat(h(e.credits_used)),children:[n?"+":"-",Math.abs(e.credits_used)," credits"]}),e.study_set_id&&(0,d.jsx)("p",{className:"text-gray-500 text-xs mt-1",children:"Study Set"})]})]})},e.id);var c})}),s.length>0&&s.length%50===0&&(0,d.jsx)("div",{className:"mt-6 text-center",children:(0,d.jsx)(l.$,{onClick:n,variant:"secondary",isLoading:t,disabled:t,children:"Load More Transactions"})})]})},x=[{id:"Free",name:"Free",price:0,credits:25,features:["25 credits per month","Basic AI generation","Document upload (up to 5MB)","Community support","Basic analytics"],stripePriceId:void 0},{id:"Study Starter",name:"Study Starter",price:29,credits:500,features:["500 credits per month","Advanced AI generation","Document upload (up to 10MB)","Email support","Detailed analytics","Export functionality"],stripePriceId:"price_starter_placeholder"},{id:"Study Pro",name:"Study Pro",price:59,credits:1200,features:["1,200 credits per month","Premium AI generation","Document upload (up to 25MB)","Priority support","Advanced analytics","Custom study sets","Collaboration features"],isPopular:!0,stripePriceId:"price_pro_placeholder"},{id:"Study Master",name:"Study Master",price:119,credits:2500,features:["2,500 credits per month","Premium AI generation","Document upload (up to 50MB)","Priority support","Advanced analytics","Custom study sets","Team collaboration","API access"],stripePriceId:"price_master_placeholder"},{id:"Study Elite",name:"Study Elite",price:239,credits:5500,features:["5,500 credits per month","Premium AI generation","Unlimited document upload","Dedicated support","Advanced analytics","Custom study sets","Team collaboration","API access","White-label options"],stripePriceId:"price_elite_placeholder"}],h=e=>{let{currentBalance:s,userTier:t,onPurchaseComplete:o}=e;const[m,h]=(0,r.useState)(null),[u,p]=(0,r.useState)(!1),[g,y]=(0,r.useState)(null),[b,j]=(0,r.useState)(null),[f,v]=(0,r.useState)(""),[N,w]=(0,r.useState)(!1),[k,C]=(0,r.useState)("subscriptions"),{refreshAfterPurchase:S}=n(),{user:P}=(0,c.A)(),L=e=>{switch(e){case"Study Starter":return(0,d.jsx)(i.dHv,{className:"w-6 h-6"});case"Study Pro":return(0,d.jsx)(i.K7t,{className:"w-6 h-6"});case"Study Master":return(0,d.jsx)(i.rqL,{className:"w-6 h-6"});case"Study Elite":return(0,d.jsx)(i.pCw,{className:"w-6 h-6"});default:return(0,d.jsx)(i.XtC,{className:"w-6 h-6"})}};return(0,d.jsxs)("div",{className:"space-y-6",children:[g&&(0,d.jsx)("div",{className:"bg-red-900/20 border border-red-500/50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,d.jsxs)("div",{className:"ml-3",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-red-400",children:"Error"}),(0,d.jsx)("div",{className:"mt-1 text-sm text-red-300",children:g})]}),(0,d.jsx)("div",{className:"ml-auto pl-3",children:(0,d.jsxs)("button",{onClick:()=>y(null),className:"inline-flex rounded-md bg-red-900/20 p-1.5 text-red-400 hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-900",children:[(0,d.jsx)("span",{className:"sr-only",children:"Dismiss"}),(0,d.jsx)("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})}),b&&(0,d.jsx)("div",{className:"bg-green-900/20 border border-green-500/50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,d.jsxs)("div",{className:"ml-3",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-green-400",children:"Success"}),(0,d.jsx)("div",{className:"mt-1 text-sm text-green-300",children:b})]}),(0,d.jsx)("div",{className:"ml-auto pl-3",children:(0,d.jsxs)("button",{onClick:()=>j(null),className:"inline-flex rounded-md bg-green-900/20 p-1.5 text-green-400 hover:bg-green-900/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-green-900",children:[(0,d.jsx)("span",{className:"sr-only",children:"Dismiss"}),(0,d.jsx)("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})}),(0,d.jsx)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Current Plan: ",t]}),(0,d.jsx)("p",{className:"text-gray-400",children:"Your available credits"})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("span",{className:"text-2xl font-bold text-primary-400",children:s}),(0,d.jsx)("span",{className:"text-gray-400 ml-2",children:"credits"})]})]})}),(0,d.jsxs)("div",{className:"flex space-x-1 bg-background-tertiary rounded-lg p-1",children:[(0,d.jsx)("button",{onClick:()=>C("subscriptions"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("subscriptions"===k?"bg-primary-500 text-white":"text-gray-400 hover:text-white"),children:"Monthly Plans"}),(0,d.jsx)("button",{onClick:()=>C("credits"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("credits"===k?"bg-primary-500 text-white":"text-gray-400 hover:text-white"),children:"One-Time Credits"})]}),"credits"===k&&(0,d.jsxs)("div",{className:"bg-background-secondary rounded-lg p-4 border border-border-primary mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h4",{className:"text-md font-medium text-white",children:"Have a discount code?"}),(0,d.jsx)("button",{onClick:()=>w(!N),className:"text-sm text-primary-400 hover:text-primary-300 transition-colors",children:N?"Hide":"Enter Code"})]}),N&&(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)("input",{type:"text",value:f,onChange:e=>v(e.target.value.toUpperCase()),placeholder:"Enter discount code",className:"flex-1 px-3 py-2 bg-background-tertiary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",maxLength:20}),(0,d.jsx)("button",{onClick:()=>{v(""),w(!1)},className:"px-3 py-2 text-gray-400 hover:text-white transition-colors",children:"Clear"})]})]}),"subscriptions"===k&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Choose Your Study Plan"}),(0,d.jsx)("p",{className:"text-gray-400 mb-6",children:"Monthly subscriptions with automatic credit refills. Cancel anytime."}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:x.filter(e=>"Free"!==e.id).map((e,s)=>{const r=m===e.id,n=u&&r,c=(b=e.id,t===b);var b;return(0,d.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*s},className:"\n                    relative bg-background-secondary rounded-lg p-6 border transition-all duration-200\n                    ".concat(e.isPopular?"border-primary-500 ring-2 ring-primary-500/20":"border-border-primary hover:border-border-secondary","\n                    ").concat(r?"ring-2 ring-primary-500/50":"","\n                    ").concat(c?"ring-2 ring-green-500/50":"","\n                  "),children:[e.isPopular&&(0,d.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:(0,d.jsx)("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:"Most Popular"})}),c&&(0,d.jsx)("div",{className:"absolute -top-3 right-4",children:(0,d.jsx)("div",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:"Current Plan"})}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"inline-flex p-3 rounded-lg mb-4 ".concat(e.isPopular?"bg-primary-500/20 text-primary-400":"bg-background-tertiary text-gray-400"),children:L(e.id)}),(0,d.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:e.name}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("span",{className:"text-3xl font-bold text-white",children:e.credits}),(0,d.jsx)("div",{className:"text-gray-400 text-sm",children:"credits/month"}),(0,d.jsxs)("div",{className:"text-green-400 text-xs",children:["~",5*e.credits," flashcards/quizzes"]})]}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("span",{className:"text-2xl font-bold text-white",children:["$",e.price]}),(0,d.jsx)("div",{className:"text-gray-400 text-sm",children:"/month"}),(0,d.jsxs)("div",{className:"text-gray-400 text-xs",children:["$",(e.price/e.credits).toFixed(3)," per credit"]})]}),(0,d.jsx)("div",{className:"space-y-2 mb-6",children:e.features.map((e,s)=>(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-300",children:[(0,d.jsx)(i.q9z,{className:"w-4 h-4 text-green-400 mr-2 flex-shrink-0"}),(0,d.jsx)("span",{children:e})]},s))}),(0,d.jsx)(l.$,{onClick:()=>(async e=>{const s=x.find(s=>s.id===e);if(s)if(null!==P&&void 0!==P&&P.email){p(!0),h(e),y(null),j(null);try{console.log("Creating subscription for tier:",s),j("Subscription to ".concat(s.name," will be implemented in the next phase. Price: $").concat(s.price,"/month")),await S(),o()}catch(g){console.error("Subscription error:",g),y("Failed to create subscription. Please try again.")}finally{p(!1),h(null)}}else y("User email not available. Please log out and log back in.");else y("Selected subscription tier not found. Please try again.")})(e.id),variant:e.isPopular?"primary":"secondary",className:"w-full",isLoading:n,disabled:u||!(null!==P&&void 0!==P&&P.email)||c,children:n?"Processing...":c?"Current Plan":null!==P&&void 0!==P&&P.email?"Subscribe to ".concat(e.name):"Login Required"})]})]},e.id)})})]}),"credits"===k&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"One-Time Credit Packages"}),(0,d.jsx)("p",{className:"text-gray-400 mb-6",children:"Purchase credits that never expire. Perfect for occasional use."}),(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(i.pCw,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"One-Time Packages Coming Soon"}),(0,d.jsx)("p",{className:"text-gray-600",children:"We're working on one-time credit packages. For now, please choose a subscription plan above."})]})]}),(0,d.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,d.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"subscriptions"===k?"Subscription Benefits":"One-Time Purchase Benefits"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-white mb-2",children:"Safe & Secure"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:"Student-safe payments through Stripe. Your payment info is never stored. Perfect for using your student card or parent's card with permission."})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-white mb-2",children:"subscriptions"===k?"Cancel Anytime":"Never Expire"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:"subscriptions"===k?"No long-term commitments. Cancel your subscription anytime and keep using credits until they run out.":"One-time credit purchases never expire - perfect for semester planning. Use them at your own pace!"})]})]})]})]})},u=e=>{let{stats:s,operationCosts:t}=e;const n=(0,r.useMemo)(()=>{const e=Array.from({length:7},(e,s)=>{const t=new Date;return t.setDate(t.getDate()-(6-s)),{date:t.toLocaleDateString("en-US",{weekday:"short"}),fullDate:t.toISOString().split("T")[0],credits:0}});return null!==s&&void 0!==s&&s.dailyUsage&&Array.isArray(s.dailyUsage)&&s.dailyUsage.forEach(s=>{const t=e.findIndex(e=>e.fullDate===s.date);-1!==t&&(e[t].credits=s.credits)}),e},[null===s||void 0===s?void 0:s.dailyUsage]),c=Math.max(...n.map(e=>e.credits),1),l=n.reduce((e,s)=>e+s.credits,0),o=l/7,m=n.slice(0,3).reduce((e,s)=>e+s.credits,0)/3,x=n.slice(4).reduce((e,s)=>e+s.credits,0)/3,h=x>m?"up":x<m?"down":"stable",u=m>0?Math.abs((x-m)/m*100):0;return(0,d.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Usage Trends"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:"Last 7 days"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:["up"===h?(0,d.jsx)(i.IG3,{className:"w-5 h-5 text-green-400"}):"down"===h?(0,d.jsx)(i.thT,{className:"w-5 h-5 text-red-400"}):null,"stable"!==h&&(0,d.jsxs)("span",{className:"text-sm font-medium ".concat("up"===h?"text-green-400":"text-red-400"),children:[u.toFixed(1),"%"]})]})]}),(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)("div",{className:"flex items-end justify-between h-32 space-x-2",children:n.map((e,s)=>{const t=c>0?e.credits/c*100:0;return(0,d.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-full flex justify-center mb-2",children:(0,d.jsx)(a.P.div,{initial:{height:0},animate:{height:"".concat(t,"%")},transition:{duration:.5,delay:.1*s},className:"w-full max-w-8 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-sm relative group",style:{minHeight:t>0?"4px":"0px"},children:(0,d.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,d.jsxs)("div",{className:"bg-background-tertiary border border-border-primary rounded px-2 py-1 text-xs text-white whitespace-nowrap",children:[e.credits," credits"]})})})}),(0,d.jsx)("span",{className:"text-xs text-gray-400",children:e.date})]},e.date)})})}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsx)("div",{className:"bg-background-tertiary rounded-lg p-4",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-white",children:l}),(0,d.jsx)("div",{className:"text-gray-400 text-sm",children:"Total This Week"})]})}),(0,d.jsx)("div",{className:"bg-background-tertiary rounded-lg p-4",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-white",children:o.toFixed(1)}),(0,d.jsx)("div",{className:"text-gray-400 text-sm",children:"Daily Average"})]})})]}),(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-border-secondary",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-white mb-3",children:"Usage Efficiency"}),(0,d.jsx)("div",{className:"space-y-2",children:null!==s&&void 0!==s&&s.usageByOperation?Object.entries(s.usageByOperation).slice(0,3).map(e=>{let[s,r]=e;const a=null===t||void 0===t?void 0:t.find(e=>e.operation_type===s),i=a?r*a.operations_per_credit:0;return(0,d.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,d.jsx)("span",{className:"text-gray-300 capitalize",children:s.replace(/_/g," ")}),(0,d.jsxs)("span",{className:"text-white",children:[i," generations"]})]},s)}):(0,d.jsx)("div",{className:"text-gray-400 text-sm text-center py-2",children:"No usage data available"})})]})]})},p=[{id:"overview",label:"Overview",icon:i.Lyu,description:"Credit balance and usage summary"},{id:"history",label:"Transaction History",icon:i.mqD,description:"Detailed credit transaction log"},{id:"purchase",label:"Buy Credits",icon:i.DAO,description:"Purchase additional credits"}],g=e=>{switch(e){case"quiz_generation":return"quiz questions";case"flashcard_generation":return"flashcards";case"additional_content":return"flex generations";default:return e.replace(/_/g," ").replace("generation","").trim()+"s"}},y=()=>{const[e,s]=(0,r.useState)("overview"),{user:t}=(0,c.n)(),{balance:x,transactions:y,operationCosts:b,stats:j,isLoading:f,error:v,fetchBalance:N,fetchTransactions:w,fetchOperationCosts:k,fetchStats:C,clearError:S}=n();(0,r.useEffect)(()=>{N(),w(),k(),C()},[N,w,k,C]);const P=async()=>{S(),await Promise.all([N(),w(),k(),C()])},L=()=>(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(o,{balance:x,userTier:(null===t||void 0===t?void 0:t.subscription_tier)||"Free",isLoading:f}),j&&(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsx)(u,{stats:j,operationCosts:b}),(0,d.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Usage Breakdown"}),(0,d.jsx)("div",{className:"space-y-3",children:null!==j&&void 0!==j&&j.usageByOperation?Object.entries(j.usageByOperation).map(e=>{let[s,t]=e;return(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-300 capitalize",children:s.replace(/_/g," ")}),(0,d.jsxs)("span",{className:"text-white font-medium",children:[t," credits"]})]},s)}):(0,d.jsx)("div",{className:"text-gray-400 text-center py-4",children:"No usage data available"})})]})]}),(0,d.jsxs)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Credit Costs"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:(null===b||void 0===b?void 0:b.length)>0?b.map(e=>(0,d.jsxs)("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary",children:[(0,d.jsx)("h4",{className:"font-medium text-white capitalize mb-2",children:e.operation_type.replace(/_/g," ")}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.XtC,{className:"w-4 h-4 text-primary-400"}),(0,d.jsxs)("span",{className:"text-primary-400 font-semibold",children:["1 credit = ",e.operations_per_credit," ",g(e.operation_type)]})]})]},e.operation_type)):(0,d.jsx)("div",{className:"col-span-full text-gray-400 text-center py-8",children:"No operation cost data available"})})]})]});return(0,d.jsx)("div",{className:"min-h-screen bg-background-primary text-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Credits"}),(0,d.jsx)("p",{className:"text-gray-400",children:"Manage your AI generation credits"})]}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)(l.$,{onClick:P,variant:"secondary",disabled:f,children:[(0,d.jsx)(i.pMz,{className:"w-4 h-4 mr-2 ".concat(f?"animate-spin":"")}),"Refresh"]})})]}),v&&(0,d.jsxs)("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.TMu,{className:"w-5 h-5 text-red-400"}),(0,d.jsx)("span",{className:"text-red-400 font-medium",children:"Error"})]}),(0,d.jsx)("p",{className:"text-red-300 mt-1",children:v}),(0,d.jsx)(l.$,{onClick:S,variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:(0,d.jsx)("nav",{className:"space-y-2",children:p.map(t=>{const r=t.icon,a=e===t.id;return(0,d.jsxs)("button",{onClick:()=>s(t.id),className:"\n                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left\n                        transition-all duration-200\n                        ".concat(a?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white","\n                      "),children:[(0,d.jsx)(r,{className:"w-5 h-5"}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("span",{className:"font-medium block",children:t.label}),(0,d.jsx)("span",{className:"text-xs text-gray-500 block truncate",children:t.description})]})]},t.id)})})})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsx)(a.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:(()=>{switch(e){case"overview":default:return L();case"history":return(0,d.jsx)(m,{transactions:y,isLoading:f,onLoadMore:()=>w(50,y.length)});case"purchase":return(0,d.jsx)(h,{currentBalance:x,userTier:(null===t||void 0===t?void 0:t.subscription_tier)||"Study Starter",onPurchaseComplete:P})}})()},e)})]})]})})}}}]);
//# sourceMappingURL=100.41b16254.chunk.js.map